package com.altomni.apn.voip.service.openai.impl;


import com.altomni.apn.voip.service.dto.openai.TranscriptionSummaryDTO;
import com.altomni.apn.voip.service.vo.openai.TranscriptionNoteVO;
import com.altomni.apn.voip.service.openai.OpenAIService;
import com.altomni.apn.common.service.voipserver.VoipProxyAPIHttp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class OpenAIServiceImpl implements OpenAIService {

    private static final String TRANSCRIPTION_SUMMARY_POST = "/api/v1/connect/live-transcription/summary";

    @Resource
    private VoipProxyAPIHttp voipProxyAPIHttp;

    @Override
    public ResponseEntity<TranscriptionNoteVO> getLiveTranscriptionSummary(TranscriptionSummaryDTO request) {
        return voipProxyAPIHttp.proxyApiRequest(TRANSCRIPTION_SUMMARY_POST, request, HttpMethod.POST, TranscriptionNoteVO.class);
    }

}
