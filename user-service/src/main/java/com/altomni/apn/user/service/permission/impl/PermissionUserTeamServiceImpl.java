package com.altomni.apn.user.service.permission.impl;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.folder.TeamUserSetRollList;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.permission.PermissionTeam;
import com.altomni.apn.user.domain.permission.PermissionTeamLeader;
import com.altomni.apn.user.domain.permission.PermissionTeamLeaderProfit;
import com.altomni.apn.user.domain.permission.PermissionUserTeam;
import com.altomni.apn.user.repository.permission.PermissionTeamLeaderProfitRepository;
import com.altomni.apn.user.repository.permission.PermissionTeamLeaderRepository;
import com.altomni.apn.user.repository.permission.PermissionTeamRepository;
import com.altomni.apn.user.repository.permission.PermissionUserTeamRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.cache.CachePermissionWriteOnly;
import com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO;
import com.altomni.apn.user.service.job.JobService;
import com.altomni.apn.user.service.permission.PermissionUserTeamService;
import com.altomni.apn.user.service.talent.TalentService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamJobAndUserCountVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamUsersTransferPrimaryVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamUsersTransferSecondaryVM;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class PermissionUserTeamServiceImpl implements PermissionUserTeamService {

    @Resource
    private PermissionUserTeamRepository userTeamRepository;

    @Resource
    private PermissionTeamLeaderRepository permissionTeamLeaderRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private PermissionTeamRepository permissionTeamRepository;

    @Resource
    private PermissionUserTeamRepository permissionUserTeamRepository;

    @Resource
    private CachePermissionWriteOnly cachePermissionWriteOnly;

    @Resource
    private JobService jobService;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    PermissionTeamLeaderProfitRepository permissionTeamLeaderProfitRepository;

    @Override
    public void addUsersToTeam(Set<Long> userIds, Long teamId) {
        this.addUsersToTeam(userIds, teamId, Boolean.FALSE);
    }

    private void addUsersToTeam(Set<Long> userIds, Long teamId, Boolean isPrimary) {
        userIds = this.checkAndRemoveExistsUsers(userIds, teamId);
        userTeamRepository.saveAll(userIds.stream().map(userId -> {
            PermissionUserTeam permissionUserTeam = new PermissionUserTeam();
            permissionUserTeam.setUserId(userId);
            permissionUserTeam.setTeamId(teamId);
            permissionUserTeam.setIsPrimary(isPrimary);
            permissionUserTeam.setId(null);
            cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(userId);
            cachePermissionWriteOnly.deleteClientContactDataPermissionCacheByUserId(userId);
            cachePermissionWriteOnly.deleteReportDataPermissionCacheByUserId(userId);
            commonRedisService.delete(LoginUtil.USER_ACCOUNT_REDIS_EXPIRE_PREFIX + userId);

            return permissionUserTeam;
        }).collect(Collectors.toSet()));
    }

    private Set<Long> checkAndRemoveExistsUsers(Set<Long> userIds, Long teamId){
        Set<Long> existsUserIds = userTeamRepository.findUserIdsByTeamId(teamId);
        if (CollectionUtils.isNotEmpty(existsUserIds)){
            userIds.removeAll(existsUserIds);
        }
        return userIds;
    }

    @Override
    public void removeUsersFromTeam(Set<Long> userIds, Long teamId) {
        this.removeUsersFromTeam(userIds, teamId, Boolean.FALSE);
    }

    private void removeUsersFromTeam(Set<Long> userIds, Long teamId, Boolean checkPrimaryTeam){
        if (BooleanUtils.isTrue(checkPrimaryTeam)){
            List<String> usersWithPrimaryTeam = userTeamRepository.findUsersByTeamIdAndUserIdInAndIsPrimaryTeam(teamId, userIds);
            if (CollectionUtils.isNotEmpty(usersWithPrimaryTeam)){
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONUSERTEAM_REMOVEUSERSFROMTEAM_NOTREMOVED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
            }
        }
        userTeamRepository.removeUsersFromTeam(userIds, teamId);
        permissionTeamLeaderRepository.deleteAllByTeamIdAndUserIdIn(teamId, userIds);
        userIds.forEach(userId -> {
            cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(userId);
            cachePermissionWriteOnly.deleteClientContactDataPermissionCacheByUserId(userId);
            cachePermissionWriteOnly.deleteReportDataPermissionCacheByUserId(userId);
            commonRedisService.delete(LoginUtil.USER_ACCOUNT_REDIS_EXPIRE_PREFIX + userId);
        });

    }

    @Resource
    private TalentService talentService;

    @Override
    public void primaryTeamUsersTransfer(PermissionTeamUsersTransferPrimaryVM transferVM) {
        this.checkoutExistUserTeam(transferVM.getUserId(), transferVM.getToTeamId());
        PermissionUserTeam prePrimaryTeam = userTeamRepository.findFirstByUserIdAndIsPrimary(transferVM.getUserId(), Boolean.TRUE).orElseThrow(()-> new CustomParameterizedException("Invalid primary team!"));
        Long fromTeamId = prePrimaryTeam.getTeamId();
        prePrimaryTeam.setTeamId(transferVM.getToTeamId());
        userTeamRepository.save(prePrimaryTeam);
        // 更换团队时，清空用户作为Leader的信息
        //todo 短暂兼容一下历史逻辑
        if (ObjectUtil.isNotEmpty(transferVM.getFromTeamId())) {
            permissionTeamLeaderRepository.deleteAllByTeamIdAndUserIdIn(transferVM.getFromTeamId(), Set.of(transferVM.getUserId()));
        } else {
            permissionTeamLeaderRepository.deleteAllByUserId(transferVM.getUserId());
        }
        cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(transferVM.getUserId());
        cachePermissionWriteOnly.deleteClientContactDataPermissionCacheByUserId(transferVM.getUserId());
        cachePermissionWriteOnly.deleteReportDataPermissionCacheByUserId(transferVM.getUserId());
        commonRedisService.delete(LoginUtil.USER_ACCOUNT_REDIS_EXPIRE_PREFIX + prePrimaryTeam.getUserId());

        talentService.teamAddUserSetRollList(new TeamUserSetRollList(List.of(transferVM.getUserId()), transferVM.getToTeamId(), transferVM.getToTeamId()));
        talentService.teamRemoveUserSetRollList(new TeamUserSetRollList(List.of(transferVM.getUserId()), fromTeamId, transferVM.getToTeamId()));
    }

    @Override
    public void secondaryTeamUsersTransfer(PermissionTeamUsersTransferSecondaryVM transferVM) {
        transferVM.getUserIds().forEach(userId -> {
            transferVM.getToTeamIds().forEach(teamId -> {
                this.checkoutExistUserTeam(userId, teamId);
            });
        });
        this.removeUsersFromTeam(transferVM.getUserIds(), transferVM.getFromTeamId());
        transferVM.getToTeamIds().forEach(toTeamId -> this.addUsersToTeam(new HashSet<>(transferVM.getUserIds()), toTeamId));
    }

    private void checkoutExistUserTeam(Long userId, Long teamId){
        if (userTeamRepository.existsByUserIdAndAndTeamId(userId, teamId)) {
            User user = userRepository.findById(userId).orElseThrow();
            PermissionTeam permissionTeam = permissionTeamRepository.findById(teamId).orElseThrow();
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONUSERTEAM_CHECKOUTEXISTUSERTEAM_USEREXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(user.getFirstName(), user.getLastName(), permissionTeam.getName()),userApiPromptProperties.getUserService()));
        }
    }

    @Override
    public PermissionUserTeam findPrimaryTeamByUserId(Long userId) {
        return userTeamRepository.findFirstByUserIdAndIsPrimary(userId, Boolean.TRUE).orElse(new PermissionUserTeam());
    }

    @Override
    public PermissionTeamJobAndUserCountVM searchJobAndUserByTeamId(Long teamId) {
        Integer jobCount = jobService.searchJobsCountByPTeamId(teamId).getBody();
        Integer userCount = userTeamRepository.countByTeamId(teamId);
        return new PermissionTeamJobAndUserCountVM(jobCount, userCount);
    }

    @Override
    public Long getSuperiorLeader(Long userId) {
        User user = userRepository.findById(userId).orElseThrow(() -> new CustomParameterizedException("user not found"));
        Long tenantAdminUserId = userRepository.findByRoleNameInAndTenant(List.of(AuthoritiesConstants.TENANT_ADMIN), user.getTenantId())
                .stream().findAny().map(User::getId).orElse(null);
        Optional<PermissionUserTeam> primaryTeamOpt = permissionUserTeamRepository.findFirstByUserIdAndIsPrimary(userId, Boolean.TRUE);
        if (primaryTeamOpt.isEmpty()) {
            return tenantAdminUserId;
        }
        // 如果 team leader 只有一个，直接返回
        PermissionTeam team = permissionTeamRepository.findById(primaryTeamOpt.get().getTeamId()).orElseThrow(() -> new CustomParameterizedException("team not found"));
        List<PermissionTeamLeader> teamLeaders = permissionTeamLeaderRepository.findAllByTeamId(team.getId());
        if (teamLeaders.size() == 1) {
            return teamLeaders.get(0).getUserId();
        }
        // 如果 team leader 有多个，则需要找到其上级 team 的 leader, 直到找到 team 的 parentId 为 -1, 就返回租户管理员
        return findSuperiorTeamLeader(team, tenantAdminUserId);
    }

    /**
     * 查找用户所在团队和所有上级团队的leader
     * @param userId
     * @return
     */
    @Override
    public Set<Long> getAllUpperTeamLeaders(Long userId) {
        Optional<PermissionUserTeam> primaryTeamOpt = permissionUserTeamRepository.findFirstByUserIdAndIsPrimary(userId, Boolean.TRUE);
        if (primaryTeamOpt.isEmpty()) {
            return Set.of();
        }
        HashSet<Long> teamLeaders = new HashSet<>();
        PermissionTeam team = permissionTeamRepository.findById(primaryTeamOpt.get().getTeamId()).orElseThrow(() -> new CustomParameterizedException("team not found"));
        findAllUpperTeamLeaders(team, teamLeaders);
        return teamLeaders;
    }

    private void findAllUpperTeamLeaders(PermissionTeam team, Set<Long> teamLeaders) {
       if (team == null) {
          return;
       }

       // 查找团队的leader
       List<PermissionTeamLeader> parentTeamLeaders = permissionTeamLeaderRepository.findAllByTeamId(team.getId());
       parentTeamLeaders.forEach(tl -> teamLeaders.add(tl.getUserId()));

       if (team.getParentId() == null || team.getParentId().equals(-1L)) {
           return;
       }

       // 查找上级团队
       PermissionTeam parentTeam = permissionTeamRepository.findById(team.getParentId())
                       .orElse(null);

       findAllUpperTeamLeaders(parentTeam, teamLeaders);
    }

    /**
     * 递归查找上级团队的leader
     *  talent/客户联系人owner离职进行权限移交
     *  - 若为组织架构-团队下成员，默认变更为 所在团队的 team leader
     *  - 若为组织架构-团队team leader, 默认变更为 上级团队的team leader，以此类推，都无，给第一个admin
     *  - 若当前节点团队无team leader, 变更为上一级team节点的team leader, 以此类推，都无，给第一个admin
     *  - 若当前节点团队 team leader 存在多个，变更为上一级team节点的team leader，以此类推，都无，给第一个admin
     *
     * @param team              当前团队
     * @param tenantAdminUserId
     * @return 上级leaderID
     */
    private Long findSuperiorTeamLeader(PermissionTeam team, Long tenantAdminUserId) {
        // 如果没有上级团队（parentId 为 -1 或 null），返回租户管理员
        if (team.getParentId() == null || team.getParentId().equals(-1L)) {
            return tenantAdminUserId;
        }

        // 查找上级团队
        PermissionTeam parentTeam = permissionTeamRepository.findById(team.getParentId())
                .orElseThrow(() -> new CustomParameterizedException("parent team not found"));

        // 查找上级团队的leader
        List<PermissionTeamLeader> parentTeamLeaders = permissionTeamLeaderRepository.findAllByTeamId(parentTeam.getId());

        // 如果上级团队只有一个leader，直接返回
        if (parentTeamLeaders.size() == 1) {
            return parentTeamLeaders.get(0).getUserId();
        }

        // 如果上级团队有多个leader，继续递归查找
        return findSuperiorTeamLeader(parentTeam, tenantAdminUserId);
    }

    @Override
    public Long getLevel1TeamIdByUserId(Long userId) {
        Optional<PermissionUserTeam> primaryTeamOpt = permissionUserTeamRepository.findFirstByUserIdAndIsPrimary(userId, Boolean.TRUE);
        return primaryTeamOpt.map(permissionUserTeam -> findUpperTeamId(permissionUserTeam.getTeamId())).orElse(null);

    }

    private Long findUpperTeamId(Long teamId) {

        // 查找上级团队
        Optional<PermissionTeam> teamOptional = permissionTeamRepository.findById(teamId);

        if (!teamOptional.isPresent()) {
            log.error("[findUpperTeamId] team not found: {}", teamId);
            return null;
        }

        PermissionTeam team = teamOptional.get();

        if (team.getLevel() == 0){
            return teamId;
        }

        if (team.getLevel() == 1) {
            return teamId;
        }

        return findUpperTeamId(team.getParentId());
    }

    @Override
    public Long getLevel1TeamProfitLeaderByUserId(Long userId) {
        Long teamId = getLevel1TeamIdByUserId(userId);
        if (teamId == null) {
            return null;
        } else {
            return permissionTeamLeaderProfitRepository.findByTeamId(teamId)
                    .map(PermissionTeamLeaderProfit::getUserId)
                    .orElse(null);
        }
    }

    @Override
    public List<PermissionTeam> getAllChildTeamsByParentTeamId(List<Long> parentTeamIds) {
        return permissionTeamRepository.getChildTeams(parentTeamIds);
    }

    @Override
    public List<Long> getUserIdFilterByTeamCategory(List<Long> userIds) {
        return permissionTeamRepository.getUserIdFilterByTeamCategory(userIds);
    }

    @Override
    public Set<Long> getAllActiveUserIdsIncludingChildTeamUsersByTeamIds(List<Long> parentTeamIds) {

        List<PermissionTeam> teams = this.getAllChildTeamsByParentTeamId(parentTeamIds);
        Set<Long> allTeamIds = teams.stream().map(PermissionTeam::getId).collect(Collectors.toSet());
        List<PermissionTeamUserDTO> permissionTeamUserDTOS = userRepository.findAllActiveStatusTeamUsersByPermissionTeamIdIn(allTeamIds);

        return permissionTeamUserDTOS.stream().map(PermissionTeamUserDTO::getId).collect(Collectors.toSet());
    }

}
