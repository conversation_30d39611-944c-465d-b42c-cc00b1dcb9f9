package com.altomni.apn.user.repository.permission;

import com.altomni.apn.user.domain.permission.PermissionUserTeam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;


/**
 * Spring Data JPA repository for the JobNote entity.
 */
@SuppressWarnings("unused")
@Repository
public interface PermissionUserTeamRepository extends JpaRepository<PermissionUserTeam,Long> {

    List<PermissionUserTeam> findAllByUserId(Long userId);

    List<PermissionUserTeam> findAllByTeamId(Long teamId);

    @Query(value = "select concat(u.first_name, ' ', u.last_name) from permission_user_team ut" +
            " left join user u on u.id = ut.user_id" +
            " where ut.team_id =:teamId and ut.user_id in :userIds and ut.is_primary=1", nativeQuery = true)
    List<String> findUsersByTeamIdAndUserIdInAndIsPrimaryTeam(@Param("teamId") Long teamId, @Param("userIds") Set<Long> userIds);

    @Modifying
    @Query("delete from PermissionUserTeam where userId in :userIds and teamId=:teamId")
    void removeUsersFromTeam(@Param("userIds") Set<Long> userIds, @Param("teamId") Long teamId);

    Integer countByTeamId(Long teamId);

    @Query("SELECT COUNT(DISTINCT ut.userId) FROM PermissionUserTeam ut" +
            " INNER JOIN User u ON u.id=ut.userId" +
            " WHERE ut.teamId in :teamIds AND u.activated=true")
    Integer countByTeamIdIn(@Param("teamIds") Set<Long> teamIds);

    @Query(value = "select ud.user_id from permission_user_team ud" +
            " where ud.team_id =:teamId", nativeQuery = true)
    Set<Long> findUserIdsByTeamId(@Param("teamId") Long teamId);

    boolean existsByUserIdAndAndTeamId(Long userId, Long teamId);

    Optional<PermissionUserTeam> findFirstByUserIdAndTeamId(Long userId, Long teamId);

    Optional<PermissionUserTeam> findFirstByUserIdAndIsPrimary(Long userId, Boolean isPrimary);

    void deleteByTeamId(Long teamId);

    void deleteByUserId(Long userId);

    void deleteByUserIdAndTeamId(Long userId, Long teamId);

    @Query(value = "SELECT p.user_id FROM permission_user_team p LEFT JOIN `user` u ON p.user_id = u.id WHERE team_id = ?1 AND u.last_name IS NOT NULL", nativeQuery = true)
    Set<Long> findAllUserIdByTeamId(Long teamId);

    @Query(value = "select ut.team_id, t.name, ut.is_primary " +
            "             from permission_user_team ut " +
            "             inner join permission_team t on ut.team_id = t.id " +
            "             where  ut.user_id = ?1 ", nativeQuery = true)
    List<Object[]> findTeamsByUserId(Long userId);

    @Query(value = "select tl.user_id from permission_user_team ut " +
            "left join permission_team_leader tl on tl.team_id=ut.team_id " +
            "where ut.user_id=:userId and ut.is_primary=1", nativeQuery = true)
    Set<Long> findCurrentTeamLeaderId(@Param("userId") Long userId);

    List<PermissionUserTeam> findAllByTeamIdIn(Set<Long> teamIds);

}
