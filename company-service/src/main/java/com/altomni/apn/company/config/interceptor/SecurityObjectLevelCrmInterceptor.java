package com.altomni.apn.company.config.interceptor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.auth.LoginInformation;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.config.env.CommonApplicationProperties;
import com.altomni.apn.common.dto.LoginUserDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.config.env.CrmForwardUrlProperties;
import com.ipg.resourceserver.client.ClientTokenHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.DispatcherType;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.util.UriComponentsBuilder;
import org.zalando.problem.Status;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;

import static com.altomni.apn.common.constants.AuthConstants.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SecurityObjectLevelCrmInterceptor implements HandlerInterceptor {

    public static final String APN_INTERNAL_PIN = "APN_INTERNAL_PIN";

    @Value("${application.crmUrl}")
    private String crmUrl;

    @Resource
    private CachePermission cachePermission;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private CrmForwardUrlProperties crmForwardUrlProperties;

    @Resource
    private CommonApplicationProperties commonApplicationProperties;

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    private final RestTemplate restTemplate = new RestTemplate();


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {
        if (DispatcherType.REQUEST.name().equals(request.getDispatcherType().name())) {
            response.addHeader("responseTime", String.valueOf(System.currentTimeMillis()));
            // 1. GET /user/api/v3/tenant/4/user/100
            // 2. GET /user/api/v3/tenant/{tenantId}/user/{userId}
            // 3. GET /user/api/v3/tenant/{}/user/{}
            // 4. check permission
            // ******** 1. Output the original API path*********
            log.debug("requestURI: {} {}", request.getMethod(), request.getRequestURI());

            //crm url 的转发逻辑
            if (crmForwardUrlProperties != null && CollUtil.isNotEmpty(crmForwardUrlProperties.getUrlMap()) && crmForwardUrlProperties.getUrlMap().containsKey(request.getMethod())) {
                String requestURI = request.getRequestURI();
                //移除统一的前缀,后续的一致
                String url = requestURI.replaceAll("/company/crm", "");
                RequestMethod requestMethod = RequestMethod.valueOf(request.getMethod());
                String matchedPattern = getMatchedPattern(url, requestMethod, crmForwardUrlProperties.getUrlMap());
                //通过 dispatcherType 是否匹配上来判断转发
                if (matchedPattern != null) {
                    Map<String, String> uriVariables = antPathMatcher.extractUriTemplateVariables(matchedPattern, url);
                    String targetUrl = buildTargetUrl(url, uriVariables);
                    // Forward the request
                    forwardRequest(targetUrl, request, response);
                    return true;
                }
            }

            log.debug("enableObjectLevelSecurity: {}", commonApplicationProperties.isEnableObjectLevelSecurity());

            // ******** 2. Get formatted API path *********
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            String classRequestMapping = "";
            if (method.getDeclaringClass().isAnnotationPresent(RequestMapping.class)){
                classRequestMapping = method.getDeclaringClass().getAnnotation(RequestMapping.class).value()[0];
            }
            RequestMapping methodAnnotation = handlerMethod.getMethodAnnotation(RequestMapping.class);
            String methodRequestMapping = methodAnnotation.value()[0];
            // /user/api/v3/tenant/{tenantId}/user/{userId}
            String requestURI = request.getMethod() + commonApplicationProperties.getContextPath() + classRequestMapping + methodRequestMapping;

            // ********* 3. Uniform API path ***********
            // /user/api/v3/tenant/{}/user/{}
            String uniformURI = requestURI.replaceAll("[{].*?[}]", "{}");
            response.addHeader("URI", uniformURI);

            // if the request is from internal service, skip checking api permission
            final String internalServicePin = request.getHeader(APN_INTERNAL_PIN);
            if (StringUtils.hasText(internalServicePin) && StringUtils.hasText(commonApplicationProperties.getApnInternalPin()) && internalServicePin.equals(commonApplicationProperties.getApnInternalPin())){
                return Boolean.TRUE;
            }

//            final LoginUserDTO loginUserDTO = SecurityUtils.getCurrentUserLogin().orElse(new LoginUserDTO().setId(-1L));
            final LoginInformation loginUserDTO = SecurityUtils.getCurrentUserLogin().orElse(new LoginUserDTO().setId(-1L));
            final Long userId = loginUserDTO.getId();
            this.countApi(loginUserDTO);

            if (Boolean.FALSE.equals(commonApplicationProperties.isEnableObjectLevelSecurity())){
                return Boolean.TRUE;
            }

            // check public APIs
            if (cachePermission.isPublicApi(uniformURI)){
                return true;
            }

            cachePermission.checkImpersonationLoginPermission(loginUserDTO.getId(), uniformURI,request.getMethod());

            // if the request is from jobdiva service, skip checking api permission
            if (commonApplicationProperties.getContextPath().equals("/jobdiva")
                    || commonApplicationProperties.getContextPath().equals("/agency")
//                    && StringUtils.hasLength(loginUserDTO.getUid())
//                    && loginUserDTO.getUid().contains("_")
            ){
                return Boolean.TRUE;
            }

            if (SecurityUtils.isSystemAdmin() || SecurityUtils.isAdmin()){
                return Boolean.TRUE;
            }
            cachePermission.checkUserPrivileges(userId, uniformURI,request.getMethod());
        }
        return true;
    }

    private void forwardRequest(String targetUrl, HttpServletRequest request, HttpServletResponse response) throws IOException {
        HttpMethod method = HttpMethod.resolve(request.getMethod());
        HttpHeaders headers = buildHeaders(request);

        HttpEntity<String> entity;
        String queryString = request.getQueryString();
        if (queryString != null) {
            targetUrl += "?" + queryString;
        }
        if (method == HttpMethod.GET) {
            entity = new HttpEntity<>(headers);
        } else {
            String body = ServletUtil.getBody(request);
            entity = new HttpEntity<>(body, headers);
        }

        try {
            // 发送请求并接收响应，使用byte数组接收以便处理所有类型的响应体
            ResponseEntity<byte[]> targetResponse = restTemplate.exchange(crmUrl + targetUrl, method, entity, byte[].class);
            HttpHeaders originalHeaders = targetResponse.getHeaders();

            // 将所有原始头信息设置到响应中
            originalHeaders.forEach((headerName, headerValues) -> headerValues.forEach(headerValue -> response.setHeader(headerName, headerValue)));
            response.setStatus(targetResponse.getStatusCodeValue());

            // 401 => 404
            if (targetResponse.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                response.setStatus(HttpStatus.NOT_FOUND.value());
            }

            // 获取响应体
            byte[] body = targetResponse.getBody();
            if (body != null) {
                // 判断Content-Type头以确定如何处理响应体
                String contentType = originalHeaders.getContentType() != null ? originalHeaders.getContentType().toString() : "";
                if (isFileContentType(contentType)) {
                    // 处理文件流响应
                    response.getOutputStream().write(body);
                    response.getOutputStream().flush();
                } else {
                    // 处理JSON响应
                    String responseBody = new String(body, StandardCharsets.UTF_8);
                    response.getWriter().write(responseBody);
                    response.getWriter().flush();
                }
            }
        } catch (HttpClientErrorException e) {
            // 403 时，允许前端访问 www-authenticate 头
            HttpHeaders responseHeaders = e.getResponseHeaders();
            if (responseHeaders != null) {
                responseHeaders.keySet().stream().filter(HttpHeaders.WWW_AUTHENTICATE::equalsIgnoreCase).findFirst()
                        .ifPresent(header -> {
                            response.setHeader(HttpHeaders.WWW_AUTHENTICATE, responseHeaders.getFirst(HttpHeaders.WWW_AUTHENTICATE));
                            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, header);
                        });
            }
            // Handle 401 = > 404
            log.info("crm forward url = {} code = {}, result = {}", crmUrl + targetUrl,  e.getStatusCode(), e.getResponseBodyAsString());
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED || e.getStatusCode() == HttpStatus.NOT_FOUND) {
                throw new CustomParameterizedException(Status.NOT_FOUND.getStatusCode(), "No crm permission", "No crm permission", null);
            } else {
                //透传
                response.setStatus(e.getStatusCode().value());
                response.getWriter().write(e.getResponseBodyAsString());
                response.getWriter().flush();
            }
        } catch (HttpServerErrorException e) {
            //透传
            log.info("crm forward url = {} code = {}, result = {}", crmUrl + targetUrl, e.getStatusCode(), e.getResponseBodyAsString());
            response.setStatus(e.getStatusCode().value());
            response.getWriter().write(e.getResponseBodyAsString());
            response.getWriter().flush();
        } finally {
            commonRedisService.delete(LoginUtil.USER_ACCOUNT_REDIS_EXPIRE_PREFIX + SecurityUtils.getUserId());
        }
    }

    private HttpHeaders buildHeaders(HttpServletRequest request) {
        HttpHeaders headers = new HttpHeaders();
        // Copy headers from the original request
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.set(headerName, request.getHeader(headerName));
        }
        String url = request.getRequestURI().replaceAll("/company/crm", "");
        RequestMethod requestMethod = RequestMethod.valueOf(request.getMethod());
        log.debug("url: {}, method: {}, crmForwardUrl: {}", url, requestMethod, JSONUtil.toJsonStr(crmForwardUrlProperties.getCrmPublicUrlMap()));
        if (getMatchedPattern(url, requestMethod, crmForwardUrlProperties.getCrmPublicUrlMap()) != null) {
            String clientToken = "Bearer " + ClientTokenHolder.getInstance().getClientToken().access_token();
            log.info("forward public crm url with client token, tenantId: {}, userId: {}, client token: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), clientToken);
            headers.set(X_INTERNAL_PIN_HEADER, commonApplicationProperties.getApnInternalPin());
            headers.set(X_ORIGINAL_TENANT_ID_HEADER, String.valueOf(SecurityUtils.getTenantId()));
            headers.set(X_ORIGINAL_USER_ID_HEADER, String.valueOf(SecurityUtils.getUserId()));
            headers.set("Authorization", clientToken);
        }
        log.debug("headers: {}", JSONUtil.toJsonStr(headers.toSingleValueMap()));
        return headers;
    }

    private boolean isFileContentType(String contentType) {
        return contentType.startsWith("application/octet-stream") ||
                contentType.startsWith("application/pdf") ||
                contentType.startsWith("application/zip") ||
                contentType.startsWith("image/") ||
                contentType.startsWith("video/") ||
                contentType.startsWith("audio/") ||
                contentType.startsWith("application/vnd");
    }

    private String getMatchedPattern(String requestURI, RequestMethod requestMethod, Map<String, List<String>> urlMap) {
        if (urlMap == null || urlMap.isEmpty()) {
            return null;
        }
        return urlMap.getOrDefault(requestMethod.name(), List.of()).stream()
                .filter(pattern -> antPathMatcher.match(pattern, requestURI) || pattern.equals(requestURI))
                .findFirst()
                .orElse(null);
    }

    private String buildTargetUrl(String requestURI, Map<String, String> uriVariables) {
        return UriComponentsBuilder.fromPath(requestURI)
                .buildAndExpand(uriVariables)
                .toUriString();
    }
    private void countApi(LoginInformation loginUserDTO) {
        final Long userId = loginUserDTO.getId();
        if (!userId.equals(-1L)) {
            final Long tenantId = loginUserDTO.getTenantId();
            commonRedisService.set(String.format(RedisConstants.STATISTIC_USER_ONLINE, tenantId, userId),
                    DateUtil.currentTime(DateUtil.YYYY_MM_DD_T_HH_MM_SS_Z) + ";" + loginUserDTO.getTimezone(),
                    commonApplicationProperties.getOnlineUserStatisticPeriod());
        }
    }

//    /**
//     * if the current user is calling the logout API, then remove current user's key from redis
//     *
//     * @param request   http request from frontend
//     * @param uniformURI    uniform URI in which all path variables were replaced with {}
//     * @param loginUserDTO   the current user's context
//     */
//    private void checkUserAuthentication(HttpServletRequest request, String uniformURI, LoginUserDTO loginUserDTO) {
//        String bearerToken = request.getHeader("Authorization");
//        String token = TokenUtil.extractToken(bearerToken);
//        Long userId = loginUserDTO.getId();
//        Long originalUserId = loginUserDTO.getOriginalUserId();
//        if (Objects.isNull(originalUserId) || originalUserId.longValue() < 0){
//            originalUserId = userId;
//        }
//        if (uniformURI.endsWith("/logout")) {
//            this.logoutHandler(token, userId, originalUserId);
//        }else if (!userId.equals(-1L) && StringUtils.hasLength(token)){
//            String activeUserKey = String.format(RedisConstants.DATA_KEY_ACTIVE_USER, userId, originalUserId, token);
//            if (!commonRedisService.exists(activeUserKey)){
//                throw new CustomParameterizedException(Status.UNAUTHORIZED.getStatusCode(), "Unauthorized", "The session expired or you logged out, please go to login!", null);
//            }
//            if (userId.equals(originalUserId)){
//                commonRedisService.set(activeUserKey, "1", commonApplicationProperties.getActiveUserPeriod());
//            }else {
//                // impersonation login
//                commonRedisService.softExpire(activeUserKey, commonApplicationProperties.getActiveUserPeriod());
//            }
//        }
//    }

    private void logoutHandler(String token, Long userIdFrom, Long userIdTo){
        if (!StringUtils.hasLength(token)) {
            throw new CustomParameterizedException("Access token cannot be empty, please go check!");
        }
        String activeUserKey = String.format(RedisConstants.DATA_KEY_ACTIVE_USER, userIdFrom, userIdTo, token);
        if (commonRedisService.exists(activeUserKey)){
            commonRedisService.delete(activeUserKey);
        }
//        try {
//            final JSONObject userJson = authorityService.checkToken(token);
//            final String uid = userJson.getJSONObject("user_name").getStr("uid");
//            String activeUserKey = String.format(RedisConstants.DATA_KEY_ACTIVE_USER, uid);
//            if (commonRedisService.exists(activeUserKey)){
//                commonRedisService.delete(activeUserKey);
//            }
//        }catch (Exception ex){
//            log.warn(ex.getMessage());
//            if (ex.getMessage().contains(TOKEN_EXPIRED_CONTENT)){
//                log.info(TOKEN_EXPIRED_CONTENT);
//            }else {
//                throw new CustomParameterizedException(Status.UNAUTHORIZED.getStatusCode(), "Unauthorized", "Invalid access token, please go check!", null);
//            }
//        }
//        String expiredTokenKey = String.format(RedisConstants.DATA_KEY_EXPIRED_TOKEN, StringUtil.getHashString(bearerToken));
//        String expiredTokenKey = String.format(RedisConstants.DATA_KEY_EXPIRED_TOKEN, token);
//        commonRedisService.set(expiredTokenKey, "1",
//                commonApplicationProperties.getActiveUserPeriod() * 2);
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }
}