package com.altomni.apn.company.service.company.overview.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.config.constants.ElasticSearchConstants;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.domain.dict.CompanyIndustryRelation;
import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.domain.enumeration.NodeTypeTableEnum;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.company.BriefCompanyDTO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.search.*;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.enums.*;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.company.config.env.ApplicationProperties;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.business.*;
import com.altomni.apn.company.domain.company.*;
import com.altomni.apn.company.domain.company.lcoation.CompanyLocation;
import com.altomni.apn.company.domain.company.note.CompanyProgressNote;
import com.altomni.apn.company.domain.company.note.CompanyProgressNoteContactRelation;
import com.altomni.apn.company.domain.contract.Contract;
import com.altomni.apn.company.domain.contract.ContractBusinessRelation;
import com.altomni.apn.company.domain.contract.ContractServiceType;
import com.altomni.apn.company.domain.contract.ContractSigner;
import com.altomni.apn.company.domain.enumeration.business.BusinessProgress;
import com.altomni.apn.company.domain.enumeration.company.CompanyClientLevelType;
import com.altomni.apn.company.domain.job.JobCompanyBrief;
import com.altomni.apn.company.domain.job.JobLocationCompanyBrief;
import com.altomni.apn.company.domain.talent.TalentAdditionalInfoSyncBrief;
import com.altomni.apn.company.domain.talent.TalentCompanyMigrateBrief;
import com.altomni.apn.company.domain.talent.TalentCompanySyncBrief;
import com.altomni.apn.company.domain.talent.TalentCurrentLocationCompanyBrief;
import com.altomni.apn.company.domain.user.UserCompanyMigrateBrief;
import com.altomni.apn.company.domain.vm.EntityCountVM;
import com.altomni.apn.company.domain.vm.EntityNameVM;
import com.altomni.apn.company.domain.vo.DataModifyFeedbackReportVO;
import com.altomni.apn.company.repository.application.ApplicationServiceRepository;
import com.altomni.apn.company.repository.business.*;
import com.altomni.apn.company.repository.company.*;
import com.altomni.apn.company.repository.company.folder.CompanyCustomFolderConnectClientRepository;
import com.altomni.apn.company.repository.company.location.CompanyLocationRepository;
import com.altomni.apn.company.repository.company.note.CompanyProgressNoteContactRelationRepository;
import com.altomni.apn.company.repository.company.note.CompanyProgressNoteRepository;
import com.altomni.apn.company.repository.contract.ContractBusinessRelationRepository;
import com.altomni.apn.company.repository.contract.ContractRepository;
import com.altomni.apn.company.repository.contract.ContractServiceTypeRepository;
import com.altomni.apn.company.repository.contract.ContractSignerRepository;
import com.altomni.apn.company.repository.job.JobCompanyBriefRepository;
import com.altomni.apn.company.repository.job.JobLocationCompanyBriefRepository;
import com.altomni.apn.company.repository.talent.*;
import com.altomni.apn.company.repository.user.UserServiceRepository;
import com.altomni.apn.company.service.application.ApplicationClient;
import com.altomni.apn.company.service.business.AccountBusinessService;
import com.altomni.apn.company.service.business.SalesLeadClientContactService;
import com.altomni.apn.company.service.company.note.CompanyClientNoteService;
import com.altomni.apn.company.service.company.overview.CompanyOverviewService;
import com.altomni.apn.company.service.contract.ContractService;
import com.altomni.apn.company.service.dto.location.CompanyLocationMigrateDTO;
import com.altomni.apn.company.service.dto.overview.CompanySearchConditionDTO;
import com.altomni.apn.company.service.elastic.EsCompanyDataService;
import com.altomni.apn.company.service.folder.FolderService;
import com.altomni.apn.company.service.user.UserService;
import com.altomni.apn.company.vo.business.AccountBusinessVO;
import com.altomni.apn.company.vo.business.OwnerVO;
import com.altomni.apn.company.vo.company.*;
import com.altomni.apn.user.domain.permission.PermissionTeam;
import com.altomni.apn.user.domain.permission.PermissionTeamLeader;
import com.altomni.apn.user.domain.permission.PermissionUserTeam;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.altomni.apn.common.constants.AuthConstants.TOKEN_TYPE;

@Service
@Slf4j
public class CompanyOverviewServiceImpl implements CompanyOverviewService {

    private final Double CLIENT_ACCOUNT_PROGRESS = 1.20;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private CompanyRepository companyRepository;

    @Resource
    private CompanyLocationRepository companyLocationRepository;

    @Resource
    private SalesLeadClientContactRepository salesLeadClientContactRepository;

    @Resource
    private UserService userService;

    @Resource
    private TalentContactCompanyBriefRepository talentContactCompanyBriefRepository;

    @Resource
    private TalentCurrentLocationCompanyBriefRepository talentCurrentLocationCompanyBriefRepository;

    @Resource
    private AccountBusinessRepository accountBusinessRepository;

    @Resource
    private AccountBusinessContactRelationRepository accountBusinessContactRelationRepository;

    @Resource
    private AccountBusinessAdministratorRepository accountBusinessAdministratorRepository;

    @Resource
    private TalentServiceRepository talentServiceRepository;

    @Resource
    private UserServiceRepository userServiceRepository;

    @Resource
    private CompanyAddtionalInfoRepository companyAddtionalInfoRepository;

    @Resource
    private EsCompanyDataService esCompanyDataService;

    @Resource
    private CompanyIndustryRelationRepository companyIndustryRelationRepository;

    @Resource
    private JobCompanyBriefRepository jobCompanyBriefRepository;

    @Resource
    private JobLocationCompanyBriefRepository jobLocationCompanyBriefRepository;

    @Resource
    private ApplicationServiceRepository applicationServiceRepository;

    @Resource
    public SalesLeadClientContactAddtionalInfoRepository salesLeadClientContactAddtionalInfoRepository;

    @Resource
    private FolderService folderService;

    @Resource
    private CompanyTagRelationRepository companyTagRelationRepository;

    @Resource
    private ContactTagRelationRepository contactTagRelationRepository;

    @Resource
    private CompanyClientNoteService companyClientNoteService;

    @Resource
    private ApplicationClient applicationClient;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    @Resource
    private CachePermission cachePermission;

    @Resource
    private InitiationService initiationService;

    @Resource
    private AccountBusinessService accountBusinessService;

    @Resource
    private CompanyContactRepository companyContactRepository;

    @Resource
    private CompanySalesLeadRepository companySalesLeadRepository;

    @Resource
    private AccountBusinessServiceTypeRelationRepository accountBusinessServiceTypeRelationRepository;

    @Resource
    private ContractRepository contractRepository;

    @Resource
    private ContractBusinessRelationRepository contractBusinessRelationRepository;

    @Resource
    private ContractSignerRepository contractSignerRepository;

    @Resource
    private ContractServiceTypeRepository contractServiceTypeRepository;

    @Resource
    private CompanyProgressNoteRepository companyProgressNoteRepository;

    @Resource
    private CompanyProgressNoteContactRelationRepository companyProgressNoteContactRelationRepository;

    @Resource
    private CompanyMigrateRepository companyMigrateRepository;

    @Resource
    private TalentCompanySyncBriefRepository talentCompanySyncBriefRepository;

    @Resource
    private HttpService httpService;

    @Value("${application.crmUrl}")
    private String crmUrl;

    private final String CRM_MIGRATE_URL = "/account/api/v1/save-apn-data";

    private final String CRM_QUERY_TAGS_URL = "/account/api/v1/query-tag-data";

    private final String CRM_TENANT_URL = "/account/api/v1/companies/employer-tenant";

    @Resource
    CompanyNativeRepository companyNativeRepository;

    private final String KEY_EXPERIENCES = "experiences";

    private final String Client_COMPANY_SALES_LEAD_OWNER_ID = "responsibility6.id";

    private final String COMPANY_HAS_SERVICETYPES = "hasServiceTypes";

    private final String COMPANY_TAG_OVERSEA_BUSINESS = "出海";

    private final String COMPANY_TAGS = "companyTags";

    private final String COMPANY = "company";

    private final String COMPANY_RELATIONSHIP = "companyRelationship";

    private final String COMPANY_ADDITIONAL_INFO = "additionalInfo";

    private final String INDUSTRY = "industries";

    private final String LOCATION = "locations";

    private final String BUSINESS = "business";

    private final String SERVICE_TYPE_RELATION = "serviceTypeRelations";

    private final String ADMINISTRATOR = "administrators";

    private final String BUSINESS_CONTACT_RELATION = "businessContactRelations";

    private final String CLIENT_CONTACT = "clientContacts";

    private final String CONTRACT_RELATION = "contractRelations";

    private final String CONTRACT = "contracts";

    private final String CONTRACT_SINGER = "contractSingers";

    private final String CONTRACT_SERVICE_TYPE = "contractServiceTypes";

    private final String CRM_CONTACT = "contacts";

    private final String CONTACT_INFO = "contactInfo";

    private final String CONTACT_LOCATION = "contactLocation";

    private final String PROGRESS_NOTE = "progressNotes";

    private final String USER = "users";

    private final String USER_ADMIN = "userAdmin";

    private final String TENANT = "tenants";

    private final String TEAM = "teams";

    private final String TEAM_LEADER = "teamLeaders";

    private final String TEAM_USER = "teamUsers";

    private final String COMPANY_CONTACT = "companyContacts";

    private final static String HEADERS_AUTHORIZATION_KEY = "Authorization";

    private final static String SPACE = " ";



    public static String crmCompanyTagsUrl(Long companyId) {
        return "/account/api/v1/companies/" + companyId + "/tags";
    }

    @Override
    public CompanyListVO searchCompany(CompanySearchConditionDTO condition, Pageable pageable, HttpHeaders headers) throws Throwable {
        checkFoldersPermission(condition.getFolders());
        SearchGroup searchGroup = new SearchGroup();
        ServiceUtils.myCopyProperties(condition, searchGroup);
        //check search area
        SearchConditionDTO.checkPageable(pageable);
        //fix pageInfo
        pageable = PageRequest.of(pageable.getPageNumber() - 1, pageable.getPageSize(), pageable.getSort());
        //format filter
        SearchFilterDTO searchFilterDTO = new SearchFilterDTO();
        List<String> cloneList = ObjectUtil.cloneByStream(ElasticSearchConstants.COMPANY_PROSPECT_SEARCH_SOURCE);
        if (condition.getFilter() != null && CollUtil.isNotEmpty(condition.getFilter())) {
            SearchParam searchParam = condition.getFilter().get(0);
            List<ConditionParam> conditionParamList = searchParam.getCondition();
            for (ConditionParam conditionParam : conditionParamList) {
                if (Client_COMPANY_SALES_LEAD_OWNER_ID.equals(conditionParam.getKey()) || (COMPANY_HAS_SERVICETYPES.equals(conditionParam.getKey()))) {
                    cloneList = ObjectUtil.cloneByStream(ElasticSearchConstants.COMPANY_CLIENT_SEARCH_SOURCE);
                }
            }
        }

        List<String> sourceList = new ArrayList<>(cloneList);
        searchFilterDTO.setSource(sourceList);
        searchFilterDTO.setQueryFilter(condition.getFilter());
        searchFilterDTO.setFolders(condition.getFolders());
        searchGroup.setIndex(Constants.INDEX_COMPANIES + SecurityUtils.getTenantId());
        searchGroup.setModule(condition.getModule().getName());
        searchGroup.setFilter(searchFilterDTO);
        searchGroup.setTimeZone(condition.getTimezone());
        searchGroup.setLanguage(condition.getLanguage().toDbValue());
        //format condition
        if (CollUtil.isNotEmpty(condition.getSearch())) {
            formatSearchGroup(condition.getSearch());
        } else {
            searchGroup.setSearch(new ArrayList<>());
        }
        CompanyListVO companyListVO = new CompanyListVO();
        HttpResponse response;
        try {
            if (condition.getUuid() == null) {
                response = esCompanyDataService.searchCacheAndRefreshCache(searchGroup, pageable);
            } else {
                response = esCompanyDataService.searchCache(condition.getUuid(), pageable);
                //cache invalidation retry
                if (response != null && response.getCode() == HttpStatus.BAD_REQUEST.value()) {
                    response = esCompanyDataService.searchCacheAndRefreshCache(searchGroup, pageable);
                }
            }
//            if (response != null && response.getCode() == HttpStatus.OK.value()) {
//                saveSearchHistory(condition.getSearch());
//            }
        } catch (ExternalServiceInterfaceException es) {
            throw es;
        } catch (Exception e) {
            if (Objects.isNull(e.getCause())) {
                throw e;
            }
            throw e.getCause();
        }
        if (response == null || response.getCode() != HttpStatus.OK.value() || ObjectUtil.isEmpty(response.getBody())) {
            headers.set("Pagination-Count", String.valueOf(0));
            companyListVO.setData(new ArrayList<>());
        } else {
            headers.set("Pagination-Count", ElasticSearchUtil.getObjectCountFromResponseHeader(response.getHeaders()) + "");
            companyListVO.setData(JSONUtil.parseArray(response.getBody()));
            companyListVO.setUuid(response.getHeaders().get("uuid"));
            companyListVO.setNoContractCount(response.getHeaders().get("total-unsigned") == null ? 0 : Integer.parseInt(response.getHeaders().get("total-unsigned")));
        }
        return companyListVO;
    }

    private void checkFoldersPermission(List<String> folders) {
        if (CollUtil.isEmpty(folders)) {
            return;
        }
        List<Long> lFolders = folders.stream()
            .map(Long::valueOf)
            .collect(Collectors.toList());
        folderService.checkCustomFolderPermission(lFolders);
    }

    private void formatSearchGroup(List<SearchParam> search) {
        search.forEach(s -> {
            s.getCondition().forEach(l -> {

                if ("importance".equals(l.getKey())) {
                    JSONObject data = JSONUtil.parseObj(JSONUtil.toJsonStr(JSONUtil.parse(l.getValue())));
                    Iterator iter = data.entrySet().iterator();
                    while (iter.hasNext()) {
                        Map.Entry entry = (Map.Entry) iter.next();
                        if ("data".contains(entry.getKey().toString())) {
                            entry.setValue(CompanyClientLevelType.fromDisplay(entry.getValue().toString()).toDbValue());
                            l.setValue(data);
                        }
                    }
                }

            });
        });
    }

    @Override
    public List<CompanyMigrate> saveCompany(JSONObject jsonObject) {
        if (!jsonObject.containsKey(COMPANY) && !jsonObject.containsKey(LOCATION)) {
            return null;
        }
        if (jsonObject.containsKey(COMPANY)) {
            List<CompanyMigrate> companyList = jsonObject.containsKey(COMPANY) ? jsonObject.getJSONArray(COMPANY).toList(CompanyMigrate.class) : new ArrayList<>();
            List<CompanyAdditionalInfo> additionalInfoList = jsonObject.containsKey(COMPANY_ADDITIONAL_INFO) ?
                    jsonObject.getJSONArray(COMPANY_ADDITIONAL_INFO).toList(CompanyAdditionalInfo.class).stream().peek(o -> o.setExtendedInfo(JsonUtil.toJson(JSONUtil.parseObj(o.getExtendedInfo())))).toList() : new ArrayList<>();
            //commonEsId在apn塞入additionalInfo中，用于往es同步
            //禁猎客户需求，增加CRM同步到APN salesLeadCompanyId，bdCompaniesBusinessInformationId
            setCRMRelationIds(companyList, additionalInfoList);
            List<CompanyIndustryRelation> companyIndustryRelationList = jsonObject.containsKey(INDUSTRY) ? jsonObject.getJSONArray(INDUSTRY).toList(CompanyIndustryRelation.class) : new ArrayList<>();
            List<CompanyLocationMigrateDTO> companyLocationList = jsonObject.containsKey(LOCATION) ? jsonObject.getJSONArray(LOCATION).toList(CompanyLocationMigrateDTO.class).stream().peek(o -> o.setOriginalLoc(JsonUtil.toJson(JSONUtil.parseObj(o.getOriginalLoc())))).toList() : new ArrayList<>();
            List<CompanyContact> companyContactList = jsonObject.containsKey(COMPANY_CONTACT) ? jsonObject.getJSONArray(COMPANY_CONTACT).toList(CompanyContact.class) : new ArrayList<>();
            List<CompanyTagRelation> companyTagList = jsonObject.containsKey(COMPANY_TAGS) ? jsonObject.getJSONArray(COMPANY_TAGS).toList(CompanyTagRelation.class) : new ArrayList<>();
            List<CompanyRelationSync> companyRelationSyncList = jsonObject.containsKey(COMPANY_RELATIONSHIP) ? jsonObject.getJSONArray(COMPANY_RELATIONSHIP).toList(CompanyRelationSync.class) : new ArrayList<>();

            List<Long> companyIds = companyList.stream().map(CompanyMigrate::getId).toList();
            //set last_sync_time
            List<CompanyMigrate> companyMigrateList = companyMigrateRepository.findAllById(companyIds);
            if (CollUtil.isNotEmpty(companyMigrateList)) {
                Map<Long, CompanyMigrate> map = companyMigrateList.stream().collect(Collectors.toMap(CompanyMigrate::getId, a -> a));
                companyList.forEach(company -> {
                    if (map.containsKey(company.getId())) {
                        //防止不触发同步
                        company.setLastSyncTime(map.get(company.getId()).getLastSyncTime());
                    }
                    //主动触发canal同步company
                    company.setLastEditedTime(Instant.now());
                });
            }

            companyList.forEach(company -> {
                if (null == company.getRequestDate()) {
                    company.setRequestDate(Instant.now());
                }
            });

            Map<Long, CompanyIndustryRelation> companyIndustryRelationMap = companyIndustryRelationList.stream().collect(Collectors.toMap(CompanyIndustryRelation::getId, o -> o));
            Map<Long, CompanyContact> companyContactMap = companyContactList.stream().collect(Collectors.toMap(CompanyContact::getId, o -> o));
            Map<Long, CompanyTagRelation> companyTagMap = companyTagList.stream().collect(Collectors.toMap(CompanyTagRelation::getId, o -> o));

            List<CompanyIndustryRelation> existedCompanyIndustryRelationList = companyIndustryRelationRepository.findAllByAccountCompanyIdIn(companyIds);
            List<CompanyLocation> existedCompanyLocationList = companyLocationRepository.findAllByAccountCompanyIdIn(companyIds);
            List<CompanyContact> existedCompanyContactList = companyContactRepository.findAllByCompanyIdIn(companyIds);
            List<CompanyTagRelation> existedCompanyTagList = companyTagRelationRepository.findAllByAccountCompanyIdIn(companyIds);

            List<Long> deleteIndustryRelationIds = existedCompanyIndustryRelationList.stream().map(CompanyIndustryRelation::getId).filter(o -> !companyIndustryRelationMap.containsKey(o)).toList();
            List<Long> deleteContactIds = existedCompanyContactList.stream().map(CompanyContact::getId).filter(o -> !companyContactMap.containsKey(o)).toList();
            List<Long> deleteTagIds = existedCompanyTagList.stream().map(CompanyTagRelation::getId).filter(o -> !companyTagMap.containsKey(o)).toList();

            existedCompanyIndustryRelationList.forEach(item -> {
                if (companyIndustryRelationMap.containsKey(item.getId())) {
                    ServiceUtils.myCopyProperties(companyIndustryRelationMap.get(item.getId()), item);
                }
            });
            existedCompanyContactList.forEach(item -> {
                if (companyContactMap.containsKey(item.getId())) {
                    ServiceUtils.myCopyProperties(companyContactMap.get(item.getId()), item);
                }
            });
            existedCompanyTagList.forEach(item -> {
                if (companyTagMap.containsKey(item.getId())) {
                    ServiceUtils.myCopyProperties(companyTagMap.get(item.getId()), item);
                }
            });

            companyIndustryRelationRepository.deleteAllByIdInBatch(deleteIndustryRelationIds);
            companyContactRepository.deleteAllByIdInBatch(deleteContactIds);
            companyTagRelationRepository.deleteAllByIdInBatch(deleteTagIds);
            companyMigrateRepository.saveAll(companyList);
            companyAddtionalInfoRepository.saveAll(additionalInfoList);
            companyIndustryRelationRepository.saveAll(companyIndustryRelationList);
            companyContactRepository.saveAll(companyContactList);
            companyTagRelationRepository.saveAll(companyTagList);
            saveCompanyLocation(companyIds, companyLocationList, existedCompanyLocationList);
            saveDefaultCompanyClientNote(companyTagList);
            updateContactCompanyName(companyList);
            resolveCompanyRelation(companyRelationSyncList);

            return companyList;
        } else {
            //only use add companyLocation
            List<CompanyLocationMigrateDTO> companyLocationDTOList = jsonObject.containsKey(LOCATION) ? jsonObject.getJSONArray(LOCATION).toList(CompanyLocationMigrateDTO.class).stream().peek(o -> o.setOriginalLoc(JsonUtil.toJson(JSONUtil.parseObj(o.getOriginalLoc())))).toList() : new ArrayList<>();
            List<CompanyLocation> companyLocationList = companyLocationDTOList.stream().map(CompanyLocation::fromCompanyLocationMigrateDTO).toList();
            companyLocationRepository.saveAll(companyLocationList);
        }

        return null;
    }

    private void saveCompanyLocation(List<Long> companyIds, List<CompanyLocationMigrateDTO> companyLocationDTOList, List<CompanyLocation> existedCompanyLocationList) {
        Map<Long, List<CompanyLocationMigrateDTO>> companyLocationDTOMap = companyLocationDTOList.stream().collect(Collectors.groupingBy(CompanyLocationMigrateDTO::getAccountCompanyId));
        Map<Long, List<CompanyLocation>> existedCompanyLocationMap = existedCompanyLocationList.stream().collect(Collectors.groupingBy(CompanyLocation::getAccountCompanyId));
        List<CompanyLocation> deleteLocationList = new ArrayList<>();
        List<CompanyLocation> addLocationList = new ArrayList<>();
        for (Long companyId : companyIds) {
            //itemCompanyLocationMigrateDTOList是CRM过来的；location数据,itemCompanyLocationList是apn保存的老数据，需要以CRM为主合并
            List<CompanyLocationMigrateDTO> itemCompanyLocationMigrateDTOList = companyLocationDTOMap.getOrDefault(companyId, new ArrayList<>());
            List<CompanyLocation> itemCompanyLocationList = existedCompanyLocationMap.getOrDefault(companyId, new ArrayList<>());
            //找出CRM没有的location删除
            Set<String> itemOriginalLocs = itemCompanyLocationMigrateDTOList.stream().map(CompanyLocationMigrateDTO::getOriginalLoc).collect(Collectors.toSet());
            deleteLocationList.addAll(itemCompanyLocationList.stream().filter(o -> !itemOriginalLocs.contains(o.getOriginalLoc())).collect(Collectors.toList()));
            //找出CRM新增过来的数据保存到APN
            Set<String> existItemOriginalLocs = itemCompanyLocationList.stream().map(CompanyLocation::getOriginalLoc).collect(Collectors.toSet());
            addLocationList.addAll(itemCompanyLocationMigrateDTOList.stream().filter(o -> !existItemOriginalLocs.contains(o.getOriginalLoc())).map(CompanyLocation::fromCompanyLocationMigrateDTO).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(deleteLocationList)) {
            companyLocationRepository.deleteAllByIdInBatch(deleteLocationList.stream().map(CompanyLocation::getId).toList());
        }
        if (CollUtil.isNotEmpty(addLocationList)) {
            companyLocationRepository.saveAll(addLocationList);
        }
    }

//    @Resource
//    private CompanyRelationshipsRepository companyRelationshipsRepository;

    private void resolveCompanyRelation(List<CompanyRelationSync> companyRelationSyncList) {
//TODO 关系可以建立开发中客户和正式客户关系，因此直接查询crm，暂时不同步到apn
//        for(CompanyRelationSync relation : companyRelationSyncList) {
//            Long id = relation.getId();
//            List<CompanyRelationships> parentRelations = relation.getParentRelations();
//            if(parentRelations != null) {
//                relationshipsChange(parentRelations, companyRelationshipsRepository.findCompanyRelationshipsByCompanyId(id));
//            }
//            List<CompanyRelationships> childRelations = relation.getChildRelations();
//            if(childRelations != null) {
//                relationshipsChange(childRelations, companyRelationshipsRepository.findCompanyRelationshipsByParentCompanyId(id));
//            }
//
//        }
    }


    private void setCRMRelationIds(List<CompanyMigrate> companyList, List<CompanyAdditionalInfo> additionalInfoList) {
        // 将列表转为Map提升查找效率（时间复杂度从O(n)降到O(1)）
        Map<Long, CompanyAdditionalInfo> infoMap = additionalInfoList.stream()
                .collect(Collectors.toMap(CompanyAdditionalInfo::getAccountCompanyId, Function.identity()));

        companyList.forEach(company -> {
            CompanyAdditionalInfo additionalInfo = infoMap.get(company.getId());
            if (additionalInfo != null) {
                JSONObject jsonObject = JSONUtil.parseObj(additionalInfo.getExtendedInfo());

                // 使用链式判断避免重复调用ObjectUtil.isNotNull
                Optional.ofNullable(company.getCommonEsId()).ifPresent(v -> jsonObject.put("commonEsId", v));
                Optional.ofNullable(company.getSalesLeadCompanyId()).ifPresent(v -> jsonObject.put("leadsCompanyId", v));
                Optional.ofNullable(company.getBdCompaniesBusinessInformationId()).ifPresent(v -> jsonObject.put("businessInfoCompanyId", v));

                additionalInfo.setExtendedInfo(JsonUtil.toJson(jsonObject));
            }
        });
    }

    private void saveDefaultCompanyClientNote(List<CompanyTagRelation> companyTagList) {
        if (CollUtil.isEmpty(companyTagList)) {
            return;
        }
        List<Long> overseaCompanyIds = companyTagList.stream().filter(o -> COMPANY_TAG_OVERSEA_BUSINESS.equals(o.getTag())).map(CompanyTagRelation::getAccountCompanyId).distinct().toList();
        companyClientNoteService.saveDefaultCompanyClientNote(overseaCompanyIds);
    }

    @Override
    public AccountCompanyVO queryClientCompany(Long id, HttpHeaders headers) {
        Company accountCompany = checkCompanyExist(id);
        this.countCompanyRetrieving(accountCompany);
        return toVo(accountCompany, headers);
    }

    private void buildAndSendMessage(Long tenantId, Map<String, String> tenantIdToTenantName, String user, Set<String> companies){
        JSONObject msg = new JSONObject();
        // set title
        msg.put("title", String.format("WARNING (Tenant: %d-%s)", tenantId, tenantIdToTenantName.get(String.valueOf(tenantId))));
        JSONArray content = new JSONArray();
        //set sub title
        JSONArray subTitle = new JSONArray();
        JSONObject subContent = new JSONObject();
        subContent.put("tag", "text");
        subContent.put("text", String.format("%s viewed %d companies over the past 24 hours!", user, companies.size()));
        subTitle.add(subContent);
        content.add(subTitle);
        // set pre line
        JSONArray preLine = new JSONArray();
        JSONObject preLineContent = new JSONObject();
        preLineContent.put("tag", "text");
        preLineContent.put("text", "------------------------------------------------------------------------");
        preLine.add(preLineContent);
        content.add(preLine);
        // set content details
        for (String companyIdName : companies) {
            JSONArray row = new JSONArray();
            JSONObject detail = new JSONObject();
            detail.put("tag", "a");
            detail.put("text", companyIdName);
            detail.put("href", String.format(applicationProperties.getMonitorCompanyUrl(), companyIdName.split("-")[0]));
            row.add(detail);
            content.add(row);
        }
        msg.put("content", content);
        NotificationUtils.sendRichTextMessage(applicationProperties.getMonitorWebhookKey(), applicationProperties.getMonitorWebhookUrl(), msg);
    }

    private void countCompanyRetrieving(Company accountCompany){
        log.info("countCompanyRetrieving by company id {}", accountCompany.getId());
        Set<String> whitelist = Arrays.stream(applicationProperties.getMonitorCompanyWhitelist().split(",")).collect(Collectors.toSet());
        String userEmail = SecurityUtils.getEmail();
        if (whitelist.contains(userEmail)){
            return;
        }
        Long tenantId = SecurityUtils.getTenantId();
        String user = String.format("%s(%d)", userEmail, SecurityUtils.getUserId());
        String key = String.format(RedisConstants.DATA_KEY_MONITOR_COMPANY, tenantId, user);
        String company = accountCompany.getId() + "-" + accountCompany.getFullBusinessName();
        if (commonRedisService.exists(key)){
//            long totalCompanies = commonRedisService.scard(key);
            Set<String> companies = commonRedisService.smembers(key);
            if (companies.contains(company)){
                return;
            }
            companies.add(company);
            commonRedisService.sadd(key, Set.of(company), null);
            long totalCompanies = companies.size();
            Map<String, String> tenantIdToTenantName = Arrays.stream(applicationProperties.getMonitorCompanyForTenants()
                    .split(";"))
                    .collect(Collectors.toMap(v -> v.split("-")[0], v -> v.split("-")[1]));
            if (totalCompanies >= applicationProperties.getMonitorCompanyThreshold()
                    && applicationProperties.isMonitorEnabled() && tenantIdToTenantName.containsKey(String.valueOf(tenantId))){
                // send to lark
                this.buildAndSendMessage(tenantId, tenantIdToTenantName, user, companies);
            }
        }else {
            commonRedisService.sadd(key, Set.of(company), RedisConstants.EXPIRE_IN_1_DAY);
        }
    }

    @Override
    public List<AccountCompanyCoAmVO> queryClientCompanyCoAm(Long id) {
        Company accountCompany = checkCompanyExist(id);
        List<BusinessFlowAdministrator> administratorList = accountBusinessAdministratorRepository.findAllByCompanyIdAndSalesLeadRoleType(accountCompany.getId(), Arrays.asList(SalesLeadRoleType.COOPERATE_ACCOUNT_MANAGER.toDbValue()));
        if (!administratorList.isEmpty()) {
            List<AccountCompanyCoAmVO> voList = new ArrayList<>();
            List<Long> userIds = administratorList.stream().map(BusinessFlowAdministrator::getUserId).collect(Collectors.toList());
            List<UserBriefDTO> userInfos = userService.getAllBriefUsersByIds(userIds).getBody();
            Map<Long, UserBriefDTO> userMap = userInfos.stream().collect(Collectors.toMap(UserBriefDTO::getId, a -> a));
            administratorList.forEach(v -> {
                AccountCompanyCoAmVO vo = new AccountCompanyCoAmVO();
                vo.setRole(v.getSalesLeadRoleType().name());
                vo.setUserId(v.getUserId());
                UserBriefDTO user = userMap.get(v.getUserId());
                vo.setFirstName(user.getFirstName());
                vo.setLastName(user.getLastName());
                vo.setUserName(user.getUsername());
                vo.setCountry(v.getCountry());
                vo.setAccountBusinessId(v.getAccountBusinessId());
                voList.add(vo);
            });

            return voList;
        }
        return new ArrayList<>();
    }

    @Override
    public AccountCompanyCoAmFteVO queryClientCompanyAndSalesLeadFte(Long companyId, Long jobId) {
        AccountCompanyCoAmFteVO vo = new AccountCompanyCoAmFteVO();
        List<AccountCompanyCoAmVO> coAmVOList = queryClientCompanyCoAm(companyId);
        if (coAmVOList.isEmpty()) {
            vo.setCompanyContainsFte(false);
        } else {
            vo.setCompanyContainsFte(true);
        }

        Optional<JobCompanyBrief> jobCompanyBriefOpt = jobCompanyBriefRepository.findById(jobId);
        if (!jobCompanyBriefOpt.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.OVERVIEW_CHECKCOMPANYEXIST_NOEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }
        JobCompanyBrief job = jobCompanyBriefOpt.get();
        if(null == job.getSalesLeadId()){
            vo.setJobContainsFte(false);
        }else{
            vo.setJobContainsFte(companyNativeRepository.searchServerTypeBySalesLeadId(companyId,job.getSalesLeadId()));
        }

        return vo;
    }

    private void updateContactCompanyName(List<CompanyMigrate> companyList) {
        List<SalesLeadClientContact> salesLeadClientContactList = salesLeadClientContactRepository.findAllByCompanyIdInAndActive(companyList.stream().map(CompanyMigrate::getId).toList(), Boolean.TRUE);
        List<Long> talentIds = salesLeadClientContactList.stream().map(SalesLeadClientContact::getTalentId).distinct().collect(Collectors.toList());
        Map<Long, String> companyNameMap = companyList.stream().collect(Collectors.toMap(CompanyMigrate::getId, CompanyMigrate::getFullBusinessName));
        if (CollUtil.isEmpty(talentIds)) {
            return;
        }
        List<TalentCompanySyncBrief> talentCompanyBriefList = talentCompanySyncBriefRepository.findAllById(talentIds);
        talentCompanyBriefList.forEach(talent -> {
            if (talent.getTalentAdditionalInfo() != null) {
                TalentAdditionalInfoSyncBrief talentAdditionalInfo = talent.getTalentAdditionalInfo();
                JSONObject additionalInfoJson = JSONUtil.parseObj(talentAdditionalInfo.getExtendedInfo());
                if (additionalInfoJson.containsKey(KEY_EXPERIENCES)) {
                    List<TalentExperienceDTO> talentExperienceDTOList = JSONUtil.toList(JSONUtil.parseArray(additionalInfoJson.get(KEY_EXPERIENCES)), TalentExperienceDTO.class);
                    talentExperienceDTOList.forEach(item -> {
                        //更新联系人companyName
                        if (companyNameMap.containsKey(item.getActiveCompanyId())) {
                            item.setCompanyName(companyNameMap.get(item.getActiveCompanyId()));
                        }
                        if (companyNameMap.containsKey(item.getActiveCRMAccountId())) {
                            item.setCompanyName(companyNameMap.get(item.getActiveCRMAccountId()));
                        }
                        //转为正式客户将activeCRMAccountId转为activeCompanyId
                        if (ObjectUtil.isNotEmpty(item.getActiveCRMAccountId())) {
                            item.setActiveCompanyId(item.getActiveCRMAccountId());
                            item.setActiveCRMAccountId(null);
                        }
                        //转为正式客户将crmAccountId转为companyId
                        if (ObjectUtil.isNotEmpty(item.getCrmAccountId())) {
                            item.setCompanyId(item.getCrmAccountId());
                            item.setCrmAccountId(null);
                        }
                    });
                    additionalInfoJson.put(KEY_EXPERIENCES, JSONUtil.parseArray(talentExperienceDTOList));
                }
                talentAdditionalInfo.setExtendedInfo(additionalInfoJson.toString());
                talent.setTalentAdditionalInfo(talentAdditionalInfo);
            }
        });
        talentCompanySyncBriefRepository.saveAll(talentCompanyBriefList);
    }

    @Override
    public List<CompanyJobVO> queryOpenJobsByCompanyId(Long companyId, Integer limit) {
        Company company = checkCompanyExist(companyId);
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createdDate"));
        TeamDataPermissionRespDTO teamDataPermission = getDataPermission();
        List<JobCompanyBrief> jobCompanyBriefList;
        if (BooleanUtils.isTrue(teamDataPermission.getAll())) {
            jobCompanyBriefList = jobCompanyBriefRepository.findAllByCompanyIdAndStatus(company.getId(), JobStatus.OPEN, pageable);
        } else if (CollectionUtils.isNotEmpty(teamDataPermission.getReadableTeamIds())) {
            jobCompanyBriefList = CollectionUtils.isEmpty(teamDataPermission.getSelfPermissionIds()) ?
                    jobCompanyBriefRepository.findAllByCompanyIdAndStatusAndPermissionTeamIdIn(company.getId(), JobStatus.OPEN, SecurityUtils.getUserId(), new ArrayList<>(teamDataPermission.getReadableTeamIds()), pageable) :
                    jobCompanyBriefRepository.findAllByCompanyIdAndStatusAndPermissionTeamIdIn(company.getId(), JobStatus.OPEN, SecurityUtils.getUserId(), new ArrayList<>(teamDataPermission.getReadableTeamIds()), teamDataPermission.getSelfPermissionIds(), pageable);
        } else if (BooleanUtils.isTrue(teamDataPermission.getSelf())) {
            jobCompanyBriefList = CollectionUtils.isEmpty(teamDataPermission.getSelfPermissionIds()) ?
                    jobCompanyBriefRepository.findAllByCompanyIdAndStatusAndPermissionUserId(company.getId(), JobStatus.OPEN, SecurityUtils.getUserId(), pageable) :
                    jobCompanyBriefRepository.findAllByCompanyIdAndStatusAndPermissionUserIdOrIdIn(company.getId(), JobStatus.OPEN, SecurityUtils.getUserId(), teamDataPermission.getSelfPermissionIds(), pageable);
        } else {
            jobCompanyBriefList = jobCompanyBriefRepository.findAllByCompanyIdAndStatus(company.getId(), JobStatus.OPEN, pageable);
        }

        List<JobLocationCompanyBrief> jobLocationCompanyBriefList = jobLocationCompanyBriefRepository.findAllByJobIdIn(jobCompanyBriefList.stream().map(JobCompanyBrief::getId).collect(Collectors.toList()));
        return toCompanyJobVO(jobCompanyBriefList, jobLocationCompanyBriefList);
    }

    private TeamDataPermissionRespDTO getDataPermission() {
        TeamDataPermissionRespDTO teamDataPermission = cachePermission.getTeamDataPermissionFromCacheOnly(SecurityUtils.getUserId());
        if (Objects.isNull(teamDataPermission)) {
            teamDataPermission = initiationService.initiateDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        }
        return teamDataPermission;
    }

    @Override
    public List<AccountCompanyVO> searchNoContractCompany(CompanySearchConditionDTO condition, Pageable pageable, HttpHeaders headers) {
        checkFoldersPermission(condition.getFolders());
        SearchGroup searchGroup = new SearchGroup();
        ServiceUtils.myCopyProperties(condition, searchGroup);
        //check search area
        SearchConditionDTO.checkPageable(pageable);
        //fix pageInfo
        pageable = PageRequest.of(pageable.getPageNumber() - 1, pageable.getPageSize(), pageable.getSort());
        //format filter
        SearchFilterDTO searchFilterDTO = new SearchFilterDTO();
        List<String> sourceList = Arrays.asList("companyName", "hasUnsignedSalesLead");
        searchFilterDTO.setSource(sourceList);
        searchFilterDTO.setQueryFilter(condition.getFilter());
        searchFilterDTO.setFolders(condition.getFolders());
        searchGroup.setIndex(Constants.INDEX_COMPANIES + SecurityUtils.getTenantId());
        searchGroup.setModule(condition.getModule().getName());
        searchGroup.setFilter(searchFilterDTO);
        searchGroup.setTimeZone(condition.getTimezone());
        searchGroup.setLanguage(condition.getLanguage().toDbValue());
        //format condition
        if (CollUtil.isNotEmpty(condition.getSearch())) {
            formatSearchGroup(condition.getSearch());
        } else {
            searchGroup.setSearch(new ArrayList<>());
        }

        HttpResponse response;
        try {
            if (condition.getUuid() == null) {
                response = esCompanyDataService.searchFromCommonService(searchGroup, pageable);
            } else {
                response = esCompanyDataService.searchUnsignedCache(condition.getUuid(), pageable);
                //cache invalidation retry
                if (response != null && response.getCode() == HttpStatus.BAD_REQUEST.value()) {
                    response = esCompanyDataService.searchFromCommonService(searchGroup, pageable);
                }
            }
        } catch (Exception e) {
            throw new CustomParameterizedException(e.getMessage());
        }
        if (response == null || response.getCode() != HttpStatus.OK.value() || ObjectUtil.isEmpty(response.getBody())) {
            headers.set("Pagination-Count", String.valueOf(0));
        } else {
            headers.set("Pagination-Count", ElasticSearchUtil.getObjectCountFromResponseHeader(response.getHeaders()) + "");
            headers.set("uuid", response.getHeaders().get("uuid"));
            return getAccountCompanyVoList(response.getBody());
        }

        return new ArrayList<>();
    }

    @Override
    public BriefCompanyDTO createEmployerTenantCompany(BriefCompanyDTO companyClientDTO) throws IOException {
        BriefCompanyDTO result = new BriefCompanyDTO();
        HttpResponse response = httpService.post(applicationProperties.getCrmUrl() + CRM_TENANT_URL, getRequestHeaders(), JsonUtil.toJson(companyClientDTO));

        if (HttpStatus.CREATED.value() == response.getCode()) {
            result = JSONUtil.toBean(response.getBody(), BriefCompanyDTO.class);
        } else {
            log.error("create tenant & company to crm failed. response: {}", response.getBody());
            throw new CustomParameterizedException("create tenant & company to crm failed.");
        }

        return result;
    }

    public Headers getRequestHeaders() {
        String currentUserToken = SecurityUtils.getCurrentUserToken();
        Map<String, String> headersBuilder = new HashMap<>();
        headersBuilder.put(HEADERS_AUTHORIZATION_KEY, TOKEN_TYPE + SPACE + currentUserToken);
        return Headers.of(headersBuilder);
    }


    private List<AccountCompanyVO> getAccountCompanyVoList(String body) {
        List<Long> companyIdList = new ArrayList<>();
        try {
            JSONArray dataArray = JSONUtil.parseArray(body);
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject jsonObject = dataArray.getJSONObject(i);
                if (jsonObject.containsKey("_id")) {
                    companyIdList.add(Long.parseLong(jsonObject.get("_id").toString()));
                }
            }
        } catch (JSONException e) {
            log.error("getCompanyData error: {}", e.getMessage());
            throw new ExternalServiceInterfaceException(e.getMessage());
        }
        if (CollUtil.isEmpty(companyIdList)) {
            return new ArrayList<>();
        }
        List<Company> companyList = companyRepository.findAllById(companyIdList);
        List<AccountCompanyVO> result = companyList.stream().map(Company::toAccountCompanyVO).collect(Collectors.toList());

        List<AccountBusiness> accountBusinessList = accountBusinessRepository.findAllByAccountCompanyIdIn(companyIdList);
        Map<Long, List<AccountBusiness>> businessMap = accountBusinessList.stream().collect(Collectors.groupingBy(AccountBusiness::getAccountCompanyId));

        List<BusinessFlowAdministrator> businessFlowAdministratorList = accountBusinessAdministratorRepository.findAllByAccountBusinessIdInAndRoleTypeForCrm(accountBusinessList.stream().map(AccountBusiness::getId).collect(Collectors.toList()), SalesLeadRoleType.SALES_LEAD_OWNER);
        Map<Long, List<BusinessFlowAdministrator>> businessFlowAdministratorMap = businessFlowAdministratorList.stream().collect(Collectors.groupingBy(BusinessFlowAdministrator::getAccountBusinessId));

        List<EntityNameVM> administratorNameList = userServiceRepository.findCrmUserNameByIds(businessFlowAdministratorList.stream().map(BusinessFlowAdministrator::getUserId).distinct().collect(Collectors.toList()));
        Map<Long, EntityNameVM> administratorNameMap = administratorNameList.stream().collect(Collectors.toMap(EntityNameVM::getId, o -> o));

        result.forEach(accountCompanyVO -> {
            if (businessMap.containsKey(accountCompanyVO.getId())) {
                List<AccountBusinessVO> businessVOList = businessMap.get(accountCompanyVO.getId()).stream().filter(o -> businessFlowAdministratorMap.containsKey(o.getId())).map(AccountBusiness::toAccountBusinessVO).
                    peek(item -> item.setSalesLeadsOwners(businessFlowAdministratorMap.get(item.getId()).stream().map(o -> new OwnerVO(o.getUserId(), CommonUtils.formatFullName(administratorNameMap.get(o.getUserId()).getFirstName(), administratorNameMap.get(o.getUserId()).getLastName()), o.getContribution())).collect(Collectors.toList()))).collect(Collectors.toList());
                accountCompanyVO.setAccountBusiness(businessVOList);
            }
        });

        return result;
    }


    private List<CompanyJobVO> toCompanyJobVO(List<JobCompanyBrief> jobCompanyBriefList, List<JobLocationCompanyBrief> jobLocationCompanyBriefList) {
        List<CompanyJobVO> result = new ArrayList<>();
        Map<Long, JobType> jobTypeMap = applicationClient.getAllMyRecruitmentProcessIds().getBody();
        Map<Long, Long> submitToClientCountMap = applicationServiceRepository.findApplicationCountByJobIds(jobCompanyBriefList.stream().map(JobCompanyBrief::getId).collect(Collectors.toList()), NodeTypeTableEnum.SUBMIT_TO_CLIENT).stream().distinct().collect(Collectors.toMap(EntityCountVM::getId, EntityCountVM::getCount));
        Map<Long, Long> interviewCountMap = applicationServiceRepository.findApplicationCountByJobIds(jobCompanyBriefList.stream().map(JobCompanyBrief::getId).collect(Collectors.toList()), NodeTypeTableEnum.INTERVIEW).stream().distinct().collect(Collectors.toMap(EntityCountVM::getId, EntityCountVM::getCount));
        Map<Long, Long> onBoardCountMap = applicationServiceRepository.findApplicationCountByJobIds(jobCompanyBriefList.stream().map(JobCompanyBrief::getId).collect(Collectors.toList()), NodeTypeTableEnum.ON_BOARD).stream().distinct().collect(Collectors.toMap(EntityCountVM::getId, EntityCountVM::getCount));
        Map<Long, List<JobLocationCompanyBrief>> jobLocationCompanyBriefMap = jobLocationCompanyBriefList.stream().collect(Collectors.groupingBy(JobLocationCompanyBrief::getJobId));
        Set<Long> privateJobTeamIds = jobCompanyBriefRepository.findPrivateJobTeamIds(SecurityUtils.getTenantId());
        jobCompanyBriefList.forEach(o -> {
            CompanyJobVO companyJobVO = CompanyJobVO.fromJobCompanyBrief(o);
            if (jobLocationCompanyBriefMap.containsKey(companyJobVO.getId())) {
                companyJobVO.setLocations(jobLocationCompanyBriefMap.get(companyJobVO.getId()).stream().map(location -> JSONUtil.toBean(location.getOriginalLoc(), LocationDTO.class)).collect(Collectors.toList()));
            }
            if (jobTypeMap != null && jobTypeMap.containsKey(o.getRecruitmentProcessId())) {
                companyJobVO.setJobType(jobTypeMap.get(o.getRecruitmentProcessId()));
            }
            companyJobVO.setSubmitToClient(submitToClientCountMap.get(companyJobVO.getId()));
            companyJobVO.setInterview(interviewCountMap.get(companyJobVO.getId()));
            companyJobVO.setOnBoard(onBoardCountMap.get(companyJobVO.getId()));
            companyJobVO.setPrivateJob(privateJobTeamIds.contains(o.getPermissionTeamId()));
            result.add(companyJobVO);
        });
        return result;
    }


    private AccountCompanyVO toVo(Company accountCompany, HttpHeaders headers) {
        AccountCompanyVO accountCompanyVO = Company.toAccountCompanyVO(accountCompany);
        Long accountCompanyId = accountCompanyVO.getId();
        SecurityContext context = SecurityContextHolder.getContext();

        CompletableFuture<String> companyNameFuture = fetchCompanyNameAsync(context, accountCompanyId);
        CompletableFuture<List<CompanyTagRelation>> tagRelationFuture = fetchTagRelationsAsync(accountCompanyId);
        CompletableFuture<List<CompanyIndustryRelation>> industryRelationFuture = fetchIndustryRelationsAsync(accountCompanyId);
        CompletableFuture<List<CompanyLocation>> locationListFuture = fetchLocationListAsync(accountCompanyId);
        CompletableFuture<CompanyAdditionalInfo> additionalInfoFuture = fetchAdditionalInfoAsync(accountCompanyId);
        CompletableFuture<List<CompanyContact>> contactInfoFuture = fetchCompanyContactAsync(accountCompanyId);
        CompletableFuture<List<AccountBusinessVO>> businessListFuture = fetchAccountBusinessAsync(context, accountCompanyId);
        CompletableFuture<List<CompanyRelationshipDTO>> relationsAsync = fetchAccountRelationshipsAsync(context, accountCompanyId);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("companyIdList", Arrays.asList(accountCompany.getId()));
        jsonObject.put("tenantId", SecurityUtils.getTenantId());
        jsonObject.put("type", "MODIFY_BUSINESS_NAME");
        HttpResponse response = sendHttpRequest(jsonObject);
        if (response.getCode() != 200) {
            log.info("[queryClientCompany] get data modify application error:{}", response.getBody());
        }

        if (StringUtils.isNotBlank(response.getBody())) {
            List<DataModifyFeedbackReportVO> dataModifyFeedbackReportVOS = JSONUtil.toList(JSONUtil.parseArray(response.getBody()), DataModifyFeedbackReportVO.class);
            if (null != dataModifyFeedbackReportVOS && !dataModifyFeedbackReportVOS.isEmpty()) {
                accountCompanyVO.setDataModifyStatus("UNPROCESSED");
                accountCompanyVO.setReplaceFullBusinessName(dataModifyFeedbackReportVOS.get(0).getReplaceFullBusinessName());
            }
        }


        return CompletableFuture.allOf(tagRelationFuture, industryRelationFuture, locationListFuture, additionalInfoFuture, contactInfoFuture, businessListFuture, relationsAsync, companyNameFuture)
                .thenApply(voidResult -> {
                    List<CompanyTagRelation> tagRelationList = tagRelationFuture.join();
                    List<CompanyIndustryRelation> industryRelationList = industryRelationFuture.join();
                    List<CompanyLocation> locationList = locationListFuture.join();
                    CompanyAdditionalInfo additionalInfo = additionalInfoFuture.join();
                    List<CompanyContact> companyContactList = contactInfoFuture.join();
                    List<AccountBusinessVO> businessVOList = businessListFuture.join();
                    List<CompanyRelationshipDTO> relationshipDTOList = relationsAsync.join();
                    String esCompanyInfo = companyNameFuture.join();
//
                    accountCompanyVO.setIndustries(industryRelationList.stream().map(CompanyIndustryRelation::getEnumId).collect(Collectors.toList()));
                    accountCompanyVO.setLocations(locationList.stream().map(o -> LocationDTO.fromOriginalLoc(o.getId(), o.getOriginalLoc())).collect(Collectors.toList()));
                    if (additionalInfo != null && additionalInfo.getExtendedInfo() != null) {
                        ServiceUtils.myCopyProperties(JSONUtil.toBean(additionalInfo.getExtendedInfo(), AccountCompanyVO.class), accountCompanyVO);
                    }
                    accountCompanyVO.setAccountBusiness(businessVOList);
                    accountCompanyVO.setContacts(companyContactList.stream().map(CompanyContact::toContactInfoVO).toList());
                    accountCompanyVO.setTags(getCompanyTags(accountCompanyId, tagRelationList, headers));
                    accountCompanyVO.setRelationshipList(relationshipDTOList);
                    setCompanyName(esCompanyInfo, accountCompanyVO);

                    return accountCompanyVO;
                })
                .join();
    }

    private HttpResponse sendHttpRequest(JSONObject jsonObject) {
        //String url = "http://localhost:9002/company/api/v3/saleslead/client-contacts/check-data-exist";
        String url = crmUrl + "/common/api/v1/feedback/data-modify/search-contact";
        log.info("[apn getContactDataExist {}] url = {}", SecurityUtils.getUserId(), url);
        Headers headers = Headers.of(Map.of(HttpHeaders.AUTHORIZATION, "Bearer " + SecurityUtils.getCurrentUserToken()));
        try {
            return httpService.post(url, headers, jsonObject.toString());
        } catch (IOException e) {
            log.error("[apn getContactDataExist {}] search is error, message = {}", SecurityUtils.getUserId(), ExceptionUtil.getAllExceptionMsg(e));
            throw new RuntimeException(e);
        }
    }

    /**
     * 从es中获取companyName和displayNameInEn
     */

    private void setCompanyName(String esCompanyInfo, AccountCompanyVO accountCompanyVO) {
        JSONArray jsonArray = JSONUtil.parseArray(esCompanyInfo);
        if(jsonArray == null || jsonArray.size() == 0 ) {
            return;
        }
        JSONObject jsonObject = jsonArray.getJSONObject(0);
        JSONObject source = jsonObject.getJSONObject("_source");
        if(source != null) {
            accountCompanyVO.setCompanyName(source.getStr("companyName"));
            accountCompanyVO.setDisplayNameInEn(source.getStr("displayNameInEn"));
        }
    }

    private CompletableFuture<String> fetchCompanyNameAsync(SecurityContext context, Long accountCompanyId) {
        return CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return searchCompanyById(accountCompanyId.toString());
        });
    }

    private List<SearchParam> getAccountCompanyIdSearch(String accountCompanyId) {
        List<SearchParam> ret = new ArrayList<>();
        SearchParam searchParam = new SearchParam();
        searchParam.setRelation(Relation.AND);
        List<ConditionParam> conditionParams = new ArrayList<>();
        ConditionParam condition = new ConditionParam();
        condition.setKey("id");
        JSONObject value = new JSONObject();
        value.put("data", accountCompanyId);
        condition.setValue(value);
        conditionParams.add(condition);
        searchParam.setCondition(conditionParams);
        ret.add(searchParam);
        return ret;
    }

    private String searchCompanyById(String accountCompanyId) {
        Pageable pageable = Pageable.ofSize(5);
        SearchGroup searchGroup = createSearchGroup(accountCompanyId);
        HttpResponse response;
        try {
            response = esCompanyDataService.searchCacheAndRefreshCache(searchGroup, pageable);
        } catch (Exception e) {
            throw new CustomParameterizedException(e.getMessage());
        }

        if (response.getCode() == HttpStatus.NOT_FOUND.value()) {
            return "[]";
        } else if (response.getCode() != HttpStatus.OK.value()) {
            throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
        } else if (ObjectUtil.isEmpty(response.getBody()) || response.getCode() == HttpStatus.NOT_FOUND.value()) {
            return "[]";
        }
        String body = response.getBody();
        return body;
    }

    private SearchGroup createSearchGroup(String accountCompanyId) {
        SearchGroup searchGroup = new SearchGroup();
        //format filter
        SearchFilterDTO searchFilterDTO = new SearchFilterDTO();

        searchFilterDTO.setSource(ElasticSearchConstants.COMPANY_CLIENT_SEARCH_SOURCE);
        searchFilterDTO.setQueryFilter(List.of());
        searchGroup.setIndex(Constants.INDEX_COMPANIES + SecurityUtils.getTenantId());
        searchGroup.setModule(ModuleType.COMPANY_POOL.getName());
        searchGroup.setFilter(searchFilterDTO);
        searchGroup.setTimeZone("Asia/Shanghai");
        searchGroup.setLanguage(LanguageEnum.ZH.toDbValue());
        searchGroup.setSearch(getAccountCompanyIdSearch(accountCompanyId));

        return searchGroup;
    }

    private CompletableFuture<List<CompanyRelationshipDTO>> fetchAccountRelationshipsAsync(SecurityContext context, Long accountCompanyId) {
        String currentUserToken = SecurityUtils.getCurrentUserToken();
        return CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.set("Authorization", TOKEN_TYPE + " " + currentUserToken);
            httpHeaders.set("Content-Type", "application/json");
            try {
                HttpResponse response = httpService.get(applicationProperties.getCrmUrl() + getCompanyUrl(accountCompanyId), convertToOkHttpHeaders(httpHeaders));
                if (response == null || 200 != response.getCode()) {
                    log.error("inactivate crm user error, response: {}", response);
                }
                CRMAccountCompany bean = JSONUtil.toBean(response.getBody(), CRMAccountCompany.class);
                return bean != null ? bean.getRelationshipList() : new ArrayList<>();
            } catch (Exception e) {
                e.printStackTrace();
                log.error("inactivate crm user error: {}", e.getMessage());
            }
            return new ArrayList<>();
        });
    }

    private String getCompanyUrl(Long companyId) {
        return String.format("/account/api/v1/companies/%s/basic", companyId);
    }

    private List<TagVO> getCompanyTags(Long companyId, List<CompanyTagRelation> tagRelationList, HttpHeaders headers) {
        if (CollUtil.isEmpty(tagRelationList)) {
            return new ArrayList<>();
        }
        JSONArray crmCompanyTags = new JSONArray();
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.set("Authorization", headers.getFirst(HttpHeaders.AUTHORIZATION));
            httpHeaders.set("Content-Type", "application/json");
            HttpResponse response = httpService.get(applicationProperties.getCrmUrl() + crmCompanyTagsUrl(companyId), convertToOkHttpHeaders(httpHeaders));
            if (response != null && 200 == response.getCode()) {
                crmCompanyTags = new JSONArray(response.getBody());
            } else {
                throw new CustomParameterizedException(response.getBody());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("query crm company tags error: {}", e.getMessage());
        }
        final Set<String> authorizedTags = crmCompanyTags.stream().map(String::valueOf).collect(Collectors.toSet());
        log.info("authorizedTags: {}", JSONUtil.toJsonStr(authorizedTags));
        return tagRelationList.stream().map(o -> {
            TagVO tagVO = new TagVO();
            tagVO.setContent(o.getTag());
            tagVO.setLastUseDate(o.getCreatedDate());
            tagVO.setCreatedBy(o.getCreatedBy());
            tagVO.setDeletable(authorizedTags.contains(o.getTag()));
            return tagVO;
        }).collect(Collectors.toList());
    }

    private Company checkCompanyExist(Long id) {
        Company company = companyRepository.findById(id).orElseThrow(() -> new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.OVERVIEW_CHECKCOMPANYEXIST_NOEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService())));
        if (!company.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.OVERVIEW_CHECKCOMPANYEXIST_NOEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        return company;
    }

    private CompletableFuture<List<CompanyTagRelation>> fetchTagRelationsAsync(Long accountCompanyId) {
        return CompletableFuture.supplyAsync(() ->
                companyTagRelationRepository.findAllByAccountCompanyId(accountCompanyId)
        );
    }

    private CompletableFuture<List<CompanyIndustryRelation>> fetchIndustryRelationsAsync(Long accountCompanyId) {
        return CompletableFuture.supplyAsync(() ->
                companyIndustryRelationRepository.findAllByAccountCompanyId(accountCompanyId)
        );
    }

    private CompletableFuture<List<CompanyLocation>> fetchLocationListAsync(Long accountCompanyId) {
        return CompletableFuture.supplyAsync(() ->
                companyLocationRepository.findAllByAccountCompanyId(accountCompanyId)
        );
    }

    private CompletableFuture<CompanyAdditionalInfo> fetchAdditionalInfoAsync(Long accountCompanyId) {
        return CompletableFuture.supplyAsync(() ->
                companyAddtionalInfoRepository.findByAccountCompanyId(accountCompanyId)
        );
    }

    private CompletableFuture<List<CompanyContact>> fetchCompanyContactAsync(Long accountCompanyId) {
        return CompletableFuture.supplyAsync(() ->
                companyContactRepository.findAllByCompanyId(accountCompanyId)
        );
    }

    private CompletableFuture<List<AccountBusinessVO>> fetchAccountBusinessAsync(SecurityContext context, Long accountCompanyId) {
        return CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return accountBusinessService.queryBusinessListByCompanyId(accountCompanyId);
        });
    }


    @Override
    @Async
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public void replenishContractBusiness() {
        Instant start = Instant.now();
        List<Contract> contractList = contractRepository.findNoBusinessContractList();
        List<Company> companyList = companyRepository.findAll();
        List<Long> contractIds = contractList.stream().map(Contract::getId).toList();
        List<ContractSigner> contractSignerList = contractSignerRepository.findAllByContractIdIn(contractIds);
        List<ContractServiceType> contractServiceTypeList = contractServiceTypeRepository.findAllByContractIdIn(contractIds);
        Map<Long, Company> companyMap = companyList.stream().collect(Collectors.toMap(Company::getId, o -> o));
        Map<Long, List<ContractSigner>> contractSignerMap = contractSignerList.stream().collect(Collectors.groupingBy(ContractSigner::getContractId));
        Map<Long, List<ContractServiceType>> contractServiceTypeMap = contractServiceTypeList.stream().collect(Collectors.groupingBy(ContractServiceType::getContractId));

        Long maxContractBusinessId = contractBusinessRelationRepository.maxId();
        Long maxBusinessAdminId = accountBusinessAdministratorRepository.maxId();
        Long maxBusinessServiceTypeId = accountBusinessServiceTypeRelationRepository.maxId();

        contractList = contractList.stream().filter(o -> companyMap.containsKey(o.getAccountCompanyId())).toList();
        log.info("replenish business start : {}, total: {}", start, contractList.size());
        for (int i = 0 ; i < contractList.size() ; i++) {
            Contract item = contractList.get(i);
            Company company = companyMap.get(item.getAccountCompanyId());
            CompanySalesLead companySalesLead = new CompanySalesLead();
            companySalesLead.setAccountProgress(BigDecimal.valueOf(1.20));
            companySalesLead.setAccountCompanyId(item.getAccountCompanyId());
            companySalesLead.setCreatedBy(item.getCreatedBy());
            companySalesLead.setCreatedDate(item.getCreatedDate());
            companySalesLead.setLastModifiedBy(item.getLastModifiedBy());
            companySalesLead.setLastModifiedDate(item.getLastModifiedDate());
            companySalesLead.setPermissionTeamId(company.getPermissionTeamId());
            companySalesLead.setPermissionUserId(company.getPermissionUserId());
            companySalesLead = companySalesLeadRepository.save(companySalesLead);
            AccountBusiness accountBusiness = new AccountBusiness();
            accountBusiness.setId(companySalesLead.getId());
            accountBusiness.setTenantId(company.getTenantId());
            accountBusiness.setBusinessProgress(BusinessProgress.ACCOUNT_BUSINESS_CONTRACTED.toDbValue());
            accountBusiness.setAccountCompanyId(item.getAccountCompanyId());
            accountBusiness.setCreatedBy(item.getCreatedBy());
            accountBusiness.setCreatedDate(item.getCreatedDate());
            accountBusiness.setLastModifiedBy(item.getLastModifiedBy());
            accountBusiness.setLastModifiedDate(item.getLastModifiedDate());
            accountBusiness.setPermissionTeamId(company.getPermissionTeamId());
            accountBusiness.setPermissionUserId(company.getPermissionUserId());
            accountBusiness = accountBusinessRepository.save(accountBusiness);
            ContractBusinessRelation contractBusinessRelation = new ContractBusinessRelation();
            maxContractBusinessId++;
            contractBusinessRelation.setId(maxContractBusinessId);
            contractBusinessRelation.setAccountBusinessId(accountBusiness.getId());
            contractBusinessRelation.setContractId(item.getId());
            contractBusinessRelationRepository.save(contractBusinessRelation);
            if (contractSignerMap.containsKey(item.getId())) {
                List<BusinessFlowAdministrator> businessFlowAdministratorList = new ArrayList<>();
                List<ContractSigner> signerList = contractSignerMap.get(item.getId());
                for (ContractSigner signer : signerList) {
                    BusinessFlowAdministrator businessFlowAdministrator = new BusinessFlowAdministrator();
                    maxBusinessAdminId++;
                    businessFlowAdministrator.setId(maxBusinessAdminId);
                    businessFlowAdministrator.setAccountBusinessId(accountBusiness.getId());
                    businessFlowAdministrator.setUserId(signer.getSignerId());
                    businessFlowAdministrator.setSalesLeadRoleType(SalesLeadRoleType.ACCOUNT_MANAGER);
                    businessFlowAdministrator.setCompanyId(company.getId());
                    businessFlowAdministrator.setCreatedBy(item.getCreatedBy());
                    businessFlowAdministrator.setCreatedDate(item.getCreatedDate());
                    businessFlowAdministrator.setLastModifiedBy(item.getLastModifiedBy());
                    businessFlowAdministrator.setLastModifiedDate(item.getLastModifiedDate());
                    businessFlowAdministrator.setPermissionTeamId(company.getPermissionTeamId());
                    businessFlowAdministrator.setPermissionUserId(company.getPermissionUserId());
                    businessFlowAdministratorList.add(businessFlowAdministrator);
                }
                accountBusinessAdministratorRepository.saveAll(businessFlowAdministratorList);
            }
            if (contractServiceTypeMap.containsKey(item.getId())) {
                List<AccountBusinessServiceTypeRelation> businessServiceTypeList = new ArrayList<>();
                List<ContractServiceType> serviceTypeList = contractServiceTypeMap.get(item.getId());
                for (ContractServiceType contractServiceType : serviceTypeList) {
                    AccountBusinessServiceTypeRelation businessServiceTypeRelation = new AccountBusinessServiceTypeRelation();
                    maxBusinessServiceTypeId++;
                    businessServiceTypeRelation.setId(maxBusinessServiceTypeId);
                    businessServiceTypeRelation.setAccountBusinessId(accountBusiness.getId());
                    businessServiceTypeRelation.setEnumId(contractServiceType.getServiceTypeId());
                    businessServiceTypeList.add(businessServiceTypeRelation);
                }
                accountBusinessServiceTypeRelationRepository.saveAll(businessServiceTypeList);
            }
            log.info("contract date index: {}", i);
        }
        Instant end = Instant.now();
        log.info("replenish business end: {}", end);
    }

    @Override
    @Async
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public void migrateCrmTags(String authorizationHeader) {
        Instant start = Instant.now();
        log.info("migrate crm tags start : {}, url:{}, headers:{}", start, applicationProperties.getCrmUrl(), getRequestHeaders(authorizationHeader));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", authorizationHeader);
        httpHeaders.set("Content-Type", "application/json");
        try {
            HttpResponse response = httpService.get(applicationProperties.getCrmUrl() + CRM_QUERY_TAGS_URL, convertToOkHttpHeaders(httpHeaders));
            if (response != null && 200 == response.getCode()) {
                JSONObject tagsData = JSONUtil.parseObj(response.getBody());
                if (tagsData.containsKey("companyTags")) {
                    List<CompanyTagRelation> companyTagRelationList = tagsData.getJSONArray("companyTags").toList(CompanyTagRelation.class);
                    companyTagRelationRepository.saveAll(companyTagRelationList);
                }
                if (tagsData.containsKey("contactTags")) {
                    List<ContactTagRelation> contactTagRelationList = tagsData.getJSONArray("contactTags").toList(ContactTagRelation.class);
                    contactTagRelationRepository.saveAll(contactTagRelationList);
                }
            } else {
                throw new CustomParameterizedException(response.getBody());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("migrate crm tags error: {}", e.getMessage());
        }
        log.info("migrate crm tags end : {}", Instant.now());
    }

    @Override
    @Async
    @Deprecated
    public void migrateCompanyToCrm(String authorizationHeader) {
        Instant start = Instant.now();
        int page = 0;
        int size = 10;
        List<Long> failedCompanyIds = new ArrayList<>();
        Map<String, Integer> businessProgressMap = new HashMap<>();
        businessProgressMap.put("0.00", BusinessProgress.ACCOUNT_BUSINESS_CUSTOMER_NOT_CONTACTED.toDbValue());
        businessProgressMap.put("0.25", BusinessProgress.ACCOUNT_BUSINESS_PLAN_COMMUNICATION.toDbValue());
        businessProgressMap.put("0.50", BusinessProgress.ACCOUNT_BUSINESS_PLAN_VERIFICATION.toDbValue());
        businessProgressMap.put("0.75", BusinessProgress.ACCOUNT_BUSINESS_NEGOTIATION.toDbValue());
        businessProgressMap.put("1.00", BusinessProgress.ACCOUNT_BUSINESS_CONTRACTED.toDbValue());
        businessProgressMap.put("1.20", BusinessProgress.ACCOUNT_BUSINESS_CONTRACTED.toDbValue());
        List<UserCompanyMigrateBrief> userList = userServiceRepository.findAll();
        Set<Long> firstUserIds = userList.stream()
                .collect(Collectors.groupingBy(UserCompanyMigrateBrief::getTenantId))
                .values().stream()
                .map(list -> list.stream().min(Comparator.comparing(UserCompanyMigrateBrief::getId)).get().getId())
                .collect(Collectors.toSet());
        List<Tenant> tenantList = userServiceRepository.findAllTenant();
        List<PermissionTeam> permissionTeamList = userServiceRepository.findAllPermissionTeam();
        List<PermissionTeamLeader> permissionTeamLeaderList = userServiceRepository.findAllPermissionTeamLeader();
        List<PermissionUserTeam> permissionUserTeamList = userServiceRepository.findAllPermissionUserTeam();
        Set<Long> syncedTalentIds = new HashSet<>();
        Map<Long, String> userNameMap = userList.stream().collect(Collectors.toMap(UserCompanyMigrateBrief::getId, o -> CommonUtils.formatFullName(o.getFirstName(), o.getLastName())));
        long total = companyRepository.count();
        log.info("synchronize apn company data to crm start : {}, total: {}, url:{}, headers:{}", start, total, applicationProperties.getCrmUrl(), getRequestHeaders(authorizationHeader));
        while (true) {
            Pageable pageable = PageRequest.of(page, size);
            Page<CompanyMigrate> companyPage = companyRepository.findAllCompanyMigrate(pageable);
            List<CompanyMigrate> companyList = companyPage.getContent();
            List<Long> companyIds = companyList.stream().map(CompanyMigrate::getId).toList();
            Map<Long, String> companyNameMap = companyList.stream().collect(Collectors.toMap(CompanyMigrate::getId, CompanyMigrate::getFullBusinessName));
            try {
                List<CompanyContact> companyContactList = companyContactRepository.findAllByCompanyIdIn(companyIds);
                List<CompanySalesLead> companySalesLeadList = companySalesLeadRepository.findAllByAccountCompanyIdIn(companyIds);
                List<Long> businessIds = companySalesLeadList.stream().map(CompanySalesLead::getId).toList();
                Set<Long> clientCompanyIds = companySalesLeadList.stream().filter(o -> BigDecimal.valueOf(1.20).compareTo(o.getAccountProgress()) == 0).map(CompanySalesLead::getAccountCompanyId).collect(Collectors.toSet());
                List<CompanyAdditionalInfo> additionalInfoList = companyAddtionalInfoRepository.findAllByAccountCompanyIdIn(companyIds);
                List<CompanyIndustryRelation> companyIndustryRelationList = companyIndustryRelationRepository.findAllByAccountCompanyIdIn(companyIds);
                List<CompanyLocation> locationList = companyLocationRepository.findAllByAccountCompanyIdIn(companyIds);
                List<AccountBusinessServiceTypeRelation> businessServiceTypeRelationList = accountBusinessServiceTypeRelationRepository.findAllByAccountBusinessIdIn(businessIds);
                List<BusinessFlowAdministrator> businessFlowAdministratorList = accountBusinessAdministratorRepository.findAllByAccountBusinessIdIn(businessIds);
                List<SalesLeadClientContact> clientContactList = salesLeadClientContactRepository.findAllByCompanyIdIn(companyIds);
                List<ContactTagRelation> contactTagRelationList = contactTagRelationRepository.findAllByContactIdIn(clientContactList.stream().map(SalesLeadClientContact::getId).toList());
                Map<Long, List<ContactTagRelation>> contactTagMap = contactTagRelationList.stream().collect(Collectors.groupingBy(ContactTagRelation::getContactId));
                Map<Long, Long> contactMap = clientContactList.stream().collect(Collectors.groupingBy(SalesLeadClientContact::getTalentId, Collectors.collectingAndThen(Collectors.toList(), list -> list.get(0).getCrmContactId())));
                List<SalesLeadClientContactAdditionalInfo> clientContactAdditionalInfoList = salesLeadClientContactAddtionalInfoRepository.findByCompanyContactIdIn(clientContactList.stream().map(SalesLeadClientContact::getId).toList());
                Map<Long, String> salesLeadClientContactAdditionalInfoMap = clientContactAdditionalInfoList.stream().collect(Collectors.toMap(SalesLeadClientContactAdditionalInfo::getCompanyContactId, SalesLeadClientContactAdditionalInfo::getExtendedInfo));
                List<AccountBusinessContactRelation> accountBusinessContactRelationList = accountBusinessContactRelationRepository.findAllByAccountBusinessIdIn(businessIds);
                List<Contract> contractList = contractRepository.findAllByAccountCompanyIdIn(companyIds);
                List<Long> contractIds = contractList.stream().map(Contract::getId).toList();
                List<ContractBusinessRelation> contractBusinessRelationList = contractBusinessRelationRepository.findAllByContractIdIn(contractIds);
                List<ContractSigner> contractSignerList = contractSignerRepository.findAllByContractIdIn(contractIds);
                List<ContractServiceType> contractServiceTypeList = contractServiceTypeRepository.findAllByContractIdIn(contractIds);
                List<Long> talentIds = clientContactList.stream().map(SalesLeadClientContact::getTalentId).toList();
                List<TalentCompanyMigrateBrief> talentCompanyBriefList = talentServiceRepository.findAllByIds(talentIds);
                Map<Long, TalentCompanyMigrateBrief> talentAdditionalInfoMap = talentCompanyBriefList.stream().collect(Collectors.toMap(TalentCompanyMigrateBrief::getId, o -> o));
                List<Long> firstSyncTalentIds = talentIds.stream().filter(o -> !syncedTalentIds.contains(o)).toList();
                List<TalentContact> talentContactList = talentContactCompanyBriefRepository.findAllByTalentIdInAndStatus(firstSyncTalentIds, TalentContactStatus.AVAILABLE);
                List<TalentCurrentLocationCompanyBrief> talentCurrentLocationCompanyBriefList = talentCurrentLocationCompanyBriefRepository.findAllByTalentIdIn(firstSyncTalentIds);
                syncedTalentIds.addAll(firstSyncTalentIds);
                Map<Long, Long> companyTenantMap = companyList.stream().collect(Collectors.toMap(CompanyMigrate::getId, CompanyMigrate::getTenantId));
                List<CompanyProgressNote> companyProgressNoteList = companyProgressNoteRepository.findAllByCompanyIdIn(companyIds);
                List<Long> noteIds = companyProgressNoteList.stream().map(CompanyProgressNote::getId).toList();
                List<CompanyProgressNoteContactRelation> companyProgressNoteContactRelationList = companyProgressNoteContactRelationRepository.findAllByNoteIdIn(noteIds);
                Map<Long, Long> noteContactMap = clientContactList.stream().collect(Collectors.groupingBy(SalesLeadClientContact::getId, Collectors.collectingAndThen(Collectors.toList(), list -> list.get(0).getCrmContactId())));
                Map<Long, List<Long>> noteContactRelationMap = companyProgressNoteContactRelationList.stream().collect(Collectors.groupingBy(CompanyProgressNoteContactRelation::getNoteId, Collectors.collectingAndThen(Collectors.toList(), list -> list.stream().map(o -> noteContactMap.get(o.getClientContactId())).toList())));
                JSONObject jsonObject = new JSONObject();
                jsonObject.put(COMPANY, companyList.stream().map(o -> {
                    JSONObject item = JSONUtil.parseObj(o);
                    item.put("apnSyncFlag", clientCompanyIds.contains(o.getId()));
                    return item;
                }).toList());
                jsonObject.put(COMPANY_CONTACT, companyContactList);
                jsonObject.put(COMPANY_ADDITIONAL_INFO, additionalInfoList.stream().peek(o -> {
                    JSONObject item = JSONUtil.parseObj(o.getExtendedInfo());
                    o.setExtendedInfo(JsonUtil.toJson(item));
                }).toList());
                jsonObject.put(INDUSTRY, companyIndustryRelationList);
                jsonObject.put(LOCATION, locationList);
                jsonObject.put(BUSINESS, companySalesLeadList.stream().map(o -> {
                    JSONObject item = JSONUtil.parseObj(o);
                    item.put("businessProgress", businessProgressMap.get(o.getAccountProgress().toString()));
                    item.put("tenantId", companyTenantMap.get(o.getAccountCompanyId()));
                    if (BigDecimal.valueOf(1.20).compareTo(o.getAccountProgress()) == 0) {
                        item.put("apnSyncFlag", true);
                    }
                    return item;
                }).toList());
                jsonObject.put(SERVICE_TYPE_RELATION, businessServiceTypeRelationList);
                jsonObject.put(ADMINISTRATOR, businessFlowAdministratorList.stream().map(o -> {
                    JSONObject item = JSONUtil.parseObj(o);
                    item.put("accountCompanyId", o.getCompanyId());
                    return item;
                }).toList());
                jsonObject.put(CLIENT_CONTACT, clientContactList.stream().map(o -> {
                    JSONObject item = JSONUtil.parseObj(o);
                    if (salesLeadClientContactAdditionalInfoMap.containsKey(o.getId())) {
                        item.putAll(JSONUtil.parseObj(salesLeadClientContactAdditionalInfoMap.get(o.getId())));
                    }
                    item.put("apnSyncFlag", true);
                    item.put("contactId", o.getCrmContactId());
                    item.put("accountCompanyId", o.getCompanyId());
                    if (talentAdditionalInfoMap.containsKey(o.getTalentId())) {
                        setContactTitleAndDepartMent(item, talentAdditionalInfoMap.get(o.getTalentId()));
                    }
                    JSONObject contactAdditionalInfo = new JSONObject();
                    contactAdditionalInfo.put("businessGroup", item.getStr("businessGroup"));
                    contactAdditionalInfo.put("businessUnit", item.getStr("businessUnit"));
                    contactAdditionalInfo.put("department", item.getStr("department"));
                    item.put("extendedInfo", contactAdditionalInfo);
                    return item;
                }).toList());
                jsonObject.put(BUSINESS_CONTACT_RELATION, accountBusinessContactRelationList.stream().map(o -> {
                    JSONObject item = JSONUtil.parseObj(o);
                    item.put("account_contact_id", o.getClientContactId());
                    return item;
                }).toList());

                jsonObject.put(CONTRACT_RELATION, contractBusinessRelationList);
                jsonObject.put(CONTRACT, contractList.stream().map(o -> {
                    JSONObject item = JSONUtil.parseObj(o);
                    item.put("apnSyncFlag", true);
                    return item;
                }).toList());
                jsonObject.put(CONTRACT_SINGER, contractSignerList);
                jsonObject.put(CONTRACT_SERVICE_TYPE, contractServiceTypeList);

                jsonObject.put(CRM_CONTACT, talentCompanyBriefList.stream().map(o -> {
                    JSONObject item = JSONUtil.parseObj(o);
                    item.remove("talentAdditionalInfo");
                    item.put("id", contactMap.get(o.getId()));
                    item.put("apnTalentId", o.getId());
                    item.put("imageUrl", o.getPhotoUrl());
                    return item;
                }).toList());
                jsonObject.put(CONTACT_INFO, talentContactList.stream().map(o -> {
                    JSONObject item = JSONUtil.parseObj(o);
                    item.remove("id");
                    item.put("type", o.getType().toDbValue());
                    item.put("contact_id", contactMap.get(o.getTalentId()));
                    return item;
                }).toList());
                jsonObject.put(CONTACT_LOCATION, talentCurrentLocationCompanyBriefList.stream().map(o -> {
                    JSONObject item = JSONUtil.parseObj(o);
                    item.remove("id");
                    item.put("contact_id", contactMap.get(o.getTalentId()));
                    return item;
                }).toList());
                jsonObject.put(PROGRESS_NOTE, companyProgressNoteList.stream().map(o -> {
                    JSONObject item = new JSONObject();
                    item.put("category", "BUSINESS_RECORD");
                    item.put("action", "NORMAL");
                    item.put("accountBusinessId", o.getSalesLeadId());
                    item.put("accountCompanyId", o.getCompanyId());
                    item.put("accountContactId", noteContactRelationMap.get(o.getId()));
                    item.put("model", "ACCOUNT_SERVICE");
                    item.put("companyName", companyNameMap.get(o.getCompanyId()));
                    item.put("contactTime", o.getContactDate());
                    item.put("contactType", o.getContactType().toDbValue());
                    item.put("note", o.getNote());
                    item.put("@timestamp", o.getCreatedDate());
                    item.put("createdBy", o.getPermissionUserId());
                    item.put("username", userNameMap.get(o.getPermissionUserId()));
                    item.put("createdDate", o.getCreatedDate());
                    item.put("id", o.getId());
                    item.put("lastModifiedBy", o.getLastModifiedBy());
                    item.put("lastModifiedDate", o.getLastModifiedDate());
                    return item;
                }).toList());
                HttpResponse response = httpService.post(applicationProperties.getCrmUrl() + CRM_MIGRATE_URL, JsonUtil.toJson(jsonObject));
                if (response == null || response.getCode() != HttpStatus.CREATED.value()) {
                    throw new CustomParameterizedException("call crm api error. response : " + response);
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("sync apn data to crm error: {}, companyIds: {}", e.getMessage(), companyIds);
                failedCompanyIds.addAll(companyIds);
            }

            log.info("apn company data page = {}, size = {}", page + 1, companyList.size());
            total += companyList.size();

            if (CollUtil.isEmpty(companyList)) {
                break;
            }
            page++;
        }
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(USER, userList.stream().map(o -> {
                JSONObject item = JSONUtil.parseObj(o);
                item.put("fullName", CommonUtils.formatFullName(o.getFirstName(), o.getLastName()));
                item.put("status", o.isActivated() ? "ACTIVE" : "INACTIVE");
                item.remove("tenant");
                return item;
            }).toList());
            jsonObject.put(USER_ADMIN, firstUserIds);
            jsonObject.put(TENANT, tenantList);
            jsonObject.put(TEAM, permissionTeamList);
            jsonObject.put(TEAM_LEADER, permissionTeamLeaderList);
            jsonObject.put(TEAM_USER, permissionUserTeamList);
            HttpResponse response = httpService.post(applicationProperties.getCrmUrl() + CRM_MIGRATE_URL, JsonUtil.toJson(jsonObject));
            if (response == null || response.getCode() != HttpStatus.CREATED.value()) {
                throw new CustomParameterizedException("call crm api error. response : " + response);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("sync apn user & tenant data to crm error: {}", e.getMessage());
        }

        Instant end = Instant.now();
        log.info("synchronize apn company data to crm end : {}", end);
        log.info("failed company: {}", failedCompanyIds);
    }

    private void callCrmApi(String authorizationHeader, JSONObject jsonObject) {
        try {
            HttpResponse response = httpService.post(applicationProperties.getCrmUrl() + CRM_MIGRATE_URL, JsonUtil.toJson(jsonObject));
            if (response == null || response.getCode() != HttpStatus.CREATED.value()) {
                throw new CustomParameterizedException("call crm api error. response : " + response);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("sync apn user & tenant data to crm error: {}", e.getMessage());
        }
    }

    public Headers getRequestHeaders(String authorizationHeader) {
        Map<String, String> headersBuilder = new HashMap<>();
        headersBuilder.put(HEADERS_AUTHORIZATION_KEY, authorizationHeader);
        return Headers.of(headersBuilder);
    }


    public static Headers convertToOkHttpHeaders(HttpHeaders httpHeaders) {
        Headers.Builder builder = new Headers.Builder();
        httpHeaders.forEach((key, values) -> {
            for (String value : values) {
                builder.add(key, value);
            }
        });
        return builder.build();
    }

    private void setContactTitleAndDepartMent(JSONObject item, TalentCompanyMigrateBrief talentCompanyBrief) {
        if (talentCompanyBrief.getTalentAdditionalInfo() != null && talentCompanyBrief.getTalentAdditionalInfo().getExtendedInfo() != null) {
            JSONObject talentAdditionalInfo = JSONUtil.parseObj(talentCompanyBrief.getTalentAdditionalInfo().getExtendedInfo());
            if (talentAdditionalInfo.containsKey(KEY_EXPERIENCES)) {
                List<TalentExperienceDTO> talentExperienceDTOList = JSONUtil.toList(JSONUtil.parseArray(talentAdditionalInfo.get(KEY_EXPERIENCES)), TalentExperienceDTO.class);
                for (TalentExperienceDTO talentExperienceDTO : talentExperienceDTOList) {
                    if (talentExperienceDTO.getTitle() == null) {
                        continue;
                    }
                    if (talentExperienceDTO.getActiveCompanyId() != null && talentExperienceDTO.getActiveCompanyId().equals(item.getLong("companyId"))) {
                        item.put("title", talentExperienceDTO.getTitle());
                        item.put("department", talentExperienceDTO.getDepartment());
                        break;
                    } else if (talentExperienceDTO.getCompanyId() != null && talentExperienceDTO.getCompanyId().equals(item.getLong("companyId"))) {
                        item.put("title", talentExperienceDTO.getTitle());
                        item.put("department", talentExperienceDTO.getDepartment());
                        break;
                    }
                }
            }
        }
    }


    private Map<String, Pair<String,String>>  initCompanyInfo(){
        Map<String,Pair<String,String>> companyIdMap = new HashMap<>();

        companyIdMap.put("1931",new Pair<>("1608","飞协博国际货运代理（上海）有限公司"));
        companyIdMap.put("323",new Pair<>("133","阿里巴巴（中国）有限公司"));
        companyIdMap.put("429",new Pair<>("430","闻泰科技股份有限公司"));
        companyIdMap.put("285",new Pair<>("1242","返利网数字科技股份有限公司"));
        companyIdMap.put("1409",new Pair<>("1878","菜鸟网络科技有限公司"));
        companyIdMap.put("2365",new Pair<>("843","荣耀终端有限公司"));
        companyIdMap.put("2774",new Pair<>("1433","英矽智能科技（上海）有限公司"));
        companyIdMap.put("12774",new Pair<>("11433","英矽智能科技（上海）有限公司"));
        companyIdMap.put("213",new Pair<>("499","美团科技有限公司"));
        companyIdMap.put("1338",new Pair<>("2479","罗布乐思（深圳）数码科技有限公司"));
        companyIdMap.put("113,1828",new Pair<>("298","禾多科技（广州）有限公司"));
        companyIdMap.put("435",new Pair<>("2825","石药控股集团有限公司"));
        companyIdMap.put("412",new Pair<>("13735","灵动科技（安徽）有限公司"));
        companyIdMap.put("398",new Pair<>("397","湖南快乐阳光互动娱乐传媒有限公司"));
        companyIdMap.put("337",new Pair<>("1354","深圳麦克韦尔科技有限公司"));
        companyIdMap.put("479",new Pair<>("460","深圳市汇顶科技股份有限公司"));
        companyIdMap.put("470",new Pair<>("13536","深圳市万邑通信息科技有限公司"));
        companyIdMap.put("1734",new Pair<>("13547","深圳地平线机器人科技有限公司"));
        companyIdMap.put("1339",new Pair<>("2042","江苏亚盛医药开发有限公司"));
        companyIdMap.put("11339",new Pair<>("12042","江苏亚盛医药开发有限公司"));
        companyIdMap.put("970",new Pair<>("13783","武汉市聚芯微电子有限责任公司"));
        companyIdMap.put("2084",new Pair<>("2340","梅卡曼德（北京）机器人科技有限公司"));
        companyIdMap.put("207",new Pair<>("200","杭州乒乓智能技术有限公司"));
        companyIdMap.put("384",new Pair<>("2114","昆仑万维科技股份有限公司"));
        companyIdMap.put("2178",new Pair<>("2167","捷思英达医药技术（上海）有限公司"));
        companyIdMap.put("12178",new Pair<>("12167","捷思英达医药技术（上海）有限公司"));
        companyIdMap.put("449",new Pair<>("450","成都创人所爱科技股份有限公司"));
        companyIdMap.put("1160",new Pair<>("2379","康龙化成（西安）新药技术有限公司"));
        companyIdMap.put("827",new Pair<>("2697","同盾科技有限公司"));
        companyIdMap.put("841",new Pair<>("13865","华润五丰(中国)投资有限公司"));
        companyIdMap.put("389",new Pair<>("1195","华东医药股份有限公司"));
        companyIdMap.put("1500",new Pair<>("2584","北京衔远科技有限公司"));
        companyIdMap.put("880",new Pair<>("12860","北京莱熙科技有限公司"));
        companyIdMap.put("288",new Pair<>("266","北京网聘信息技术有限公司（智联招聘）"));
        companyIdMap.put("707",new Pair<>("745","北京畅游时代数码技术有限公司"));
        companyIdMap.put("743",new Pair<>("1888","北京滴普科技有限公司"));
        companyIdMap.put("1373",new Pair<>("746","北京平凯星辰科技发展有限公司"));
        companyIdMap.put("699",new Pair<>("760","北京仁科互动网络技术有限公司"));
        companyIdMap.put("11",new Pair<>("1855","北京京东世纪贸易有限公司"));
        companyIdMap.put("265",new Pair<>("2460","北京世纪好未来教育科技有限公司"));
        companyIdMap.put("434",new Pair<>("1456","信达生物制药(苏州)有限公司"));
        companyIdMap.put("11346",new Pair<>("13644","佛山济川医药有限公司"));
        companyIdMap.put("1406",new Pair<>("1892","优锐医药科技(上海)有限公司"));
        companyIdMap.put("11406",new Pair<>("11892","优锐医药科技(上海)有限公司"));
        companyIdMap.put("281",new Pair<>("465","云粒智慧科技有限公司"));
        companyIdMap.put("1172",new Pair<>("1256","中科驭数(北京)科技有限公司"));
        companyIdMap.put("504",new Pair<>("2746","中国平安保险（集团）股份有限公司"));
        companyIdMap.put("2062",new Pair<>("2069","上海鹰角网络科技有限公司"));
        companyIdMap.put("2761",new Pair<>("2762","上海跃赛生物科技有限公司"));
        companyIdMap.put("12761",new Pair<>("12762","上海跃赛生物科技有限公司"));
        companyIdMap.put("376",new Pair<>("755","上海蔚来汽车有限公司"));
        companyIdMap.put("1103",new Pair<>("1104","上海百思童年母婴用品有限公司"));
        companyIdMap.put("2136,1829",new Pair<>("2040","上海君实生物医药科技股份有限公司"));
        companyIdMap.put("11829",new Pair<>("12040","上海君实生物医药科技股份有限公司"));
        companyIdMap.put("287",new Pair<>("147","三诺生物传感股份有限公司"));
        companyIdMap.put("228",new Pair<>("13905","OPPO广东移动通信有限公司"));
        companyIdMap.put("12798",new Pair<>("12855","深圳术为科技有限公司"));
        companyIdMap.put("167",new Pair<>("1589","上海携程国际旅行社有限公司"));
        companyIdMap.put("12896",new Pair<>("13801","金石易服（杭州）科技有限公司"));
        companyIdMap.put("1193",new Pair<>("2776","西比曼生物科技（上海）有限公司"));
        companyIdMap.put("11193,10117",new Pair<>("12776","西比曼生物科技（上海）有限公司"));
        companyIdMap.put("13779",new Pair<>("11302","芳拓生物科技（上海）有限公司"));
        companyIdMap.put("13876",new Pair<>("11912","羿尊生物医药（浙江）有限公司"));
        companyIdMap.put("11437",new Pair<>("11443","珠海泰诺麦博制药股份有限公司"));
        companyIdMap.put("12864",new Pair<>("12863","添可智能科技有限公司"));
        companyIdMap.put("1553,1249",new Pair<>("1770","深圳传音控股股份有限公司"));
        companyIdMap.put("1822",new Pair<>("1698","杭州微策生物技术股份有限公司"));
        companyIdMap.put("2497,2053",new Pair<>("2748","映恩生物制药（苏州）有限公司"));
        companyIdMap.put("12053",new Pair<>("12748","映恩生物制药（苏州）有限公司"));
        companyIdMap.put("496",new Pair<>("2076","拼多多（上海）网络科技有限公司"));
        companyIdMap.put("12895",new Pair<>("12799","扬子江药业集团有限公司"));
        companyIdMap.put("968",new Pair<>("13582","康师傅（天津）饮品有限公司"));
        companyIdMap.put("6,79",new Pair<>("15","华为技术有限公司"));
        companyIdMap.put("13894",new Pair<>("13878","北京炎明生物科技有限公司"));
        companyIdMap.put("1484",new Pair<>("2826","上海睿智化学研究有限公司"));
        companyIdMap.put("11484",new Pair<>("12826","上海睿智化学研究有限公司"));
        companyIdMap.put("11272",new Pair<>("13634","上海和誉生物医药科技有限公司"));
        companyIdMap.put("2797",new Pair<>("12856","万兴科技集团股份有限公司"));
        companyIdMap.put("1936",new Pair<>("1938","星奕昂（上海）生物科技有限公司"));
        companyIdMap.put("11936",new Pair<>("13342","星奕昂（上海）生物科技有限公司"));
        companyIdMap.put("2483",new Pair<>("1859","宜明昂科生物医药技术（上海）股份有限公司"));
        companyIdMap.put("12483",new Pair<>("11859","宜明昂科生物医药技术（上海）股份有限公司"));
        companyIdMap.put("13359",new Pair<>("13527","北京深势科技有限公司"));
        companyIdMap.put("2773,2177,1503",new Pair<>("2775","信念医药科技(上海)有限公司"));
        companyIdMap.put("12773,12177,11503",new Pair<>("12775","信念医药科技(上海)有限公司"));
        companyIdMap.put("107,158",new Pair<>("1678","龙湖地产有限公司"));
        companyIdMap.put("1936,497",new Pair<>("1736","北京快手科技有限公司"));
        companyIdMap.put("817",new Pair<>("2595","上海依图网络科技有限公司"));
        companyIdMap.put("13267",new Pair<>("2465","Neumarker Inc."));
        companyIdMap.put("13713",new Pair<>("532","上海识装信息科技有限公司"));
        companyIdMap.put("13376",new Pair<>("13533","地太科特电子制造(北京)有限公司"));

        companyIdMap.put("102",new Pair<>("2751","再鼎医药(上海)有限公司"));
        companyIdMap.put("258,333",new Pair<>("10335","北京抖音信息服务有限公司"));
        companyIdMap.put("1253",new Pair<>("1596","博雅辑因(北京)生物科技有限公司"));
        companyIdMap.put("278",new Pair<>("2697","同盾科技有限公司"));
        companyIdMap.put("13686",new Pair<>("2363","名创优品（广州）有限责任公司"));
        companyIdMap.put("13535",new Pair<>("13532","宁夏伊品生物科技股份有限公司"));
        companyIdMap.put("2151",new Pair<>("2382","宁德时代新能源科技股份有限公司"));
        companyIdMap.put("110",new Pair<>("13889","小米科技有限责任公司"));
        companyIdMap.put("112",new Pair<>("1539","广州小鹏汽车科技有限公司"));
        companyIdMap.put("13661",new Pair<>("14469","广州汽车集团股份有限公司"));
        companyIdMap.put("1376",new Pair<>("1388","广州趣丸网络科技有限公司"));
        companyIdMap.put("885",new Pair<>("915","智己汽车科技有限公司"));
        companyIdMap.put("1282",new Pair<>("1171","江苏品生医疗科技集团有限公司"));
        companyIdMap.put("1770",new Pair<>("2522","深圳传音控股股份有限公司"));
        companyIdMap.put("480",new Pair<>("744","深圳坤湛科技有限公司"));
        companyIdMap.put("94",new Pair<>("1457","深圳市大疆创新科技有限公司"));
        companyIdMap.put("336",new Pair<>("1354","深圳麦克韦尔科技有限公司"));
        companyIdMap.put("433",new Pair<>("2387","福州天盟数码有限公司"));
        companyIdMap.put("13717",new Pair<>("14395","酷哇科技有限公司"));
        companyIdMap.put("500,1540,621",new Pair<>("13696","腾讯科技(深圳)有限公司"));
        companyIdMap.put("1614",new Pair<>("2763","陕西麦科奥特科技有限公司"));
        companyIdMap.put("11661",new Pair<>("13639","南京三迭纪医药科技有限公司"));
        companyIdMap.put("12392",new Pair<>("14019","南京维立志博生物科技有限公司"));
        companyIdMap.put("11282",new Pair<>("11171","江苏品生医疗科技集团有限公司"));
        companyIdMap.put("12781",new Pair<>("12779","泰诚思（上海）生物医药有限公司"));
        companyIdMap.put("10177",new Pair<>("12776","西比曼生物科技（上海）有限公司"));
        companyIdMap.put("14344",new Pair<>("13740","迈杰转化医学研究(苏州)有限公司"));
        companyIdMap.put("13705",new Pair<>("13706","通瑞生物制药(成都)有限公司"));
        companyIdMap.put("12840",new Pair<>("13725","维亚臻生物技术（上海）有限公司"));

        return companyIdMap;
    }

    @Override
    public List<CompanyCalendarVO> searchCompanyCalendar(List<Long> companyIdList) {
        return companyNativeRepository.searchCompanyCalendar(companyIdList);
    }
}
