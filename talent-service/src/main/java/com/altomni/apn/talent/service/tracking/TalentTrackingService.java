package com.altomni.apn.talent.service.tracking;

import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinGroup;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinMessageTemplate;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinPending;
import com.altomni.apn.talent.service.dto.tracking.*;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingGroupVO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingTemplateVO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingVO;
import org.apache.sis.measure.Longitude;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface TalentTrackingService {

    TalentTrackingVO saveTalentTracking(TalentTrackingDTO talentTrackingDTO);

    Page<TalentTrackingLinkedinPending> searchTalentTracking(TalentTrackingSearchDTO talentTrackingSearchDTO, Pageable pageable);

    void inactiveTalentTracking(TalentTrackingManageDTO talentTrackingManageDTO);

    List<TalentTrackingVO> sentTalentTracking(TalentTrackingManageDTO talentTrackingManageDTO);

    List<TalentTrackingVO> retractSentTalentTracking(TalentTrackingManageDTO talentTrackingManageDTO);

    Page<TalentTrackingLinkedinMessageTemplate> searchTalentTrackingTemplate(TalentTrackingTemplateSearchDTO talentTrackingTemplateSearchD, Pageable pageable);

    TalentTrackingTemplateVO saveTalentTrackingTemplate(TalentTrackingTemplateDTO talentTrackingTemplateDTO);

    TalentTrackingTemplateVO updateTalentTrackingTemplate(Long id, TalentTrackingTemplateDTO talentTrackingTemplateDTO);

    void inactiveTalentTrackingTemplate(TalentTrackingManageDTO talentTrackingManageDTO);

    TalentTrackingGroupVO saveGroup(TalentTrackingGroupDTO talentTrackingGroupDTO);

    TalentTrackingGroupVO updateGroup(Long id, TalentTrackingGroupDTO talentTrackingGroupDTO);

    void inactiveGroup(Long id);

    void saveGroupMember(TalentTrackingGroupManageDTO talentTrackingGroupManageDTO);

    Page<TalentTrackingLinkedinGroup>  searchGroup(String operatorLinkedinId, Pageable pageable);

    List<TalentTrackingGroupVO> toTalentTrackingGroupVO(List<TalentTrackingLinkedinGroup> talentTrackingLinkedinGroupList);

    List<TalentTrackingVO> queryGroupMember(Long id);

    List<TalentTrackingGroupVO> queryGroupByOperatorLinkedinId(String operatorLinkedinId);
}
