package com.altomni.apn.talent.repository.tracking;

import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinGroupMember;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.w3c.dom.stylesheets.LinkStyle;

import java.util.List;

@Repository
public interface TalentTrackingLinkedinGroupMemberRepository extends JpaRepository<TalentTrackingLinkedinGroupMember, Long>, JpaSpecificationExecutor<TalentTrackingLinkedinGroupMember> {

    Integer countDistinctByGroupId(Long groupId);

    List<TalentTrackingLinkedinGroupMember> findAllByGroupIdIn(List<Long> groupIds);

    List<TalentTrackingLinkedinGroupMember> findAllByGroupId(Long id);

    List<TalentTrackingLinkedinGroupMember> findAllByGroupIdInAndTalentLinkedinIdIn(List<Long> groupIds, List<String> talentLinkedinIds);
}
