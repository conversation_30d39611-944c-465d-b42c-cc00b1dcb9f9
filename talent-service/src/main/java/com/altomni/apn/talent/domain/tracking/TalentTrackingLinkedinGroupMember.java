package com.altomni.apn.talent.domain.tracking;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.service.dto.tracking.TalentTrackingGroupMemberDTO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A TalentTrackingLinkedinGroupMember.
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "talent_tracking_linkedin_group_member")
public class TalentTrackingLinkedinGroupMember extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "photo_url")
    private String photoUrl;

    @Column(name = "title")
    private String title;

    @Column(name = "talent_linkedin_id")
    private String talentLinkedinId;

    @Column(name = "link_url")
    private String linkUrl;

    @Column(name = "group_id")
    private Long groupId;

    @Column(name = "tenant_id")
    private Long tenantId;

    public static TalentTrackingLinkedinGroupMember fromTalentTrackingGroupMemberDTO(TalentTrackingGroupMemberDTO talentTrackingGroupMemberDTO, Long tenantId, Long groupId) {
        TalentTrackingLinkedinGroupMember talentTrackingLinkedinGroupMember = new TalentTrackingLinkedinGroupMember();
        ServiceUtils.myCopyProperties(talentTrackingGroupMemberDTO, talentTrackingLinkedinGroupMember);
        talentTrackingLinkedinGroupMember.setTenantId(tenantId);
        talentTrackingLinkedinGroupMember.setGroupId(groupId);
        return talentTrackingLinkedinGroupMember;
    }

    public static TalentTrackingVO toTalentTrackingVO(TalentTrackingLinkedinGroupMember talentTrackingLinkedinGroupMember) {
        TalentTrackingVO talentTrackingVO = new TalentTrackingVO();
        ServiceUtils.myCopyProperties(talentTrackingLinkedinGroupMember, talentTrackingVO);
        return talentTrackingVO;
    }

}
