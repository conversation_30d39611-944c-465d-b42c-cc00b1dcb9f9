package com.altomni.apn.talent.service.vo.tracking;


import com.altomni.apn.talent.domain.enumeration.tracking.TrackingCategory;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingCategoryConverter;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingStatus;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingStatusConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.io.Serializable;
import java.time.Instant;


@Data
@ApiModel(value = "talent tracking")
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentTrackingVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "firstName")
    private String firstName;

    @ApiModelProperty(value = "lastName")
    private String lastName;

    @ApiModelProperty(value = "photoUrl")
    private String photoUrl;

    @ApiModelProperty(value = "title")
    private String title;

    @ApiModelProperty(value = "talentLinkedinId")
    private String talentLinkedinId;

    @ApiModelProperty(value = "operatorLinkedinId")
    private String operatorLinkedinId;

    @ApiModelProperty(value = "linkUrl")
    private String linkUrl;

    @ApiModelProperty(value = "lastInteractionTime")
    private Instant lastInteractionTime;

    @ApiModelProperty(value = "category")
    @Convert(converter = TrackingCategoryConverter.class)
    private TrackingCategory category;

    @ApiModelProperty(value = "category")
    @Convert(converter = TrackingStatusConverter.class)
    private TrackingStatus status;

    @ApiModelProperty(value = "groupId")
    private Long groupId;

}
