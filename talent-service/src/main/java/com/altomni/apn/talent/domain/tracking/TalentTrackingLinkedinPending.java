package com.altomni.apn.talent.domain.tracking;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingCategory;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingCategoryConverter;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingStatus;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingStatusConverter;
import com.altomni.apn.talent.service.dto.tracking.TalentTrackingDTO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

/**
 * A TalentTrackingLinkedinPending.
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "talent_tracking_linkedin_pending")
public class TalentTrackingLinkedinPending extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "photo_url")
    private String photoUrl;

    @Column(name = "title")
    private String title;

    @Column(name = "talent_linkedin_id")
    private String talentLinkedinId;

    @Column(name = "link_url")
    private String linkUrl;

    @Convert(converter = TrackingStatusConverter.class)
    @Column(name = "status")
    private TrackingStatus status;

    @Column(name = "last_interaction_time")
    private Instant lastInteractionTime;

    @Convert(converter = TrackingCategoryConverter.class)
    @Column(name = "category")
    private TrackingCategory category;

    @Column(name = "operatorLinkedinId")
    private String operatorLinkedinId;

    @Column(name = "tenant_id")
    private Long tenantId;

    public static TalentTrackingLinkedinPending fromTalentTrackingDTO(TalentTrackingDTO talentTrackingDTO) {
        TalentTrackingLinkedinPending talentTrackingLinkedinPending = new TalentTrackingLinkedinPending();
        ServiceUtils.myCopyProperties(talentTrackingDTO, talentTrackingLinkedinPending);
        return talentTrackingLinkedinPending;
    }

    public static TalentTrackingVO toTalentTrackingVO(TalentTrackingLinkedinPending talentTrackingLinkedinPending) {
        TalentTrackingVO talentTrackingVO = new TalentTrackingVO();
        ServiceUtils.myCopyProperties(talentTrackingLinkedinPending, talentTrackingVO);
        return talentTrackingVO;
    }
}
