package com.altomni.apn.talent.service.talent.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.*;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.altomni.apn.common.config.constants.ContactTypeConstants;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.dict.*;
import com.altomni.apn.common.domain.enumeration.*;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.talent.CreationTalentType;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.parser.ResumeParseInfo;
import com.altomni.apn.common.domain.talent.*;
import com.altomni.apn.common.domain.transactionrecord.CommonMqTransactionRecord;
import com.altomni.apn.common.domain.user.SimpleUser;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.ByteArrayResourceMultipartFile;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.RecommendFeedback;
import com.altomni.apn.common.dto.SkillDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessResignationVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.company.ClientContactCompany;
import com.altomni.apn.common.dto.company.ClientContactDTO;
import com.altomni.apn.common.dto.folder.FolderSharedTeamDTO;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.redis.ParserRedisResponse;
import com.altomni.apn.common.dto.salelead.TalentClientContactStatusDTO;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.store.UploadUrlDto;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.dto.user.NameOnlyUser;
import com.altomni.apn.common.dto.user.TimeSheetUserDTO;
import com.altomni.apn.common.enumeration.ParseStatus;
import com.altomni.apn.common.enumeration.ReviewedByType;
import com.altomni.apn.common.enumeration.enums.MqTranRecordBusTypeEnums;
import com.altomni.apn.common.enumeration.enums.MqTranRecordStatusEnums;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.errors.*;
import com.altomni.apn.common.repository.talent.ResumeRepository;
import com.altomni.apn.common.repository.user.SimpleUserRepository;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.service.enums.EnumIndustryService;
import com.altomni.apn.common.service.enums.EnumLanguageService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.common.vo.store.StoreGetUploadUrlVO;
import com.altomni.apn.common.vo.talent.TalentBriefVO;
import com.altomni.apn.common.vo.talent.TalentEmailContactVO;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.company.web.rest.dto.FindAllByNamesDTO;
import com.altomni.apn.talent.config.TalentFormRelateField;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.config.env.EsfillerMQProperties;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.config.env.TalentTxMQProperties;
import com.altomni.apn.talent.constants.Constants;
import com.altomni.apn.talent.constants.RedisConstants;
import com.altomni.apn.talent.constants.ReportTemplateConstants;
import com.altomni.apn.talent.converter.TalentInfoMapper;
import com.altomni.apn.talent.domain.agency.AgencyTalentRelation;
import com.altomni.apn.talent.domain.application.TalentApplicationProcessSubmitToJob;
import com.altomni.apn.talent.domain.enumeration.LanguageEnum;
import com.altomni.apn.talent.domain.enumeration.ReportTypeEnum;
import com.altomni.apn.talent.domain.enumeration.talent.RecommendTemplateType;
import com.altomni.apn.talent.domain.folder.TalentFolderRelation;
import com.altomni.apn.talent.domain.record.ExcelUpdateTalentRecord;
import com.altomni.apn.talent.domain.talent.*;
import com.altomni.apn.talent.domain.user.HotListTalent;
import com.altomni.apn.talent.repository.agency.AgencyTalentRelationRepository;
import com.altomni.apn.talent.repository.application.TalentApplicationProcessSubmitToJobRepository;
import com.altomni.apn.talent.repository.folder.TalentFolderRelationRepository;
import com.altomni.apn.talent.repository.hotlist.HotListTalentRepository;
import com.altomni.apn.talent.repository.talent.*;
import com.altomni.apn.talent.repository.transactionrecord.TalentCommonMqTransactionRecordRepository;
import com.altomni.apn.talent.service.CompanyService;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.application.ApplicationService;
import com.altomni.apn.talent.service.confidential.TalentConfidentialService;
import com.altomni.apn.talent.service.dto.start.TalentExcelUploadUrlDto;
import com.altomni.apn.talent.service.dto.talent.*;
import com.altomni.apn.talent.service.elastic.EsCommonService;
import com.altomni.apn.talent.service.elastic.EsFillerTalentService;
import com.altomni.apn.talent.service.finance.FinanceService;
import com.altomni.apn.talent.service.jobdiva.JobdivaService;
import com.altomni.apn.talent.service.mail.MailService;
import com.altomni.apn.talent.service.parser.ParserService;
import com.altomni.apn.talent.service.store.StoreService;
import com.altomni.apn.talent.service.talent.*;
import com.altomni.apn.talent.service.talent.channel.CreateTalentByExcelChannel;
import com.altomni.apn.talent.service.talent.file.LiePinHeaderFooterEventHandler;
import com.altomni.apn.talent.service.vo.talent.ExcelTalentProcessVo;
import com.altomni.apn.talent.service.vo.talent.TalentFailReasonVo;
import com.altomni.apn.talent.utils.ObjectMapperUtils;
import com.altomni.apn.talent.web.rest.talent.dto.*;
import com.altomni.apn.talent.web.rest.vm.TalentContactSearchVM;
import com.altomni.apn.talent.web.rest.vm.TalentContactVM;
import com.altomni.apn.talent.web.rest.vm.TalentNameVM;
import com.altomni.apn.user.domain.user.CreditTransaction;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.dto.user.CreditTransactionDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserTeamPermissionVM;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.net.MediaType;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ipg.resourceserver.authentication.OAuth2ClientAuthenticationToken;
import com.itextpdf.text.Font;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.opencsv.CSVReader;
import com.opencsv.CSVWriter;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.*;
import org.jetbrains.annotations.Nullable;
import org.mozilla.universalchardet.UniversalDetector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;
import org.zalando.problem.Status;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.altomni.apn.common.constants.AuthConstants.AUTHORIZATION_HEADER;
import static com.altomni.apn.common.constants.AuthConstants.TOKEN_TYPE;
import static com.altomni.apn.common.domain.enumeration.talent.TalentNoteType.OTHERS;
import static com.altomni.apn.talent.constants.Constants.*;
import static com.altomni.apn.talent.service.talent.impl.TalentOwnershipServiceImpl.TALENT_BROWSE_QUOTA;
import static com.altomni.apn.talent.service.talent.impl.TalentOwnershipServiceImpl.TALENT_RECOMMENDATION_RIGHTS_PROTECTION_PERIOD;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;
import static org.zalando.problem.Status.BAD_REQUEST;

@Service
public class TalentServiceImpl implements TalentService {

    private final Logger log = LoggerFactory.getLogger(TalentServiceImpl.class);

    @Resource
    private TalentNoteService talentNoteService;

    @Resource
    private TalentContactRepository talentContactRepository;

    @Resource
    private TalentNoteRepository talentNoteRepository;

    @Resource
    private TalentOwnershipRepository talentOwnershipRepository;

    @Resource
    private TalentOwnershipService talentOwnershipService;

    @Resource
    private TalentContactService talentContactService;

    @Resource
    private EsFillerTalentService esFillerTalentService;

    @Resource
    private CreateTalentByExcelChannel createTalentByExcelChannel;

    @Resource
    private TalentResumeService talentResumeService;

    @Resource
    private UserService userService;

    @Resource
    private TalentRepository talentRepository;

    @Resource
    private JobdivaService jobdivaService;

    @Resource
    private FinanceService financeService;

    @Resource
    private SimpleUserRepository simpleUserRepository;

    @Resource
    private MailService mailService;

    @Resource
    private CompanyService companyService;

    @Resource
    private EsCommonService esCommonService;

    @Resource
    private ApplicationService applicationService;

    @Resource
    private TalentLocationRepository talentLocationRepository;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private EnumLanguageService enumLanguageService;

    @Resource
    private EnumIndustryService enumIndustryService;

    @Resource
    private InitiationService initiationService;

    @Resource
    private EnumCommonService enumCommonService;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    private ResumeRepository resumeRepository;

    @Resource
    private StoreService storeService;

    @Resource
    private ParserService parserService;

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private HotListTalentRepository hotListTalentRepository;

    @Resource(name = "talentTxRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Resource(name = "updateTalentRabbitTemplate")
    private RabbitTemplate updateTalentRabbitTemplate;

    @Resource
    private TalentTxMQProperties talentTxMQProperties;

    @Resource
    TalentCommonMqTransactionRecordRepository talentCommonMqTransactionRecordRepository;

    @Resource(name = "commonThreadPool")
    private Executor executor;

    @Resource
    private TalentFolderRelationRepository talentFolderRelationRepository;

    @Autowired
    private TalentResumeRelationRepository talentResumeRelationRepository;

    @Resource
    private TalentInfoMapper talentInfoMapper;

    @Resource
    private TalentJobFunctionRelationRepository talentJobFunctionRelationRepository;

    @Resource
    private TalentIndustryRelationRepository talentIndustryRelationRepository;

    @Resource
    private ExcelUpdateTalentRecordRepository excelUpdateTalentRecordRepository;

    @Resource
    private PlatformTransactionManager transactionManager;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    @Resource
    private HttpService httpService;

    @Resource
    private TalentConfidentialService talentConfidentialService;


    @Resource
    private AgencyTalentRelationRepository agencyTalentRelationRepository;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private TalentCrmSyncService talentCrmSyncService;

    private static final String RESET_CONTENT_TYPE_KEY = "RESET_CONTENT_TYPE_KEY";

    private static final String FILL_RESUME_INFOS_KEY = "FILL_RESUME_INFOS_KEY";

    private final String KEY_GENDER = "gender";

    private final String KEY_BIRTHDATE = "birthDate";

    //支持的2种CSV格式
    private static final List<String> CSV_CONTENT_TYPE_LIST = CollUtil.newArrayList("application/csv","text/csv");

    //禁猎客户需求：talent experience中的companyName如果被修改，移除这些key
    private static final List<String> FIELDS_TO_REMOVE = Arrays.asList(
            "businessInfoCompanyId",
            "bdCompanyId",
            "recogLeadsCompanyId",
            "recogCRMAccountId",
            "recogCompanyId"
    );

    //候选人爬虫bot用户id
    private static final Long TALENT_CRAWLER_BOT_USER_ID = 12243L;

    private ParserRedisResponse getResumeParseResponse(String uuid) {
        ParserRedisResponse parserResumeData = commonRedisService.getParserResumeData(uuid);
        if(parserResumeData.getData() == null) {
            Resume resume = resumeRepository.findByUuid(uuid);
            if(resume != null) {
                String parseResult = resume.getParseResult();
                if(StringUtils.isNotEmpty(parseResult)) {
                    parserResumeData.setData(parseResult);
                    parserResumeData.setStatus(ParseStatus.FINISHED);
                }
            }
        }
        return parserResumeData;
    }

    private void removeEnumsNull(TalentInfoInput talentDTO) {
        talentDTO.setWorkAuthorization(talentDTO.getWorkAuthorization() == null ? null : talentDTO.getWorkAuthorization().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        talentDTO.setJobFunctions(talentDTO.getJobFunctions() == null ? null : talentDTO.getJobFunctions().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        talentDTO.setLanguages(talentDTO.getLanguages() == null ? null : talentDTO.getLanguages().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        talentDTO.setIndustries(talentDTO.getIndustries() == null ? null : talentDTO.getIndustries().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        String photoUrl = talentDTO.getPhotoUrl();
        if(StringUtils.isEmpty(photoUrl)) {
            talentDTO.setPhotoUrl(null);
        }
    }

    private void removeEnumsNull(TalentDTOV3 talentDTO) {
        talentDTO.setWorkAuthorization(talentDTO.getWorkAuthorization() == null ? null : talentDTO.getWorkAuthorization().stream().filter(o -> StringUtils.isNotEmpty(o.getEnumId())).collect(Collectors.toList()));
        talentDTO.setJobFunctions(talentDTO.getJobFunctions() == null ? null : talentDTO.getJobFunctions().stream().filter(o -> StringUtils.isNotEmpty(o.getEnumId())).collect(Collectors.toList()));
        talentDTO.setLanguages(talentDTO.getLanguages() == null ? null : talentDTO.getLanguages().stream().filter(o -> StringUtils.isNotEmpty(o.getEnumId())).collect(Collectors.toList()));
        talentDTO.setIndustries(talentDTO.getIndustries() == null ? null : talentDTO.getIndustries().stream().filter(o -> StringUtils.isNotEmpty(o.getEnumId())).collect(Collectors.toList()));
        String photoUrl = talentDTO.getPhotoUrl();
        if(StringUtils.isEmpty(photoUrl)) {
            talentDTO.setPhotoUrl(null);
        }
    }

    private void setTalentDefaultValue(TalentDTOV3 talentDTO) {
        talentDTO.setTenantId(SecurityUtils.getTenantId());
        if (ObjectUtil.isNull(talentDTO.getFullName())) {
            talentDTO.setFullName();
        } else {
            talentDTO.setFullName(talentDTO.getFullName());
        }
        //  set user id for notes
        if (CollUtil.isNotEmpty(talentDTO.getNotes())) {
            talentDTO.getNotes().forEach(n -> n.setUserId(SecurityUtils.getUserId()));
        }
    }
    private void setExcelTalentRelateEntityByExcel(TalentDTOV3 talentDTO, TalentV3 talent) {
        //talentContacts
        if (CollUtil.isNotEmpty(talentDTO.getContacts())) {
            List<TalentContactDTO> contactSet = new ArrayList<>();
            for (TalentContactDTO t : talentDTO.getContacts().stream().filter(c ->
                    ContactTypeConstants.COMPREHENSIVE_CONTACT_TYPES.contains(c.getType())).collect(collectingAndThen(
                    toCollection(() -> new TreeSet<>(Comparator.comparing(TalentContactDTO::getTypeAndContact))), ArrayList::new))) {
                t.setTenantId(SecurityUtils.getTenantId());
                t.setTalentId(talent.getId());
                t.setStatus(TalentContactStatus.AVAILABLE);
                t.setId(null);
                contactSet.add(t);
            }
            List<TalentContact> talentContactList = talentContactRepository.saveAll(Convert.toList(TalentContact.class, contactSet));
            talentDTO.setContacts(Convert.toList(TalentContactDTO.class, talentContactList));
        } else {
            throw new CustomParameterizedException("At least one contact information is required when create talent !");
        }
        //talentLocation
        if (ObjectUtil.isNotEmpty(talentDTO.getCurrentLocation())) {
            TalentCurrentLocation location = new TalentCurrentLocation();
            location.setTalentId(talent.getId());
            location.setOriginalLoc(JSONUtil.toJsonStr(talentDTO.getCurrentLocation()));
            talentLocationRepository.saveAndFlush(location);
        }
        //ownership  添加当前操作人为TALENT_OWNER
        createOwnerships(talent.getId(), talent.getCreatedDate());
    }


    private boolean setTalentRelateEntity(TalentDTOV3 talentDTO, Long talentId) {
        boolean isTalentResumesChanged = false;

        //update talent ownership
        updateTalentOwnership(talentDTO, talentId);

        //update talent contact
        boolean isContactsChanged = updateTalentContact(talentDTO, talentId);

        //update talent resumes
        List<TalentResumeDTO> resumes = talentDTO.getResumes();
        if (CollUtil.isNotEmpty(resumes)) {
            Map<Long, Long> replaceTalentResumeMap = new HashMap<>();
            List<TalentResumeRelation> allTalent = talentResumeRelationRepository.findAllByTalentIdAndTenantId(talentId, SecurityUtils.getTenantId());
            Map<Long, Long> resumeId2RelationIdMap = allTalent.stream().collect(Collectors.toMap(TalentResumeRelation::getResumeId, TalentResumeRelation::getId));
            List<Long> existResumeIds = allTalent.stream().map(TalentResumeRelation::getResumeId).collect(Collectors.toList());
            Map<Long, TalentResumeDTO> activeMap = new HashMap<>();
            for (TalentResumeDTO talentResumeDTO : resumes) {
                Long resumeId = talentResumeDTO.getResumeId();
                if(resumeId == null) {
                    continue;
                }
                if(existResumeIds.contains(resumeId)) {
                    activeMap.put(resumeId, talentResumeDTO);
                    existResumeIds.remove(resumeId);
                    //添加后续要替换的简历relationid
                    if(talentResumeDTO.getOriginTalentResumeRelationIds() != null) {
                        List<Long> originTalentResumeRelationIds = talentResumeDTO.getOriginTalentResumeRelationIds();
                        for(Long originId : originTalentResumeRelationIds) {
                            replaceTalentResumeMap.put(originId, resumeId2RelationIdMap.get(resumeId));
                        }
                    }
                    continue;
                }
                TalentResumeRelation talentResumeRelation = new TalentResumeRelation();
                talentResumeRelation.setResumeId(resumeId);
                talentResumeRelation.setTalentId(talentId);
                talentResumeRelation.setFileName(talentResumeDTO.getFileName());
                talentResumeRelation.setSourceType(talentResumeDTO.getSourceType());
                talentResumeRelation.setTenantId(SecurityUtils.getTenantId());
                talentResumeRelation.setStatus(CommonDataStatus.AVAILABLE);
                talentResumeRelationRepository.saveAndFlush(talentResumeRelation);
                //添加后续要替换的简历relationid
                if(talentResumeDTO.getOriginTalentResumeRelationIds() != null) {
                    List<Long> originTalentResumeRelationIds = talentResumeDTO.getOriginTalentResumeRelationIds();
                    for(Long originId : originTalentResumeRelationIds) {
                        replaceTalentResumeMap.put(originId, talentResumeRelation.getId());
                    }
                }
                isTalentResumesChanged = true;
            }
            if(!activeMap.isEmpty()) {
                updateStatusAndSourceTypeTalentResumeRelationByTalentIdAndResumeId(activeMap, talentId, CommonDataStatus.AVAILABLE);
                isTalentResumesChanged = true;
            }
            if(!existResumeIds.isEmpty()) {
                updateStatusTalentResumeRelationByTalentIdAndResumeId(existResumeIds, talentId, CommonDataStatus.INVALID);
                isTalentResumesChanged = true;
            }
            if(!replaceTalentResumeMap.isEmpty()) {
                replaceSubmitToJobResume(replaceTalentResumeMap);
            }
        } else {
            isTalentResumesChanged = deleteTalentResumeRelation(talentId);
        }

        //update talent location
        if (ObjectUtil.isNotEmpty(talentDTO.getCompanyLocationId())) {
            TalentCurrentLocation existLocation = talentLocationRepository.findByTalentId(talentId);
            LocationDTO locationDTO = companyService.queryCompanyLocation(talentDTO.getCompanyLocationId()).getBody();
            if (locationDTO != null) {
                if(existLocation == null) {
                    locationDTO.setId(null);
                    TalentCurrentLocation location = new TalentCurrentLocation();
                    location.setTalentId(talentId);
                    location.setZipCode(talentDTO.getZipCode());
                    location.setOriginalLoc(JSONUtil.toJsonStr(locationDTO));
                    talentLocationRepository.saveAndFlush(location);
                } else {
                    String originalLoc = existLocation.getOriginalLoc();
                    String newOriginalLoc = JSONUtil.toJsonStr(locationDTO);
                    if(!ObjectUtil.equal(newOriginalLoc, originalLoc)) {
                        existLocation.setOriginalLoc(newOriginalLoc);
                        existLocation.setOfficialCounty(null);
                        existLocation.setOfficialCountry(null);
                        existLocation.setOfficialCity(null);
                        existLocation.setOfficialProvince(null);
                    }
                    if(talentDTO.getZipCode() != null && !ObjectUtil.equal(existLocation.getZipCode(), talentDTO.getZipCode())) {
                        existLocation.setZipCode(talentDTO.getZipCode());
                    }
                    talentLocationRepository.saveAndFlush(existLocation);
                }
            }
        } else if (ObjectUtil.isNotEmpty(talentDTO.getCurrentLocation()) || StringUtils.isNotEmpty(talentDTO.getZipCode())) {
            updateLocation(talentId, talentDTO.getZipCode(), talentDTO.getCurrentLocation());
        } else {
            talentLocationRepository.deleteAllByTalentId(talentId);
        }

        return isTalentResumesChanged || isContactsChanged;
    }

    private void singleSaveTalentRelateEntity(TalentDTOV3 talentDTO, Long talentId) {
        //update talent contact
        if(CollUtil.isNotEmpty(talentDTO.getContacts())) {
            updateTalentContact(talentDTO, talentId);
        }

        //update talent location
        if (ObjectUtil.isNotEmpty(talentDTO.getCompanyLocationId())) {
            TalentCurrentLocation existLocation = talentLocationRepository.findByTalentId(talentId);
            LocationDTO locationDTO = companyService.queryCompanyLocation(talentDTO.getCompanyLocationId()).getBody();
            if (locationDTO != null) {
                if(existLocation == null) {
                    locationDTO.setId(null);
                    TalentCurrentLocation location = new TalentCurrentLocation();
                    location.setTalentId(talentId);
                    location.setZipCode(talentDTO.getZipCode());
                    location.setOriginalLoc(JSONUtil.toJsonStr(locationDTO));
                    talentLocationRepository.saveAndFlush(location);
                } else {
                    String originalLoc = existLocation.getOriginalLoc();
                    String newOriginalLoc = JSONUtil.toJsonStr(locationDTO);
                    if(!ObjectUtil.equal(newOriginalLoc, originalLoc)) {
                        existLocation.setOriginalLoc(newOriginalLoc);
                        existLocation.setOfficialCounty(null);
                        existLocation.setOfficialCountry(null);
                        existLocation.setOfficialCity(null);
                        existLocation.setOfficialProvince(null);
                    }
                    if(talentDTO.getZipCode() != null && !ObjectUtil.equal(existLocation.getZipCode(), talentDTO.getZipCode())) {
                        existLocation.setZipCode(talentDTO.getZipCode());
                    }
                    talentLocationRepository.saveAndFlush(existLocation);
                }
            }
        } else if (ObjectUtil.isNotEmpty(talentDTO.getCurrentLocation()) || StringUtils.isNotEmpty(talentDTO.getZipCode())) {
            updateLocation(talentId, talentDTO.getZipCode(), talentDTO.getCurrentLocation());
        } else if (talentDTO.getCurrentLocation() != null && isEmptyObject(talentDTO.getCurrentLocation())) {
            // 新增方法判断对象是否为空对象（所有属性为默认值）
            talentLocationRepository.deleteAllByTalentId(talentId);
        }
    }

    /**
     * 判断对象是否为空对象（所有属性为默认值）
     */
    private boolean isEmptyObject(Object obj) {
        if (obj == null) {
            return false;
        }

        // 使用Hutool的反射工具获取所有字段
        Field[] fields = ReflectUtil.getFields(obj.getClass());
        for (Field field : fields) {
            // 跳过静态字段
            if (Modifier.isStatic(field.getModifiers())) {
                continue;
            }

            // 获取字段值
            Object value = ReflectUtil.getFieldValue(obj, field);

            // 如果有任何一个字段值不为空，则对象不为空
            if (ObjectUtil.isNotEmpty(value)) {
                return false;
            }
        }

        return true;
    }

    private void replaceSubmitToJobResume(Map<Long, Long> replaceTalentResumeMap) {
        Set<Long> originIdList = replaceTalentResumeMap.keySet();
        List<TalentApplicationProcessSubmitToJob> allByTalentResumeRelationIdIn = submitToJobRepository.findAllByTalentResumeRelationIdIn(new ArrayList<>(originIdList));
        allByTalentResumeRelationIdIn.forEach(c -> {
            c.setTalentResumeRelationId(replaceTalentResumeMap.get(c.getTalentResumeRelationId()));
        });

        submitToJobRepository.saveAll(allByTalentResumeRelationIdIn);
    }

    private void updateLocation(Long talentId, String zipCode, LocationDTO currentLocation){
        TalentCurrentLocation existLocation = talentLocationRepository.findByTalentId(talentId);
        if(existLocation == null) {
            TalentCurrentLocation location = new TalentCurrentLocation();
            location.setTalentId(talentId);
            location.setZipCode(zipCode);
            location.setOriginalLoc(JSONUtil.toJsonStr(currentLocation));
            talentLocationRepository.saveAndFlush(location);
        } else {
            String originalLoc = existLocation.getOriginalLoc();
            String newOriginalLoc = JSONUtil.toJsonStr(currentLocation);
            if(!ObjectUtil.equal(newOriginalLoc, originalLoc)) {
                existLocation.setOriginalLoc(newOriginalLoc);
                existLocation.setOfficialCounty(null);
                existLocation.setOfficialCountry(null);
                existLocation.setOfficialCity(null);
                existLocation.setOfficialProvince(null);
            }
            if(zipCode != null && !ObjectUtil.equal(existLocation.getZipCode(), zipCode)) {
                existLocation.setZipCode(zipCode);
            }
            talentLocationRepository.save(existLocation);
        }
    }
    private boolean equalTalentContactFromDTO(List<TalentContactDTO> talentContactDTOList, TalentContact talentContact) {
        if(talentContactDTOList == null || talentContactDTOList.isEmpty()) {
            return false;
        }

        if(talentContact == null) {
            return false;
        }
        for(TalentContactDTO talentContactDTO : talentContactDTOList) {
            if(talentContactDTO.getType() == talentContact.getType() && StringUtils.equals(talentContactDTO.getContact(), talentContact.getContact()) && StringUtils.equals(talentContactDTO.getDetails(), talentContact.getDetails())) {
                return true;
            }
        }
        return false;
    }

    private boolean equalTalentContactFromDB(List<TalentContact> talentContactList, TalentContactDTO talentContactDTO) {
        if(talentContactList == null || talentContactList.isEmpty()) {
            return false;
        }

        if(talentContactDTO == null) {
            return false;
        }
        for(TalentContact talentContact : talentContactList) {
            if (TalentContactVerificationStatus.WRONG_CONTACT.equals(talentContact.getVerificationStatus())) {
                if (StringUtils.equals(talentContact.getContact(), talentContactDTO.getContact())) { //TODO: international country code
                    return true;
                }
            }
            if(talentContact.getType() == talentContactDTO.getType() && StringUtils.equals(talentContact.getContact(), talentContactDTO.getContact()) && StringUtils.equals(talentContact.getDetails(), talentContactDTO.getDetails())) {
                return true;
            }
        }
        return false;
    }

    public boolean updateTalentContact(TalentDTOV3 talentDTO, Long talentId) {
        List<TalentContact> existContact = talentContactRepository.findAllByTalentId(talentId);
        List<TalentContactDTO> contacts = talentDTO.getContacts();
        cleanUpContacts(contacts);

        List<TalentContact> update2Active = existContact.stream().filter(tc -> !TalentContactVerificationStatus.WRONG_CONTACT.equals(tc.getVerificationStatus())) //wrong contact cannot be updated
                .filter(c -> equalTalentContactFromDTO(contacts, c)).collect(Collectors.toList());
        update2Active.forEach(c -> contacts.forEach(ic -> {
            if(c.getType() == ic.getType() && StringUtils.equals(c.getContact(), ic.getContact())) {
                c.setSort(ic.getSort());
            }
        }));
        updateTalentContactStatus(update2Active, TalentContactStatus.AVAILABLE);

        List<TalentContact> update2Invalid = existContact.stream().filter(tc -> !TalentContactVerificationStatus.WRONG_CONTACT.equals(tc.getVerificationStatus())) //wrong contact cannot be updated
                .filter(c -> !equalTalentContactFromDTO(contacts, c) && TalentContactStatus.AVAILABLE == c.getStatus()).collect(Collectors.toList());
        updateTalentContactStatus(update2Invalid, TalentContactStatus.INVALID);

        List<TalentContactDTO> addContact = contacts.stream().filter(c -> !TalentContactVerificationStatus.WRONG_CONTACT.equals(c.getVerificationStatus())) //wrong contact cannot be updated
                .filter(c -> !equalTalentContactFromDB(existContact, c)).collect(Collectors.toList());

        // timesheet user 添加联系方式时， tenant id 有可能为空
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            tenantId = existContact.stream().map(TalentContact::getTenantId).findAny().orElse(null);
        }
        //talentContacts
        if (CollUtil.isNotEmpty(addContact)) {
            List<TalentContactDTO> contactSet = new ArrayList<>();
            for (TalentContactDTO t : addContact.stream().filter(c ->
                    ContactTypeConstants.COMPREHENSIVE_CONTACT_TYPES.contains(c.getType())).collect(Collectors.toList())) {
                t.setTenantId(tenantId);
                t.setTalentId(talentId);
                t.setStatus(TalentContactStatus.AVAILABLE);
                t.setId(null);
                contactSet.add(t);
            }
            talentContactRepository.saveAllAndFlush(Convert.toList(TalentContact.class, contactSet));
            talentDTO.setContacts(Convert.toList(TalentContactDTO.class, talentContactRepository.findAllByTalentId(talentId)));
        }
        return CollUtil.isNotEmpty(addContact);
    }

    private void updateTalentContactStatus(List<TalentContact> updateContact, TalentContactStatus talentContactStatus) {
        for(TalentContact talentContact : updateContact) {
            talentContact.setStatus(talentContactStatus);
        }
        talentContactRepository.saveAllAndFlush(updateContact);
    }

    public void updateTalentOwnership(TalentDTOV3 talentDTO, Long talentId) {
        List<TalentOwnershipType> ownershipList = Lists.newArrayList(TalentOwnershipType.SHARE, TalentOwnershipType.TALENT_OWNER, TalentOwnershipType.TENANT_SHARE);
        List<TalentOwnership> existTalent = talentOwnershipRepository.findAllByTalentIdAndOwnershipTypeIn(talentId, ownershipList);

        Map<Pair<Long, TalentOwnershipType>, TalentOwnership> existingMap = existTalent.stream()
                .collect(Collectors.toMap(
                        to -> Pair.of(to.getUserId(), to.getOwnershipType()),
                        to -> to
                ));

        List<TalentOwnershipDTO> ownerships = talentDTO.getOwnerships();
        if (ownerships != null) {
            ownerships = ownerships.stream()
                    .filter(p -> ownershipList.contains(p.getOwnershipType()))
                    .collect(Collectors.toList());
        } else {
            ownerships = new ArrayList<>();
        }

        Map<Pair<Long, TalentOwnershipType>, TalentOwnershipDTO> inputMap = ownerships.stream()
                .collect(Collectors.toMap(
                        o -> Pair.of(o.getUserId(), o.getOwnershipType()),
                        o -> o
                ));

        // Delete ownerships that are not in the input
        List<TalentOwnership> toDelete = existingMap.entrySet().stream()
                .filter(entry -> !inputMap.containsKey(entry.getKey()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());

        talentOwnershipRepository.deleteAll(toDelete);

        Instant createdDate = talentDTO.getCreatedDate();
        if(createdDate == null) {
            createdDate = Instant.now();
        }
        // Add new ownerships
        for (Map.Entry<Pair<Long, TalentOwnershipType>, TalentOwnershipDTO> entry : inputMap.entrySet()) {
            Pair<Long, TalentOwnershipType> key = entry.getKey();
            if (!existingMap.containsKey(key)) {
                // Add new ownership
                TalentOwnership talentOwnership = new TalentOwnership();
                talentOwnership.setTalentId(talentId);
                talentOwnership.setOwnershipType(key.getRight());
                talentOwnership.setExpireTime(createdDate.plus(userService.getTenantParamValue(TALENT_RECOMMENDATION_RIGHTS_PROTECTION_PERIOD).getBody(), ChronoUnit.MINUTES));
                talentOwnership.setUserId(key.getLeft());
                talentOwnershipRepository.saveAndFlush(talentOwnership);
            }
        }

        List<TalentOwnership> updatedOwnerships = talentOwnershipRepository.findAllByTalentId(talentId);
        talentDTO.setOwnerships(toDtos(updatedOwnerships));
    }

    private Map<Long, TalentOwnershipType> transeferToUserIdAndOwnershipTypeMap(List<TalentOwnershipDTO> ownerships) {
        Map<Long, TalentOwnershipType> map = new HashMap<>();
        if(ownerships == null) {
            return map;
        }
        ownerships.forEach(o -> {map.put(o.getUserId(), o.getOwnershipType());});
        return map;
    }

    private void updateStatusAndSourceTypeTalentResumeRelationByTalentIdAndResumeId(Map<Long, TalentResumeDTO> activeMap, Long id, CommonDataStatus commonDataStatus) {
        List<Long> resumeIds = activeMap.keySet().stream().collect(Collectors.toList());
        List<TalentResumeRelation> talentResumeRelationList = talentResumeRelationRepository.findAllByTalentIdAndResumeIdIn(id, resumeIds);
        if(org.springframework.util.CollectionUtils.isEmpty(talentResumeRelationList)) {
            return;
        }
        talentResumeRelationList.forEach(t -> {
            t.setStatus(commonDataStatus);
            TalentResumeDTO resumeDTO = activeMap.get(t.getResumeId());
            if(resumeDTO != null) {
                t.setSourceType(resumeDTO.getSourceType());
            }
        });
        talentResumeRelationRepository.saveAllAndFlush(talentResumeRelationList);
    }

    private void updateStatusTalentResumeRelationByTalentIdAndResumeId(List<Long> resumeIds, Long id, CommonDataStatus commonDataStatus) {
        List<TalentResumeRelation> talentResumeRelationList = talentResumeRelationRepository.findAllByTalentIdAndResumeIdIn(id, resumeIds);
        if(org.springframework.util.CollectionUtils.isEmpty(talentResumeRelationList)) {
            return;
        }
        talentResumeRelationList.forEach(t -> t.setStatus(commonDataStatus));
        talentResumeRelationRepository.saveAllAndFlush(talentResumeRelationList);
    }

    @Override
    public Long create(TalentInfoInput input) {
        StopWatch stopWatch = new StopWatch("create start");
        stopWatch.start("[3.1] removeEnumsNull");
        removeEnumsNull(input);
        // 手动控制事务
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);
        TalentV3 talent;
        try {
            log.info("[APN: TalentService @{}] create talent v3 -> CreateTalentInput: {}", SecurityUtils.getUserId(), input);
            TalentDTOV3 talentDTO = talentInfoMapper.toTalentDTO(input);
            setTalentDefaultValue(talentDTO);
            try {
                log.info("startCheckDuplicationTalent");
                TalentV3 checkDuplicationTalent = TalentV3.fromTalentDTO(talentDTO);
                List<TalentContactDTO> contacts = input.getContacts();
                TalentSimilarityDto talentSimilarityDto = new TalentSimilarityDto();
                talentSimilarityDto.setCurrentLocation(input.getCurrentLocation());
                stopWatch.stop();
                stopWatch.start("[3.2] CreateTalentInput");
                List<SuspectedDuplications> talentDTOV3List = getSuspectededDuplicationsByContactAndSmilarity(checkDuplicationTalent, contacts, talentSimilarityDto, null);
                if (CollUtil.isNotEmpty(talentDTOV3List)) {
                    Long talentId = Long.valueOf(talentDTOV3List.get(0).get_id());
                    setTalentFolder(talentDTO.getHotListId(), talentId);
                    throw new WithDataException("Duplicate entry talent data.", cn.hutool.http.Status.HTTP_PRECON_FAILED, talentDTOV3List);
                }
            } catch (NotFoundException ignored) {
                throw new DuplicateException("Please contact customer service to resolve this issue.");
            }
            stopWatch.stop();
            stopWatch.start("[3.2.1] initResumeData");
            initResumeData(talentDTO.getResumes());
            Long resumeExistTalentId = resumeBindByOther(talentDTO.getResumes(), null);
            if(resumeExistTalentId != null) {
                SuspectedDuplications resumeDuplicate = getResumeDuplicate(resumeExistTalentId);
                throw new WithDataException("Duplicate entry talent data.", cn.hutool.http.Status.HTTP_PRECON_FAILED, List.of(resumeDuplicate));
            }
            talent = TalentV3.fromTalentDTO(talentDTO);
            talent.setOwnedByTenants(talent.getTenantId());
            talent = talentRepository.saveAndFlush(talent);
            stopWatch.stop();
            stopWatch.start("[3.3] setTalentRelateData");
            //contacts, ownership, resumes and locations
            setTalentRelateData(talentDTO, talent.getId(), talent.getCreatedDate());
            stopWatch.stop();
            stopWatch.start("[3.5] setTalentFolder");
            setTalentFolder(talentDTO.getHotListId(), talent.getId());
            stopWatch.stop();
            stopWatch.start("[3.6] transaction commit");
            log.info("[APN: TalentService @{}] create talent success -> result: {}", SecurityUtils.getUserId(), talent);
            entityManager.flush();
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
        stopWatch.stop();
        stopWatch.start("[3.7] asyncGenerateResume");
        asyncGenerateResume(talent.getId(), input);
        stopWatch.stop();
        stopWatch.start("[3.8] confidentialTalent");
        // 如果需要保密，进行保密, 即使保密失败也不会影响到候选人创建成功
        if (input.isConfidential()) {
            try {
                talentConfidentialService.confidentialTalent(talent);
            } catch (Exception e) {
                throw new CustomParameterizedException(HttpStatus.MULTI_STATUS.value(), "Talent confidential failed.", e.getMessage(), Map.of("id", talent.getId()));
            }
        }
        stopWatch.stop();
        log.info("[apn @{}] create end time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return talent.getId();
    }

    private TalentInfoInput translate2TalentInfoInput(String request) {
        TalentInfoInput talentInfo = new TalentInfoInput();
        ObjectMapper mapper = ObjectMapperUtils.getInstance();
        try {
            JsonNode jsonObject = mapper.readTree(request);
            ObjectNode extendedInfo = (ObjectNode) jsonObject.deepCopy();

            // 处理基本字段
            if (isValidField(jsonObject, "talentId")) {
                talentInfo.setTalentId(jsonObject.get("talentId").asLong());
                extendedInfo.remove("talentId");
            }
            if (isValidField(jsonObject, "tenantId")) {
                talentInfo.setTenantId(jsonObject.get("tenantId").asLong());
                extendedInfo.remove("tenantId");
            }
            if (isValidField(jsonObject, "firstName")) {
                talentInfo.setFirstName(jsonObject.get("firstName").asText());
                extendedInfo.remove("firstName");
            }
            if (isValidField(jsonObject, "lastName")) {
                talentInfo.setLastName(jsonObject.get("lastName").asText());
                extendedInfo.remove("lastName");
            }
            if (isValidField(jsonObject, "fullName")) {
                talentInfo.setFullName(jsonObject.get("fullName").asText());
                extendedInfo.remove("fullName");
            }
            if (isValidField(jsonObject, "zipCode")) {
                talentInfo.setZipCode(jsonObject.get("zipCode").asText());
                extendedInfo.remove("zipCode");
            }
            if (isValidField(jsonObject, "contacts")) {
                List<TalentContactDTO> contacts = mapper.convertValue(jsonObject.get("contacts"),
                        new TypeReference<List<TalentContactDTO>>() {});
                talentInfo.setContacts(contacts);
                extendedInfo.remove("contacts");
            }
            if (isValidField(jsonObject, "motivationId")) {
                talentInfo.setMotivationId(jsonObject.get("motivationId").asInt());
                extendedInfo.remove("motivationId");
            }

            if (isValidField(jsonObject, "motivation")) {
                String motivation = extractStatus(jsonObject.get("motivation").asText());
                if(!StringUtils.isEmpty(motivation)) {
                    enumCommonService.findAllEnumMotivation().stream()
                            .filter(enumMotivation -> motivation.equals(enumMotivation.getName()))
                            .findFirst()
                            .map(EnumMotivation::getId)
                            .ifPresent(talentInfo::setMotivationId);
                }
                extendedInfo.remove("motivation");
            }

            if (isValidField(jsonObject, "photoUrl")) {
                talentInfo.setPhotoUrl(jsonObject.get("photoUrl").asText());
                extendedInfo.remove("photoUrl");
            }
            if (isValidField(jsonObject, "creationTalentType")) {
                talentInfo.setCreationTalentType(CreationTalentType.valueOf(jsonObject.get("creationTalentType").asText()));
                extendedInfo.remove("creationTalentType");
            }
            if (isValidField(jsonObject, "companyLocationId")) {
                talentInfo.setCompanyLocationId(jsonObject.get("companyLocationId").asLong());
                extendedInfo.remove("companyLocationId");
            }
            if (isValidField(jsonObject, "currentLocation")) {
                LocationDTO location = mapper.convertValue(jsonObject.get("currentLocation"), LocationDTO.class);
                talentInfo.setCurrentLocation(location);
                extendedInfo.remove("currentLocation");
            }
            if (isValidField(jsonObject, "workAuthorization")) {
                List<EnumWorkAuthorization> allEnumWorkAuthorization = enumCommonService.findAllEnumWorkAuthorization();
                List<Integer> workAuthorization = new ArrayList<>();

                JsonNode jsonNode = jsonObject.get("workAuthorization");
                if (jsonNode.isArray()) {
                    for (int i = 0; i < jsonNode.size(); i++) {
                        JsonNode element = jsonNode.get(i);
                        if (element.isNumber()) {
                            // 如果是数字类型直接添加
                            workAuthorization.add(element.asInt());
                        } else if (element.isTextual()) {
                            String value = element.asText();
                            if (isNumeric(value)) {
                                // 如果是数字字符串，转换为整数
                                try {
                                    workAuthorization.add(Integer.parseInt(value));
                                } catch (NumberFormatException e) {
                                    // 忽略无法解析的值
                                }
                            } else {
                                // 如果是枚举名称，查找对应ID
                                for (EnumWorkAuthorization enumItem : allEnumWorkAuthorization) {
                                    if (enumItem.getName().equalsIgnoreCase(value)) {
                                        workAuthorization.add(enumItem.getId().intValue());
                                        break;
                                    }
                                }
                            }
                        }
                    }
                } else {
                    if (jsonNode.isNumber()) {
                        // 如果是数字类型直接添加
                        workAuthorization.add(jsonNode.asInt());
                    } else if (jsonNode.isTextual()) {
                        String value = jsonNode.asText();
                        if (isNumeric(value)) {
                            // 如果是数字字符串，转换为整数
                            try {
                                workAuthorization.add(Integer.parseInt(value));
                            } catch (NumberFormatException e) {
                                // 忽略无法解析的值
                            }
                        } else {
                            // 如果是枚举名称，查找对应ID
                            for (EnumWorkAuthorization enumItem : allEnumWorkAuthorization) {
                                if (enumItem.getName().equalsIgnoreCase(value)) {
                                    workAuthorization.add(enumItem.getId().intValue());
                                    break;
                                }
                            }
                        }
                    }
                }

                talentInfo.setWorkAuthorization(workAuthorization);
                extendedInfo.remove("workAuthorization");
            }
            if (isValidField(jsonObject, "jobFunctions")) {
                List<Integer> jobFunctions = new ArrayList<>();

                JsonNode jsonNode = jsonObject.get("jobFunctions");
                if (jsonNode.isArray()) {
                    // 如果是数组，处理数组中的每个元素
                    for (int i = 0; i < jsonNode.size(); i++) {
                        JsonNode element = jsonNode.get(i);
                        if (element.isNumber()) {
                            // 如果是数字类型直接添加
                            jobFunctions.add(element.asInt());
                        } else if (element.isTextual()) {
                            String value = element.asText();
                            if (isNumeric(value)) {
                                // 如果是数字字符串，转换为整数
                                try {
                                    jobFunctions.add(Integer.parseInt(value));
                                } catch (NumberFormatException e) {
                                    // 忽略无法解析的值
                                }
                            }
                        }
                    }
                } else {
                    // 如果不是数组，将整个值作为数组的第一个元素处理
                    if (jsonNode.isNumber()) {
                        // 如果是数字类型直接添加
                        jobFunctions.add(jsonNode.asInt());
                    } else if (jsonNode.isTextual()) {
                        String value = jsonNode.asText();
                        if (isNumeric(value)) {
                            // 如果是数字字符串，转换为整数
                            try {
                                jobFunctions.add(Integer.parseInt(value));
                            } catch (NumberFormatException e) {
                                // 忽略无法解析的值
                            }
                        }
                    }
                }

                talentInfo.setJobFunctions(jobFunctions);
                extendedInfo.remove("jobFunctions");
            }
            if (isValidField(jsonObject, "languages")) {
                List<EnumLanguage> allEnumLanguage = enumCommonService.findAllEnumLanguages();
                List<Integer> languages = new ArrayList<>();

                JsonNode jsonNode = jsonObject.get("languages");
                if (jsonNode.isArray()) {
                    for (int i = 0; i < jsonNode.size(); i++) {
                        JsonNode element = jsonNode.get(i);
                        if (element.isNumber()) {
                            // 如果是数字类型直接添加
                            languages.add(element.asInt());
                        } else if (element.isTextual()) {
                            String value = element.asText();
                            if (isNumeric(value)) {
                                // 如果是数字字符串，转换为整数
                                try {
                                    languages.add(Integer.parseInt(value));
                                } catch (NumberFormatException e) {
                                    // 忽略无法解析的值
                                }
                            } else {
                                // 如果是枚举名称，查找对应ID
                                for (EnumLanguage enumItem : allEnumLanguage) {
                                    if (enumItem.getName().equalsIgnoreCase(value)) {
                                        languages.add(enumItem.getId().intValue());
                                        break;
                                    }
                                }
                           }
                        }
                    }
                } else {
                    if (jsonNode.isNumber()) {
                        // 如果是数字类型直接添加
                        languages.add(jsonNode.asInt());
                    } else if (jsonNode.isTextual()) {
                        String value = jsonNode.asText();
                        if (isNumeric(value)) {
                            // 如果是数字字符串，转换为整数
                            try {
                                languages.add(Integer.parseInt(value));
                            } catch (NumberFormatException e) {
                                // 忽略无法解析的值
                            }
                        } else {
                            // 如果是枚举名称，查找对应ID
                            for (EnumLanguage enumItem : allEnumLanguage) {
                                if (enumItem.getName().equalsIgnoreCase(value)) {
                                    languages.add(enumItem.getId().intValue());
                                    break;
                                }
                            }
                        }
                    }
                }

                talentInfo.setLanguages(languages);
                extendedInfo.remove("languages");
            }
            if (isValidField(jsonObject, "industries")) {
                List<EnumIndustry> allEnumIndustry = enumCommonService.findAllEnumIndustry();
                List<Integer> industries = new ArrayList<>();

                JsonNode jsonNode = jsonObject.get("industries");
                if (jsonNode.isArray()) {
                    for (int i = 0; i < jsonNode.size(); i++) {
                        JsonNode element = jsonNode.get(i);
                        if (element.isNumber()) {
                            // 如果是数字类型直接添加
                            industries.add(element.asInt());
                        } else if (element.isTextual()) {
                            String value = element.asText();
                            if (isNumeric(value)) {
                                // 如果是数字字符串，转换为整数
                                try {
                                    industries.add(Integer.parseInt(value));
                                } catch (NumberFormatException e) {
                                    // 忽略无法解析的值
                                }
                            } else {
                                // 如果是枚举名称，查找对应ID
                                for (EnumIndustry enumItem : allEnumIndustry) {
                                    if (enumItem.getName().equalsIgnoreCase(value)) {
                                        industries.add(enumItem.getId().intValue());
                                        break;
                                    }
                                }
                            }
                        }
                    }
                } else {
                    if (jsonNode.isNumber()) {
                        // 如果是数字类型直接添加
                        industries.add(jsonNode.asInt());
                    } else if (jsonNode.isTextual()) {
                        String value = jsonNode.asText();
                        if (isNumeric(value)) {
                            // 如果是数字字符串，转换为整数
                            try {
                                industries.add(Integer.parseInt(value));
                            } catch (NumberFormatException e) {
                                // 忽略无法解析的值
                            }
                        } else {
                            // 如果是枚举名称，查找对应ID
                            for (EnumIndustry enumItem : allEnumIndustry) {
                                if (enumItem.getName().equalsIgnoreCase(value)) {
                                    industries.add(enumItem.getId().intValue());
                                    break;
                                }
                            }
                        }
                    }
                }

                talentInfo.setIndustries(industries);
                extendedInfo.remove("industries");
            }
            if (isValidField(jsonObject, "folderId")) {
                talentInfo.setFolderId(jsonObject.get("folderId").asLong());
                extendedInfo.remove("folderId");
            }
            if (isValidField(jsonObject, "generateResume")) {
                talentInfo.setGenerateResume(jsonObject.get("generateResume").asBoolean());
                extendedInfo.remove("generateResume");
            }
            if (isValidField(jsonObject, "parseResumeUuid")) {
                talentInfo.setParseResumeUuid(jsonObject.get("parseResumeUuid").asText());
                extendedInfo.remove("parseResumeUuid");
            }
            if (isValidField(jsonObject, "resumes")) {
                List<TalentResumeDTO> resumes = mapper.convertValue(jsonObject.get("resumes"),
                        new TypeReference<List<TalentResumeDTO>>() {});
                talentInfo.setResumes(resumes);
                extendedInfo.remove("resumes");
            }
            if (isValidField(jsonObject, "ownerships")) {
                List<TalentOwnershipDTO> ownerships = mapper.convertValue(jsonObject.get("ownerships"),
                        new TypeReference<List<TalentOwnershipDTO>>() {});
                talentInfo.setOwnerships(ownerships);
                extendedInfo.remove("ownerships");
            }
            if (isValidField(jsonObject, "notes")) {
                List<TalentNoteDTO> notes = mapper.convertValue(jsonObject.get("notes"),
                        new TypeReference<List<TalentNoteDTO>>() {});
                talentInfo.setNotes(notes);
                extendedInfo.remove("notes");
            }
            if (isValidField(jsonObject, "confidential")) {
                talentInfo.setConfidential(jsonObject.get("confidential").asBoolean());
                extendedInfo.remove("confidential");
            }
            if (isValidField(jsonObject, "sourceOwnershipPeriod")) {
                List<String> sourceOwnershipPeriod = mapper.convertValue(jsonObject.get("sourceOwnershipPeriod"),
                        new TypeReference<List<String>>() {});
                talentInfo.setSourceOwnershipPeriod(sourceOwnershipPeriod);
                extendedInfo.remove("sourceOwnershipPeriod");
            }

            //非关系型数据
            if (isValidField(jsonObject, "selfEvaluation")) {
                talentInfo.setSelfEvaluation(jsonObject.get("selfEvaluation").asText());
            }
            if (isValidField(jsonObject, "nickName")) {
                talentInfo.setNickName(jsonObject.get("nickName").asText());
            }
            if (isValidField(jsonObject, "birthDate")) {
                talentInfo.setBirthDate(jsonObject.get("birthDate").asText());
            }
            if (isValidField(jsonObject, "preferences")) {
                //生成猎聘简历使用
                List<TalentPreference> originPreference = mapper.convertValue(jsonObject.get("preferences"),
                        new TypeReference<List<TalentPreference>>() {});
                talentInfo.setOriginPreferences(originPreference);

                JsonNode preferences = jsonObject.get("preferences");
                JSONArray preferencesArray = new JSONArray();
                for (int i = 0; i < preferences.size(); i++) {
                    JsonNode preferenceObj = preferences.get(i);
                    // 将 JsonNode 转换为 ObjectNode
                    ObjectNode objectNode = (ObjectNode) preferenceObj;
                    if (isValidField(preferenceObj, "payTimes")) {
                        double payTimes = preferenceObj.get("payTimes").asDouble();
                        RateUnitType payType = isValidField(preferenceObj, "payType") ? RateUnitType.valueOf(preferenceObj.get("payType").asText()) : null;
                        RangeDTO salaryRange = isValidField(preferenceObj, "salaryRange") ? mapper.convertValue(preferenceObj.get("salaryRange"), RangeDTO.class) : null;
                        if(RateUnitType.MONTHLY.equals(payType) && payTimes > 12) {
                            if(salaryRange != null) {
                                if(salaryRange.getGte() != null) {
                                    salaryRange.setGte(salaryRange.getGte().multiply(new BigDecimal(payTimes)));
                                }
                                if(salaryRange.getLte() != null) {
                                    salaryRange.setLte(salaryRange.getLte().multiply(new BigDecimal(payTimes)));
                                }
                                // 覆盖现有的键值
                                objectNode.remove("payTimes");
                                objectNode.putPOJO("salaryRange", salaryRange);
                                objectNode.put("payType", RateUnitType.YEARLY.name());
                            }
                        }
                    }
                    preferencesArray.add(objectNode);
                }

                extendedInfo.putPOJO("preferences", preferencesArray);
                List<TalentPreference> preferencesList = mapper.convertValue(preferencesArray,
                        new TypeReference<List<TalentPreference>>() {});

                talentInfo.setPreferences(preferencesList);
            }
//            if (isValidField(jsonObject, "preferredTitle")) {
//                List<String> preferredTitle = mapper.convertValue(jsonObject.get("preferredTitle"),
//                        new TypeReference<List<String>>() {});
//                talentInfo.setPreferredTitle(preferredTitle);
//                extendedInfo.remove("preferredTitle");
//            }
//            if (isValidField(jsonObject, "preferredLocations")) {
//                List<LocationDTO> preferredLocations = mapper.convertValue(jsonObject.get("preferredLocations"),
//                        new TypeReference<List<LocationDTO>>() {});
//                talentInfo.setPreferredLocations(preferredLocations);
//            }
//            if (isValidField(jsonObject, "preferredCurrency")) {
//                Object value = mapper.convertValue(jsonObject.get("preferredCurrency"), Object.class);
//                talentInfo.setPreferredCurrency(value);
//            }
//            if (isValidField(jsonObject, "preferredPayType")) {
//                Object value = mapper.convertValue(jsonObject.get("preferredPayType"), Object.class);
//                talentInfo.setPreferredPayType(value);
//            }
//            if (isValidField(jsonObject, "preferredPayTimes")) {
//                Object value = mapper.convertValue(jsonObject.get("preferredPayTimes"), Object.class);
//                talentInfo.setPreferredPayTimes(value);
//            }
//            if (isValidField(jsonObject, "preferredSalaryRange")) {
//                Object value = mapper.convertValue(jsonObject.get("preferredSalaryRange"), Object.class);
//                talentInfo.setPreferredSalaryRange(value);
//            }
            if (isValidField(jsonObject, "gender")) {
                talentInfo.setGender(jsonObject.get("gender").asText());
            }
            if (isValidField(jsonObject, "ethnicity")) {
                List<String> ethnicity = mapper.convertValue(jsonObject.get("ethnicity"),
                        new TypeReference<List<String>>() {});
                talentInfo.setEthnicity(ethnicity);
            }
            if (isValidField(jsonObject, "preferredPronoun")) {
                talentInfo.setPreferredPronoun(jsonObject.get("preferredPronoun").asText());
            }
            if (isValidField(jsonObject, "disability")) {
                talentInfo.setDisability(jsonObject.get("disability").asText());
            }
            if (isValidField(jsonObject, "veteran")) {
                talentInfo.setVeteran(jsonObject.get("veteran").asText());
            }
            if (isValidField(jsonObject, "memberOfLGBTQ")) {
                talentInfo.setMemberOfLGBTQ(jsonObject.get("memberOfLGBTQ").asText());
            }
            if (isValidField(jsonObject, "skills")) {
                JsonNode skillsNode = jsonObject.get("skills");
                List<SkillDTO> skills = new ArrayList<>();

                if (skillsNode.isArray()) {
                    for (JsonNode skillNode : skillsNode) {
                        if (skillNode.isObject()) {
                            // 处理对象格式：{"skillName": "logistics", "id": 1}
                            SkillDTO skill = mapper.convertValue(skillNode, SkillDTO.class);
                            skills.add(skill);
                        } else if (skillNode.isTextual()) {
                            // 处理字符串格式："人才搜索"
                            SkillDTO skill = new SkillDTO();
                            skill.setSkillName(skillNode.asText());
                            // id 可以设为 null 或者根据业务需要设置默认值
                            skill.setId(null);
                            skills.add(skill);
                        }
                    }
                }

                talentInfo.setSkills(removeDuplicateSkills(skills));
            }
            if (isValidField(jsonObject, "projects")) {
                List<TalentProjectDTO> projects = mapper.convertValue(jsonObject.get("projects"),
                        new TypeReference<List<TalentProjectDTO>>() {});
                talentInfo.setProjects(projects);
            }
            if (isValidField(jsonObject, "source")) {
                talentInfo.setSource(ResumeSourceType.valueOf(jsonObject.get("source").asText()));
            }
            if (isValidField(jsonObject, "educations")) {
                List<TalentEducationDTO> educations = mapper.convertValue(jsonObject.get("educations"),
                        new TypeReference<List<TalentEducationDTO>>() {});
                talentInfo.setEducations(educations);
            }
            if (isValidField(jsonObject, "payType")) {
                talentInfo.setPayType(com.altomni.apn.common.domain.enumeration.RateUnitType.valueOf(jsonObject.get("payType").asText()));
            }
            if (isValidField(jsonObject, "salaryRange")) {
                RangeDTO salaryRange = mapper.convertValue(jsonObject.get("salaryRange"), RangeDTO.class);
                talentInfo.setSalaryRange(salaryRange);
            }
            //在salaryRange和payType后处理
            if (isValidField(jsonObject, "payTimes")) {
                double payTimes = jsonObject.get("payTimes").asDouble();
                RateUnitType payType = talentInfo.getPayType();
                if(RateUnitType.MONTHLY.equals(payType) && payTimes > 12) {
                    RangeDTO salaryRange = talentInfo.getSalaryRange();
                    if(salaryRange != null) {
                        if(salaryRange.getGte() != null) {
                            salaryRange.setGte(salaryRange.getGte().multiply(new BigDecimal(payTimes)));
                        }
                        if(salaryRange.getLte() != null) {
                            salaryRange.setLte(salaryRange.getLte().multiply(new BigDecimal(payTimes)));
                        }
                        talentInfo.setSalaryRange(salaryRange);
                        talentInfo.setPayType(RateUnitType.YEARLY);
                        extendedInfo.putPOJO("salaryRange", salaryRange);
                        extendedInfo.put("payType", RateUnitType.YEARLY.name());
                    }
                }
                extendedInfo.remove("payTimes");
            }
            if (isValidField(jsonObject, "currency")) {
                talentInfo.setCurrency(jsonObject.get("currency").asText());
            }
            if (isValidField(jsonObject, "experiences")) {
                ArrayNode experiencesArray = (ArrayNode) jsonObject.get("experiences");
                for (int i = 0; i < experiencesArray.size(); i++) {
                    ObjectNode experienceJson = (ObjectNode) experiencesArray.get(i);
                    experienceJson.remove("deletable");
                }
                List<TalentExperienceDTO> experiences = mapper.convertValue(experiencesArray,
                        new TypeReference<List<TalentExperienceDTO>>() {});
                talentInfo.setExperiences(experiences);
                extendedInfo.set("experiences", experiencesArray);
            }
            if (isValidField(jsonObject, "annualSalaryInUSD")) {
                RangeDTO annualSalaryInUSD = mapper.convertValue(jsonObject.get("annualSalaryInUSD"), RangeDTO.class);
                talentInfo.setAnnualSalaryInUSD(annualSalaryInUSD);
            }
            if (isValidField(jsonObject, "certificates")) {
                List<TalentCertificateDTO> certificates = mapper.convertValue(jsonObject.get("certificates"),
                        new TypeReference<List<TalentCertificateDTO>>() {});
                talentInfo.setCertificates(certificates);
            }

            // 移除前端专用字段
            List<String> frontendFields = Arrays.asList(
                    "id", "clientContactCompanyId", "companyAffiliations", "esId", "addressLine", "isAM",
                    "creditTransactionId", "additionalInfoId", "hotListId", "recommendFeedback",
                    "createdUser", "lastModifiedUser", "applicationCount", "purchased",
                    "createdBy", "createdDate", "lastModifiedBy", "lastModifiedDate",
                    "createdUser", "lastModifiedUser", "agencyId", "recruiterMode",
                    "confidential", "confidentialTalentViewAble", "payTimes", "preferredPayTimes"
            );
            frontendFields.forEach(extendedInfo::remove);

            // 设置扩展信息
            if (!extendedInfo.isEmpty()) {
                String jsonString = JSON.toJSONString(JSONUtil.parseObj(extendedInfo.toString()), (PropertyFilter) (o, s, o1) -> ObjectUtil.isNotEmpty(o1));
                talentInfo.setExtendedInfo(jsonString);
            }
        } catch (JsonProcessingException e) {
            throw new CustomParameterizedException("Talent input request param error, input:{}", request);
        }

        return talentInfo;
    }

    public List<SkillDTO> removeDuplicateSkills(List<SkillDTO> skills) {
        if (skills == null || skills.isEmpty()) {
            return new ArrayList<>();
        }

        return skills.stream()
                .filter(skill -> skill.getSkillName() != null) // 过滤掉skillName为null的项
                .collect(Collectors.toMap(
                        SkillDTO::getSkillName,
                        skill -> skill,
                        (existing, replacement) -> existing, // 保留第一个出现的
                        LinkedHashMap::new // 保持原有顺序
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
    }

    // 辅助方法：检查字符串是否可以解析为数字
    private boolean isNumeric(String str) {
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static String extractStatus(String statusString) {
        // 处理 null 和空字符串
        if (statusString == null || statusString.trim().isEmpty()) {
            return null;
        }

        String trimmed = statusString.trim();

        // 查找冒号位置
        int colonIndex = trimmed.indexOf(':');

        // 没有找到冒号
        if (colonIndex == -1) {
            return null;
        }

        // 冒号在最后位置，没有状态名称
        if (colonIndex == trimmed.length() - 1) {
            return null;
        }

        // 提取冒号后的部分
        String status = trimmed.substring(colonIndex + 1).trim();

        // 状态名称为空
        if (status.isEmpty()) {
            return null;
        }

        return status;
    }

    /**
     * 检查JsonNode中的字段是否有效（非null且非空字符串）
     *
     * @param node JsonNode对象
     * @param fieldName 字段名
     * @return 字段是否有效
     */
    private static boolean isValidField(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull()
                && !(fieldNode.isTextual() && fieldNode.asText().trim().isEmpty());
    }


    private void asyncGenerateResume(Long id, TalentInfoInput input) {
        SecurityContext context = SecurityContextHolder.getContext();
        ConcurrentStopWatch stopWatch = new ConcurrentStopWatch("resume stop watch");
        CompletableFuture.runAsync(() -> {
            try {
                SecurityContextHolder.setContext(context);
                if(!input.isGenerateResume()) {
                    return;
                }
                stopWatch.start("GenerateResume");
                GenerateResumeDTO generateResumeDTO = generateResume(input);
                stopWatch.stop();
                log.info("GenerateResume time = {}ms ", stopWatch.getTotalTimeMillis());
                stopWatch.start("saveResumeAndRelation");
                saveResumeAndRelation(id, generateResumeDTO.getUuid(), generateResumeDTO.getLiePinPDFName(), generateResumeDTO.getDataMD5(), generateResumeDTO.getText());
                stopWatch.stop();
                log.info("saveResumeAndRelation time = {}ms ", stopWatch.getTotalTimeMillis());
            } catch (Exception e) {
                String message = "Sync generate liepin resume error : " +
                        "\n\tTalent ID: " + id + "\n " +
                        "\n\tError message:" +
                        "\n\t\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
                log.error("[APN: Talent @{}] generate liepin resume error, talent id : {}， error: {}", SecurityUtils.getUserId(), id, JSONUtil.toJsonStr(e));
                throw e;
            }
        });
    }

    @Override
    public SuspectedDuplications getResumeDuplicate(Long id) {
        SuspectedDuplications contactExist = new SuspectedDuplications();
        contactExist.set_id(id.toString());
        contactExist.set_index("talents_" + SecurityUtils.getTenantId());
        contactExist.set_similarity(new BigDecimal(1));
        Optional<TalentV3> opt = talentRepository.findById(id);
        if(opt.isPresent()) {
            TalentV3 talentV3 = opt.get();
            TalentDTOV3 talentDTOV3 = TalentDTOV3.fromTalent(talentV3);
            List<TalentExperienceDTO> talentExperienceDTOS = sortTalentExperiences(talentDTOV3.getExperiences());
            contactExist.setFullName(talentV3.getFullName());
            if(talentExperienceDTOS != null && !talentExperienceDTOS.isEmpty()) {
                TalentExperienceDTO experienceDTO = talentExperienceDTOS.get(0);
                contactExist.setCompanyName(experienceDTO.getCompanyName());
                contactExist.setTitle(experienceDTO.getTitle());
            }
        }
        Instant talentEsLastModifiedTime = talentRepository.getTalentEsLastModifiedTime(id);
        contactExist.setLastModifiedDate(com.altomni.apn.common.utils.DateUtil.fromInstantToUtcDateTimeWithMillisecond(talentEsLastModifiedTime));
        contactExist.setDuplicateResume(true);
        return contactExist;
    }

    @Override
    public TalentDTOV3 createAndResult(String requestBody){
        StopWatch stopWatch = new StopWatch("createAndResult");
        stopWatch.start("[1] mergeByRedisData");
        String merge = mergeByRedisData(requestBody);
        stopWatch.stop();
        stopWatch.start("[2] translate2TalentInfoInput");
        TalentInfoInput input = translate2TalentInfoInput(merge);
        stopWatch.stop();
        stopWatch.start("[3] create");
        Long talentId =  create(input);
        stopWatch.stop();
        stopWatch.start("[4] findTalentById");
        TalentDTOV3 result = new TalentDTOV3();
        result.setId(talentId);
        //TalentDTOV3 result = findTalentById(talentId, false);
        stopWatch.stop();
        log.info("[apn @{}] createAndResult time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return result;
    }

    private String mergeByRedisData(String requestBody) {
        JSONObject jsonObject = JSONUtil.parseObj(requestBody);
        String parseResumeUuid = jsonObject.getStr("parseResumeUuid");

        if (ObjectUtil.isNotNull(parseResumeUuid)) {
            ParserRedisResponse response = getResumeParseResponse(parseResumeUuid);
            if (ObjectUtil.isNotEmpty(response.getStatus()) && ParseStatus.FINISHED.equals(response.getStatus()) && ObjectUtil.isNotEmpty(response.getData())) {
                String extendedInfoRedis = response.getData();
                requestBody = TalentV3.mergeExtendedInfoV3(requestBody, extendedInfoRedis);
            }
        }

        return addIdsToArrayObjects(requestBody);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, noRollbackFor = WithDataException.class)
    public Long createByExcel(TalentDTOV3 talentDTO, String json, String notes, String md5, String noteType) {
        log.info("[APN: TalentService ] create talent by excel v3 -> talentDTO: {}", json);
        checkTalentRequiredFiled(talentDTO);
        setTalentDefaultValue(talentDTO);
        removeEnumsNull(talentDTO);
        TalentSimilarityDto talentSimilarityDto = new TalentSimilarityDto();
        BeanUtil.copyProperties(talentDTO, talentSimilarityDto);
        log.info("startCheckDuplicationTalent");
        LinkedList<Long> talentIds = searchTalentsByContactAndSimilarityByExcel(talentSimilarityDto, json);
        if (CollUtil.isNotEmpty(talentIds)) {
            if (ObjectUtil.isNotEmpty(talentDTO.getHotListId())) {
                Long talentId = new ArrayList<>(talentIds).get(0);
                setTalentFolder(talentDTO.getHotListId(), talentId);
                talentRepository.updateTalentLastEditedTime(talentId);
                return talentId;
            }
            updateTalentByExcel(talentDTO, talentIds, md5);
            setTalentNoteByTalentIds(notes, talentIds, noteType);
            throw new WithDataException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_CREATE_DUPLICATETALENTDATA.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()), cn.hutool.http.Status.HTTP_PRECON_FAILED, null);
        }
        TalentV3 talent = TalentV3.fromTalentDTO(talentDTO);
        talent.setOwnedByTenants(talent.getTenantId());
        talent = talentRepository.save(formatFromJson(talent, json));
        setExcelTalentRelateEntityByExcel(talentDTO, talent);
        setTalentFolder(talentDTO.getHotListId(), talent.getId());
        setTalentNote(notes, talent.getId(), noteType);
        return talent.getId();
    }

    private void updateTalentByExcel(TalentDTOV3 talentDTO, LinkedList<Long> talentIds, String md5) {
        List<EnumRelationDTO> jobFunctions = talentDTO.getJobFunctions();
        List<EnumRelationDTO> industries = talentDTO.getIndustries();
        List<TalentEducationDTO> educations = talentDTO.getEducations();
        List<TalentExperienceDTO> experiences = talentDTO.getExperiences();
        List<TalentProjectDTO> projects = talentDTO.getProjects();
        Long talentId = talentIds.getFirst();
        talentRepository.findById(talentId).ifPresent(old -> {
            com.alibaba.fastjson.JSONObject oldJson = new com.alibaba.fastjson.JSONObject();
            com.alibaba.fastjson.JSONObject newJson = new com.alibaba.fastjson.JSONObject();
            newJson.put("educations", educations);
            newJson.put("experiences", experiences);
            newJson.put("industries", projects);
            // jobfunction 和 industry 做完全覆盖
            talentJobFunctionRelationRepository.deleteAllByTalentId(talentId);
            if (CollUtil.isNotEmpty(jobFunctions)) {
                List<TalentJobFunctionRelation> talentJobFunctionRelationList = jobFunctions.stream()
                        .filter(jobFunction -> StrUtil.isNotBlank(jobFunction.getEnumId()))
                        .map(jobFunction -> {
                            TalentJobFunctionRelation talentJobFunctionRelation = new TalentJobFunctionRelation();
                            talentJobFunctionRelation.setTalentId(talentId);
                            talentJobFunctionRelation.setEnumId(Integer.parseInt(jobFunction.getEnumId()));
                            return talentJobFunctionRelation;
                        }).collect(Collectors.toList());
                talentJobFunctionRelationRepository.saveAll(talentJobFunctionRelationList);
                newJson.put("jobFunctions", talentJobFunctionRelationList);
            }
            talentIndustryRelationRepository.deleteAllByTalentId(talentId);
            if (CollUtil.isNotEmpty(industries)) {
                List<TalentIndustryRelation> talentIndustryRelationList = industries.stream()
                        .filter(jobFunction -> StrUtil.isNotBlank(jobFunction.getEnumId()))
                        .map(industry -> {
                            TalentIndustryRelation talentIndustryRelation = new TalentIndustryRelation();
                            talentIndustryRelation.setTalentId(talentId);
                            talentIndustryRelation.setEnumId(Integer.parseInt(industry.getEnumId()));
                            return talentIndustryRelation;
                        }).collect(Collectors.toList());
                talentIndustryRelationRepository.saveAll(talentIndustryRelationList);
                newJson.put("industries", talentIndustryRelationList);
            }
            if (CollUtil.isNotEmpty(old.getJobFunctions())) {
                oldJson.put("jobFunctions", old.getJobFunctions());
            }
            if (CollUtil.isNotEmpty(old.getIndustries())) {
                oldJson.put("industries", old.getIndustries());
            }
            com.alibaba.fastjson.JSONObject extendJson = new com.alibaba.fastjson.JSONObject();
            extendJson.put("educations", educations);
            extendJson.put("experiences", experiences);
            extendJson.put("projects", projects);
            String extendInfo = old.getTalentExtendedInfo();
            if (ObjectUtil.isNotEmpty(old.getAdditionalInfoId()) && StrUtil.isNotBlank(extendInfo)) {
                //有历史数据
                com.alibaba.fastjson.JSONObject oldExtendJson = JSON.parseObject(extendInfo);
                JSONArray oldEductions = oldExtendJson.getJSONArray("educations");
                JSONArray oldExperiences = oldExtendJson.getJSONArray("experiences");
                JSONArray oldProjects = oldExtendJson.getJSONArray("projects");
                if (CollUtil.isNotEmpty(oldEductions)) {
                    oldJson.put("educations", oldEductions);
                }
                if (CollUtil.isNotEmpty(oldExperiences)) {
                    oldJson.put("experiences", oldExperiences);
                }
                if (CollUtil.isNotEmpty(oldProjects)) {
                    oldJson.put("projects", oldProjects);
                }
                oldExtendJson.putAll(extendJson);
                talentAdditionalInfoRepository.updateExtendInfoById(old.getAdditionalInfoId(), JSON.toJSONString(oldExtendJson));
            }
            if (ObjectUtil.isEmpty(old.getAdditionalInfoId())) {
                TalentAdditionalInfo talentAdditionalInfo = new TalentAdditionalInfo();
                talentAdditionalInfo.setExtendedInfo(JSON.toJSONString(extendInfo));
                talentAdditionalInfoRepository.save(talentAdditionalInfo);
                talentRepository.updateTalenAdditionalInfoIdById(old.getId(), talentAdditionalInfo.getId());
            }
            old.setLastModifiedDate(Instant.now());
            old.setLastModifiedBy(SecurityUtils.getUserUid());
            talentRepository.save(old);

            ExcelUpdateTalentRecord excelUpdateTalentRecord = excelUpdateTalentRecordRepository.findExcelUpdateTalentRecordByTalentId(old.getId());
            if (excelUpdateTalentRecord != null) {
                excelUpdateTalentRecord.setNewData(JSON.toJSONString(newJson));
            } else {
                excelUpdateTalentRecord = new ExcelUpdateTalentRecord();
                excelUpdateTalentRecord.setTalentId(old.getId());
                excelUpdateTalentRecord.setTenantId(old.getTenantId());
                excelUpdateTalentRecord.setOldData(JSON.toJSONString(oldJson));
                excelUpdateTalentRecord.setNewData(JSON.toJSONString(newJson));
                excelUpdateTalentRecord.setMd5(md5);
            }
            excelUpdateTalentRecordRepository.save(excelUpdateTalentRecord);
            log.info("excel update talentId = {}, param = {}", talentId, JSONUtil.toJsonStr(newJson));
        });
    }

    private void setHotListTalent(Long hotListId, Long talentId) {
        if (ObjectUtil.isNotEmpty(hotListId) && ObjectUtil.isNotEmpty(talentId)) {
            List<HotListTalent> hotListTalentList = hotListTalentRepository.findAllByHotListIdAndTalentId(hotListId, talentId);
            if (CollUtil.isEmpty(hotListTalentList)) {
                HotListTalent hotListTalent = new HotListTalent();
                hotListTalent.setHotListId(hotListId);
                hotListTalent.setTalentId(talentId);
                hotListTalentRepository.save(hotListTalent);
            }
        }
    }

    private void setTalentFolder(Long folderId, Long talentId) {
        if (ObjectUtil.isNotEmpty(folderId) && ObjectUtil.isNotEmpty(talentId)) {
            List<TalentFolderRelation> talentFolderList = talentFolderRelationRepository.findAllByTalentFolderIdAndTalentId(folderId, talentId);
            if (CollUtil.isEmpty(talentFolderList)) {
                TalentFolderRelation talentFolderRelation = new TalentFolderRelation();
                talentFolderRelation.setTalentFolderId(folderId);
                talentFolderRelation.setTalentId(talentId);
                talentFolderRelationRepository.saveAndFlush(talentFolderRelation);
                talentRepository.updateTalentLastEditedTime(talentId);
            }
        }
    }

    private void setTalentNote(String notes, Long talentId, String noteType) {
        if (StrUtil.isNotBlank(notes)) {
            TalentNote talentNote = new TalentNote();
            talentNote.setNote(notes);
            talentNote.setTitle("create talent by excel");
            talentNote.setTalentId(talentId);
            TalentNoteType talentNoteType = TalentNoteType.getNoteTypeByName(noteType);
            talentNote.setNoteType(talentNoteType);
            if (talentNoteType == OTHERS && StrUtil.isNotBlank(noteType)) {
                Map<String, Object> map = new HashMap<>(16);
                map.put("otherContactInfo", noteType);
                talentNote.setAdditionalInfo(map);
            }
            talentNote.setUserId(SecurityUtils.getUserId());
            TalentNote save = talentNoteRepository.save(talentNote);
            talentNoteService.notifyNoteEnrich(save);
        }
    }

    private void setTalentNoteByTalentIds(String notes, List<Long> talentIdList, String noteType) {
        if (StrUtil.isNotBlank(notes)) {
            List<TalentNote> talentNoteList = talentIdList.stream().distinct().map(talentId -> {
                TalentNote talentNote = new TalentNote();
                talentNote.setNote(notes);
                talentNote.setTitle("update talent by excel");
                talentNote.setTalentId(talentId);
                TalentNoteType talentNoteType = TalentNoteType.getNoteTypeByName(noteType);
                talentNote.setNoteType(talentNoteType);
                if (talentNoteType == OTHERS && StrUtil.isNotBlank(noteType)) {
                    Map<String, Object> map = new HashMap<>(16);
                    map.put("otherContactInfo", noteType);
                    talentNote.setAdditionalInfo(map);
                }
                talentNote.setUserId(SecurityUtils.getUserId());
                return talentNote;
            }).toList();
            talentNoteRepository.saveAllAndFlush(talentNoteList);
        }
    }

    private void setTalentRelateData(TalentDTOV3 talentDTO, Long id, Instant createDate) {
        //talent contacts
        if (CollUtil.isNotEmpty(talentDTO.getContacts())) {
            List<TalentContactDTO> contacts = talentDTO.getContacts().stream().filter(c -> ContactTypeConstants.COMPREHENSIVE_CONTACT_TYPES.contains(c.getType())).collect(Collectors.toList());
            cleanUpContacts(contacts);

            List<TalentContactDTO> contactSet = new ArrayList<>();
            for (TalentContactDTO t: contacts) {
                t.setTenantId(SecurityUtils.getTenantId());
                t.setTalentId(id);
                t.setStatus(TalentContactStatus.AVAILABLE);
                t.setId(null);
                contactSet.add(t);
            }
            talentDTO.setContacts(Convert.toList(TalentContactDTO.class, talentContactRepository.saveAllAndFlush(Convert.toList(TalentContact.class, contactSet))));
        } else {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_SETTALENTRELATEDATA_CONTACTSNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }

        //ownerships
        if (CollUtil.isNotEmpty(talentDTO.getOwnerships())) {
            talentDTO.getOwnerships().forEach(talentOwnershipDTO -> talentOwnershipDTO.setId(null));
            List<TalentOwnershipDTO> talentOwnershipDTOS = talentOwnershipService.create(id, talentDTO.getOwnerships(), createDate);
            talentDTO.setOwnerships(talentOwnershipDTOS);
        } else {
            //如果通过上传简历的方式创建候选人，且没有ownership，则默认添加一个ownership
            createOwnerships(id, createDate);
        }

        //talent resumes
        List<TalentResumeDTO> resumes = talentDTO.getResumes();
        if (CollUtil.isNotEmpty(resumes)) {
            List<TalentResumeRelation> talentResumeRelations = resumes.stream().filter(r -> r.getResumeId() != null).map(talentResumeDTO -> {
                TalentResumeRelation talentResumeRelation = new TalentResumeRelation();
                talentResumeRelation.setResumeId(talentResumeDTO.getResumeId());
                talentResumeRelation.setTalentId(id);
                talentResumeRelation.setFileName(talentResumeDTO.getFileName());
                talentResumeRelation.setSourceType(talentResumeDTO.getSourceType());
                talentResumeRelation.setTenantId(SecurityUtils.getTenantId());
                talentResumeRelation.setStatus(CommonDataStatus.AVAILABLE);
                return talentResumeRelation;
            }).collect(Collectors.toList());
            if (!talentResumeRelations.isEmpty()) {
                talentResumeRelationRepository.saveAllAndFlush(talentResumeRelations);
            }
        }

        //talent location
        if (ObjectUtil.isNotEmpty(talentDTO.getCurrentLocation()) || StringUtils.isNotEmpty(talentDTO.getZipCode())) {
            TalentCurrentLocation location = new TalentCurrentLocation();
            location.setTalentId(id);
            location.setOriginalLoc(JSONUtil.toJsonStr(talentDTO.getCurrentLocation()));
            location.setZipCode(talentDTO.getZipCode());
            talentLocationRepository.saveAndFlush(location);
        }
    }

    @Override
    public void cleanUpContacts(List<TalentContactDTO> contacts) {
        Map<String, TalentContactDTO> map = new HashMap<>();
        Iterator<TalentContactDTO> iterator = contacts.iterator();
        while (iterator.hasNext()) {
            TalentContactDTO contact = iterator.next();
            if (ContactType.PHONE.equals(contact.getType()) || ContactType.PRIMARY_PHONE.equals(contact.getType())) {
                String phoneNumber = contact.getContact();
                if (MapUtils.isEmpty(map)) {
                    map.put(phoneNumber, contact);
                } else {
                    String removeKey = null;
                    TalentContactDTO toAdd = null;

                    boolean addCurrent = false;
                    boolean noMatch = true;
                    for (String existPhoneNumber: map.keySet()) {
                        if (ContactUtil.similarityCheck(phoneNumber, existPhoneNumber)) { //found similar phone number
                            noMatch = false;
                            TalentContactDTO exist = map.get(existPhoneNumber);
                            if (TalentContactVerificationStatus.WRONG_CONTACT.equals(exist.getVerificationStatus())) {
                                break;
                            }

                            if (TalentContactVerificationStatus.WRONG_CONTACT.equals(contact.getVerificationStatus())) {
                                removeKey = existPhoneNumber;
                                addCurrent = true;
                                break;
                            }

                            String finalContact = existPhoneNumber.length() >= phoneNumber.length() ? existPhoneNumber : phoneNumber;
                            if (Objects.nonNull(contact.getId())) {
                                contact.setContact(finalContact);
                                if (StringUtils.isEmpty(contact.getDetails()) && StringUtils.isNotEmpty(exist.getDetails())) {
                                    contact.setDetails(exist.getDetails());
                                }
                                removeKey = existPhoneNumber;
                                addCurrent = true;
                            } else {
                                exist.setContact(finalContact);
                                if (StringUtils.isEmpty(exist.getDetails()) && StringUtils.isNotEmpty(contact.getDetails())) {
                                    exist.setDetails(contact.getDetails());
                                }
                            }

                            break;
                        }
                    }

                    if (StringUtils.isNotEmpty(removeKey)) {
                        map.remove(removeKey);
                    }

                    if (addCurrent || noMatch) {
                        map.put(phoneNumber, contact);
                    }
                }

                iterator.remove(); // 使用 iterator.remove() 避免 ConcurrentModificationException
            }
        }

        contacts.addAll(map.values());
    }

    private void createOwnerships(Long talentId, Instant createDate) {
        List<TalentOwnershipDTO> talentOwnerships  = new ArrayList<>();
        TalentOwnershipDTO talentOwnershipDTO = new TalentOwnershipDTO();
        talentOwnershipDTO.setUserId(SecurityUtils.getUserId());
        talentOwnershipDTO.setOwnershipType(TalentOwnershipType.TALENT_OWNER);
        talentOwnerships.add(talentOwnershipDTO);
        talentOwnershipService.create(talentId, talentOwnerships, createDate);
    }

    private void initResumeData(List<TalentResumeDTO> resumes) {
        if(resumes == null) {
            return;
        }
        for (TalentResumeDTO resume : resumes) {
            String uuid = resume.getUuid();
            if (uuid == null) {
                continue;
            }
            ParserRedisResponse response = commonRedisService.getParserResumeData(uuid);
            if (ObjectUtil.isNotEmpty(response.getStatus()) && ParseStatus.FINISHED.equals(response.getStatus())) {
                Resume save = resumeRepository.findByUuid(uuid);
                if(save == null) {
                    save = new Resume();
                }
                if (ObjectUtil.isNotEmpty(response.getData())) {
                    JSONObject jsonObject = JSONUtil.parseObj(response.getData());
                    save.setParseResult(response.getData());
                    save.setText(jsonObject.getStr("text"));
                    save.setSkillsText(jsonObject.getStr("skillsText"));
                }
                //create resume
                save.setUuid(uuid);
                //update resume imageInfo
                if (ObjectUtil.isNotEmpty(response.getImagesInfo())) {
                    if (ObjectUtil.isNotNull(response.getImagesInfo().getHas_portrait())) {
                        save.setHasPortrait(BooleanUtil.toBoolean(response.getImagesInfo().getHas_portrait().toString()));
                    }
                    save.setNPages(response.getImagesInfo().getN_pages());
                }
                save = resumeRepository.saveAndFlush(save);
                resume.setResumeId(save.getId());
            }
        }
    }

    private void checkDuplicates(TalentV3 talent) {
        BigDecimal result = esFillerTalentService.checkTalentDuplication(talent);
        if (result.compareTo(applicationProperties.getSimilarity()) >= 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_CREATE_DUPLICATETALENTDATA.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
    }

    private boolean checkTalentResumeExists(TalentDTOV3 talentDTO) {
        return CollUtil.isNotEmpty(talentResumeService.getTalentIdsByResumeDTOS(talentDTO.getResumes()));
    }

    @Resource
    private EsfillerMQProperties esfillerMQProperties;


    private List<TalentExperienceDTO> getMergedAndSortedExperiences(List<TalentExperienceDTO> existingExperiences, List<TalentExperienceDTO> newExperiences){
        if(newExperiences == null) {
            return new ArrayList<>();
        }
        for (TalentExperienceDTO existingExperience : existingExperiences) {
            // 原有的experience可能存储着还未到达start date的工作经历，这些前端不会传过来，为了防止丢失，需要加进来
            if (Objects.nonNull(existingExperience.getStartDate()) && existingExperience.getStartDate().isAfter(LocalDate.now())){
                newExperiences.add(existingExperience);
            }
        }
        return newExperiences.stream().sorted(TalentExperienceDTO.COMPARATOR).collect(Collectors.toList());
    }


    public static String addIdsToArrayObjects(String jsonString) {
        return addIdsToArrayObjects(jsonString, Set.of("experiences", "educations", "projects", "preferences"));
    }

    /**
     * 给JSON字符串中指定key的数组对象中不含id的对象添加自增id属性
     * id从1开始，或者从已存在的对象中最大id开始自增
     *
     * @param jsonString JSON格式字符串
     * @param keysToProcess 需要处理的数组的key集合，如果为null或空，则处理所有数组
     * @return 处理后的JSON字符串
     */
    public static String addIdsToArrayObjects(String jsonString, Set<String> keysToProcess) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return jsonString;
        }

        try {
            // 解析JSON字符串
            Object jsonObj = com.alibaba.fastjson.JSON.parse(jsonString);
            // 处理JSON对象
            Object processedObj = processJsonObject(jsonObj, keysToProcess, null);
            // 转换回字符串
            return com.alibaba.fastjson.JSON.toJSONString(processedObj);
        } catch (Exception e) {
            // 处理异常，如果输入不是有效的JSON，则返回原始字符串
            return jsonString;
        }
    }

    /**
     * 递归处理JSON对象，为指定key的数组中的对象添加id
     *
     * @param obj JSON对象或数组
     * @param keysToProcess 需要处理的数组的key集合，如果为null或空，则处理所有数组
     * @param currentKey 当前处理的key，用于判断是否是指定key
     * @return 处理后的对象
     */
    private static Object processJsonObject(Object obj, Set<String> keysToProcess, String currentKey) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof com.alibaba.fastjson.JSONObject jsonObject) {
            // 处理JSON对象
            for (String key : jsonObject.keySet()) {
                Object value = jsonObject.get(key);
                jsonObject.put(key, processJsonObject(value, keysToProcess, key));
            }
            return jsonObject;
        } else if (obj instanceof JSONArray jsonArray) {
            // 处理JSON数组

            // 判断是否需要处理当前数组
            boolean shouldProcess = keysToProcess == null || keysToProcess.isEmpty() ||
                    (currentKey != null && keysToProcess.contains(currentKey));

            // 检查数组中是否含有JSON对象
            boolean hasJsonObjects = false;
            for (int i = 0; i < jsonArray.size(); i++) {
                if (jsonArray.get(i) instanceof com.alibaba.fastjson.JSONObject) {
                    hasJsonObjects = true;
                    break;
                }
            }

            // 如果数组不包含JSON对象，或者不需要处理当前数组，则仅递归处理其内容
            if (!hasJsonObjects || !shouldProcess) {
                // 递归处理数组中的每个元素
                for (int i = 0; i < jsonArray.size(); i++) {
                    Object item = jsonArray.get(i);
                    jsonArray.set(i, processJsonObject(item, keysToProcess, null));
                }
                return jsonArray;
            }

            // 首先递归处理数组中的每个元素
            for (int i = 0; i < jsonArray.size(); i++) {
                Object item = jsonArray.get(i);
                jsonArray.set(i, processJsonObject(item, keysToProcess, null));
            }

            // 找出数组中已有的最大id
            long maxId = 0;
            for (int i = 0; i < jsonArray.size(); i++) {
                Object item = jsonArray.get(i);
                if (item instanceof com.alibaba.fastjson.JSONObject jsonObj) {
                    if (jsonObj.containsKey("id")) {
                        Object idObj = jsonObj.get("id");
                        if (idObj != null) {
                            try {
                                // 尝试将id转换为长整型
                                long id;
                                if (idObj instanceof Number) {
                                    id = ((Number) idObj).longValue();
                                } else {
                                    id = Long.parseLong(idObj.toString());
                                }
                                if (id > maxId) {
                                    maxId = id;
                                }
                            } catch (NumberFormatException e) {
                                // 忽略非数字id
                            }
                        }
                    }
                }
            }

            // 如果没有找到有效id，从1开始
            if (maxId == 0) {
                maxId = 0; // 从1开始自增
            }

            // 为没有id的对象添加id
            for (int i = 0; i < jsonArray.size(); i++) {
                Object item = jsonArray.get(i);
                if (item instanceof com.alibaba.fastjson.JSONObject jsonObj) {
                    if (!jsonObj.containsKey("id")) {
                        jsonObj.put("id", ++maxId); // 自增并赋值
                    }
                }
            }

            return jsonArray;
        } else {
            // 其他类型直接返回
            return obj;
        }
    }

    @Override
    public TalentDTOV3 update(Long id, String requestBody) {
        StopWatch stopWatch = new StopWatch("update");
        stopWatch.start("[1] findTalentById");
        TalentV3 dbTalent = talentRepository.findById(id).orElseThrow(() -> new CustomParameterizedException("The talent to update dose not exist"));
        stopWatch.stop();
        stopWatch.start("[2] mergeByRedisOrDbData");
        Map<String, Object> mergeResult = mergeByEnrichNote(requestBody);
        requestBody = (String) mergeResult.get("mergedJson");
        Map<Long, String> enrichedNotes = mergeResult.get("noteEnriched") == null ? null : (Map<Long, String>) mergeResult.get("noteEnriched");
        TalentInfoInput input = mergeByRedisOrDbData(dbTalent, requestBody);

        removeEnumsNull(input);
        if(!hasRemain(id)) {
            throw new CustomParameterizedException(Status.TOO_MANY_REQUESTS.getStatusCode(), "No limit", "Today's browsing limit has been reached");
        }
        if(!checkOwnership(input.getOwnerships())) {
            throw new DuplicateException("A person cannot be both an owner and a share.");
        }

        stopWatch.stop();
        stopWatch.start("[3] setExperiences");
        // 重新组装 talent experience, 有些系统自动增加的 experience 不能被删除
        input.setExperiences(this.getMergedAndSortedExperiences(this.getExperiencesFromExtendedInfo(dbTalent.getTalentAdditionalInfo().getExtendedInfo()), input.getExperiences()));

        TalentDTOV3 update = talentInfoMapper.toTalentDTO(input);
        update.setId(id);
        //候选人查重
        try {
            stopWatch.stop();
            stopWatch.start("[4] startCheckDuplicationTalent");
            log.info("startCheckDuplicationTalent");
            TalentV3 talentCheckDuplication = TalentV3.fromTalentDTO(update);
            TalentSimilarityDto talentSimilarityDto = new TalentSimilarityDto();
            talentSimilarityDto.setCurrentLocation(input.getCurrentLocation());
            List<SuspectedDuplications> talentDuplication = getSuspectededDuplicationsByContactAndSmilarity(talentCheckDuplication, update.getContacts(), talentSimilarityDto, id);
            if(CollectionUtils.isNotEmpty(talentDuplication)) {
                throw new WithDataException("Duplicate entry talent data.", cn.hutool.http.Status.HTTP_PRECON_FAILED, talentDuplication);
            }
        } catch (NotFoundException ignored) {
            throw new DuplicateException("Please contact customer service to resolve this issue.");
        }
        stopWatch.stop();
        stopWatch.start("[5] checkExistApproverEmail");
        // 如果候选人存在approver邮箱，那么需要判定不允许修改此邮箱
        checkExistApproverEmail(update);

        setTalentDefaultValue(update);
        stopWatch.stop();
        stopWatch.start("[6] checkPermission");
        checkPermission(dbTalent);
        stopWatch.stop();
        stopWatch.start("[7] checkEditableDataPermission");
        this.checkEditableDataPermission(dbTalent);
        stopWatch.stop();
        stopWatch.start("[8] checkModifyOwnershipPermission");
        checkModifyOwnershipPermission(dbTalent, update);

        // 手动控制事务
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);
        TalentV3 talentV3;
        try {
            update.setTenantId(dbTalent.getTenantId());
            update.setCreatedBy(dbTalent.getCreatedBy());
            update.setCreatedDate(dbTalent.getCreatedDate());
            update.setLastModifiedDate(Instant.now());

            stopWatch.stop();
            stopWatch.start("[9] initResumeData");
            //初始resume信息
            initResumeData(update.getResumes());
            stopWatch.stop();
            stopWatch.start("[10] resumeBindByOther");
            Long resumeExistId = resumeBindByOther(update.getResumes(), id);
            if(resumeExistId != null) {
                SuspectedDuplications resumeDuplicate = getResumeDuplicate(resumeExistId);
                throw new WithDataException("Duplicate entry talent data.", cn.hutool.http.Status.HTTP_PRECON_FAILED, List.of(resumeDuplicate));
            }
            if(!checkDeleteSubmitToJobResume(id, update.getResumes())) {
                throw new CustomParameterizedException("Resumes that have been submitted to the job cannot be deleted.");
            }
            if(!checkOriginTalentResumeRelationIds(update.getResumes())) {
                throw new CustomParameterizedException("OriginTalentResumeRelationIds error.");
            }
            talentV3 = TalentV3.fromTalentDTO(update);
            //继承历史lastSyncTime属性，避免更新后丢失
            talentV3.setLastSyncTime(dbTalent.getLastSyncTime());
            talentV3.setLastEditedTime(Instant.now());
            stopWatch.stop();
            stopWatch.start("[11] mergeDBTalentRelation2Update");
            //合并talent relation
            mergeDBTalentRelation2Update(talentV3, dbTalent);
            talentV3.setOwnedByTenants(dbTalent.getOwnedByTenants());
            TalentAdditionalInfo talentAdditionalInfo = talentV3.getTalentAdditionalInfo();
            if(talentAdditionalInfo != null) {
                talentAdditionalInfo.setId(dbTalent.getAdditionalInfoId());
            }

            stopWatch.stop();
            stopWatch.start("[12] updateClientContact");
            //如果是客户联系人则先调用crm更新客户联系人数据
            talentCrmSyncService.updateClientContact(id, input);

            stopWatch.stop();
            stopWatch.start("[13] getTimeSheetUser and compareTalentEmails");
            //if talent has timeSheet account , compare emails and update timeSheet account info
            TimeSheetUserDTO timeSheetUser = jobdivaService.getTimeSheetUser(update.getId()).getBody();
            if (ObjectUtil.isNotNull(timeSheetUser) && timeSheetUser.isActivated()) {
                compareTalentEmails(update, timeSheetUser);
            }

            stopWatch.stop();
            stopWatch.start("[14] setTalentRelateEntity");
            //update talent contacts, resumes, locations and owneships
            boolean isOwnedByTenants = setTalentRelateEntity(update, talentV3.getId());
            if (isOwnedByTenants) {
                talentV3.setOwnedByTenants(talentV3.getTenantId());
            }
            stopWatch.stop();
            stopWatch.start("[14] saveTalent");
            talentV3.setIsNeedSyncHr(dbTalent.getIsNeedSyncHr());
            talentV3 = talentRepository.saveAndFlush(talentV3);
            updateEnrichNote(talentV3.getId(), enrichedNotes);

            stopWatch.stop();
            stopWatch.start("[15] mqParam");
            //构建发送mq的数据
            com.alibaba.fastjson.JSONObject mqParam = new com.alibaba.fastjson.JSONObject();
            String token = SecurityUtils.getCurrentUserToken();
            mqParam.put(AUTHORIZATION_HEADER, String.format("%s %s", TOKEN_TYPE, token));

            mqParam.put("talentId",talentV3.getId());
            mqParam.put("fullName",talentV3.getFullName());
            mqParam.put("mqRecordType", MqTranRecordBusTypeEnums.UPDATE_TALENT_FINANCE.toDbValue());
            // 操作人的的 uid，方便消费者记录 createBy、modifyBy
            mqParam.put(SecurityUtils.OPERATOR_UID, SecurityUtils.getUserUid());

            //保存记录
            CommonMqTransactionRecord record = saveCommonMqRecord(BigInteger.valueOf(talentV3.getId()),mqParam.toJSONString(),MqTranRecordBusTypeEnums.UPDATE_TALENT_FINANCE.toDbValue());
            mqParam.put("mqRecordId",record.getId());
            //financeService.updateStartLastEditedTimeByTalentId(talentV3.getId());
            executor.execute(()->{
                updateTalentRabbitTemplate.convertAndSend(talentTxMQProperties.getUpdateTalentTxExchange(), talentTxMQProperties.getUpdateTalentTxQRoutingKey(), mqParam, new CorrelationData(String.valueOf(record.getId())));
                log.info("[APN: Talent @{}] update Talent finance, send to talent tx rabbit -> param: {}", SecurityUtils.getUserId(), mqParam.toJSONString());
            });
            if (update.getCompanyLocationId() != null) {
                companyService.updateContactLocationIdByTalentId(talentV3.getId(), update.getCompanyLocationId());
                com.alibaba.fastjson.JSONObject comMqParam = new com.alibaba.fastjson.JSONObject();
                comMqParam.put(AUTHORIZATION_HEADER, String.format("%s %s", TOKEN_TYPE, token));
                comMqParam.put("mqRecordType",MqTranRecordBusTypeEnums.UPDATE_TALENT_COMPANY.toDbValue());
                comMqParam.put("talentId",talentV3.getId());
                comMqParam.put("companyLocationId",update.getCompanyLocationId());
                CommonMqTransactionRecord companyRecord = saveCommonMqRecord(BigInteger.valueOf(talentV3.getId()),comMqParam.toJSONString(),MqTranRecordBusTypeEnums.UPDATE_TALENT_COMPANY.toDbValue());
                comMqParam.put("mqRecordId",companyRecord.getId());
                comMqParam.put(SecurityUtils.OPERATOR_UID, SecurityUtils.getUserUid());
                executor.execute(()->{
                    updateTalentRabbitTemplate.convertAndSend(talentTxMQProperties.getUpdateTalentTxExchange(), talentTxMQProperties.getUpdateTalentTxRoutingCompanyKey(), comMqParam, new CorrelationData(String.valueOf(companyRecord.getId())));
                    log.info("[APN: Talent @{}] update Talent company, send to talent tx rabbit -> param: {}", SecurityUtils.getUserId(), comMqParam.toJSONString());
                });
            }
            entityManager.flush();
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), StrUtil.format("Talent id:{} update failure, please manual processing， error: {}", id, JSONUtil.toJsonStr(e)));
            log.error("[APN: Talent @{}] update Talent error, talent id : {}， error: {}", SecurityUtils.getUserId(), id, JSONUtil.toJsonStr(e));
            throw e;
        }
        stopWatch.stop();
        stopWatch.start("[16] tryDeclassifyTalentByTalentUpdate");
        boolean autoDeclassify = talentConfidentialService.tryDeclassifyTalentByTalentUpdate(talentV3);
        if (autoDeclassify) {
            String i18nMsg = commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(
                    TalentAPIMultilingualEnum.TALENT_AUTO_DECLASSIFY.getKey(),
                    CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),
                    talentApiPromptProperties.getTalentService());
            throw new CustomParameterizedException(HttpStatus.MULTI_STATUS.value(), "Talent auto declassify", i18nMsg, Map.of("id", id));
        }
        stopWatch.stop();
        stopWatch.start("[17] findTalentById");
        TalentDTOV3 result=  findTalentById(id, false);
        stopWatch.stop();
        log.info("[apn @{}] update time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return result;
    }

    private void updateEnrichNote(Long id, Map<Long, String> noteEnriched) {
        if(noteEnriched == null) {
            return;
        }

        List<TalentNote> notEnirchNotes = talentNoteRepository.findAllByTalentIdAndParsedResultIsNotNullAndEnrichResultIsNull(id);
        for (Long noteId : noteEnriched.keySet()) {
            notEnirchNotes.removeIf(c -> c.getId().equals(noteId));
            talentNoteRepository.updateOnlyEnrichNote(noteId, noteEnriched.get(noteId));
        }
        notEnirchNotes.forEach(c -> c.setEnrichResult("{}"));
        talentNoteRepository.saveAll(notEnirchNotes);
    }

    private Map<String, Object> mergeByEnrichNote(String requestBody) {
        JSONObject jsonObject = JSONUtil.parseObj(requestBody);
        JSONObject enrichNoteData = jsonObject.getJSONObject("enrichNoteData");
        jsonObject.remove("enrichNoteData");

        Map<String, Object> result = new HashMap<>();

        if(enrichNoteData == null) {
            result.put("mergedJson", jsonObject.toString());
            result.put("noteEnriched", null);
            return result;
        } else if(enrichNoteData.isEmpty()) {
            result.put("mergedJson", jsonObject.toString());
            result.put("noteEnriched", new HashMap<>());
            return result;
        }


        // 分离noteId数据
        Map<Long, String> splitNotes = splitMergedJson(enrichNoteData.toString());

        // 清理noteId结构
        String toBeMerged = splitNoteIdJson(enrichNoteData.toString());

        // 合并数据
        String mergedJson = mergeJsonData(jsonObject.toString(), toBeMerged);

        result.put("mergedJson", mergedJson);
        result.put("noteEnriched", splitNotes);

        return result;
    }

    public String mergeJsonData(String firstJson, String secondJson) {
        try {
            JSONObject first = JSONUtil.parseObj(firstJson);
            JSONObject second = JSONUtil.parseObj(secondJson);

            JSONObject result = new JSONObject(first); // 以第一个为基础

            // 遍历第二个JSON的所有字段
            for (String key : second.keySet()) {
                Object secondValue = second.get(key);

                if (first.containsKey(key)) {
                    Object firstValue = first.get(key);

                    if (firstValue instanceof cn.hutool.json.JSONArray && secondValue instanceof cn.hutool.json.JSONArray) {
                        // 如果都是数组，按照ID规则合并
                        cn.hutool.json.JSONArray mergedArray = mergeArraysByIdRule(
                                (cn.hutool.json.JSONArray) firstValue,
                                (cn.hutool.json.JSONArray) secondValue
                        );
                        result.put(key, mergedArray);
                    } else {
                        // 如果是对象或其他类型，第二个覆盖第一个
                        result.put(key, secondValue);
                    }
                } else {
                    // 如果第一个JSON中没有这个key，直接添加
                    result.put(key, secondValue);
                }
            }

            return result.toString();
        } catch (Exception e) {
            throw new RuntimeException("JSON合并失败", e);
        }
    }

    /**
     * 按ID规则合并数组
     * 如果数组元素有id字段且相同，则覆盖；否则添加到数组中
     */
    private cn.hutool.json.JSONArray mergeArraysByIdRule(cn.hutool.json.JSONArray firstArray, cn.hutool.json.JSONArray secondArray) {
        cn.hutool.json.JSONArray result = new cn.hutool.json.JSONArray();

        // 先把第一个数组的所有元素加入结果
        for (int i = 0; i < firstArray.size(); i++) {
            result.add(firstArray.get(i));
        }

        // 遍历第二个数组的元素
        for (int i = 0; i < secondArray.size(); i++) {
            Object secondItem = secondArray.get(i);
            boolean foundAndReplaced = false;

            if (secondItem instanceof JSONObject) {
                JSONObject secondObj = (JSONObject) secondItem;
                Object secondId = secondObj.get("id");

                if (secondId != null) {
                    // 查找第一个数组中是否有相同ID的元素
                    for (int j = 0; j < result.size(); j++) {
                        Object resultItem = result.get(j);
                        if (resultItem instanceof JSONObject) {
                            JSONObject resultObj = (JSONObject) resultItem;
                            Object resultId = resultObj.get("id");

                            if (secondId.equals(resultId)) {
                                // ID相同，覆盖
                                result.set(j, secondItem);
                                foundAndReplaced = true;
                                break;
                            }
                        }
                    }
                }
            }

            // 如果没有找到相同ID的元素，添加到数组末尾
            if (!foundAndReplaced) {
                result.add(secondItem);
            }
        }

        return result;
    }

    public String splitNoteIdJson(String mergedJsonString) {
        try {
            JSONObject mergedJson = JSONUtil.parseObj(mergedJsonString);
            JSONObject result = new JSONObject();

            // 遍历所有字段
            for (String fieldName : mergedJson.keySet()) {
                Object fieldValue = mergedJson.get(fieldName);
                Object cleanedValue = cleanNoteIdStructure(fieldValue);
                result.put(fieldName, cleanedValue);
            }

            return result.toString();
        } catch (Exception e) {
            throw new RuntimeException("清理noteId结构失败", e);
        }
    }

    /**
     * 递归清理noteId结构，提取value值
     */
    private Object cleanNoteIdStructure(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) obj;

            // 检查是否是noteId结构 {"noteId": x, "value": y}
            if (jsonObj.containsKey("noteId") && jsonObj.containsKey("value")) {
                // 递归处理value部分，因为value可能也包含嵌套的noteId结构
                return cleanNoteIdStructure(jsonObj.get("value"));
            } else {
                // 不是noteId结构，递归处理所有字段
                JSONObject cleanedObj = new JSONObject();
                for (String key : jsonObj.keySet()) {
                    Object value = jsonObj.get(key);
                    cleanedObj.put(key, cleanNoteIdStructure(value));
                }
                return cleanedObj;
            }
        } else if (obj instanceof cn.hutool.json.JSONArray) {
            cn.hutool.json.JSONArray jsonArray = (cn.hutool.json.JSONArray) obj;
            cn.hutool.json.JSONArray cleanedArray = new cn.hutool.json.JSONArray();

            for (int i = 0; i < jsonArray.size(); i++) {
                Object item = jsonArray.get(i);
                cleanedArray.add(cleanNoteIdStructure(item));
            }

            return cleanedArray;
        } else {
            // 基本类型直接返回
            return obj;
        }
    }

    public Map<Long, String> splitMergedJson(String mergedJsonString) {
        Map<Long, String> resultMap = new HashMap<>();

        try {
            JSONObject mergedJson = JSONUtil.parseObj(mergedJsonString);

            // 用于按noteId分组数据
            Map<Long, JSONObject> noteDataMap = new HashMap<>();

            // 遍历合并JSON的所有字段
            for (String fieldName : mergedJson.keySet()) {
                Object fieldValue = mergedJson.get(fieldName);

                if (fieldValue instanceof JSONObject) {
                    // 处理简单对象字段（如currentLocation）
                    JSONObject fieldObj = (JSONObject) fieldValue;
                    Long noteId = fieldObj.getLong("noteId");
                    Object actualValue = fieldObj.get("value");

                    if (noteId != null && actualValue != null) {
                        JSONObject noteData = noteDataMap.computeIfAbsent(noteId, k -> new JSONObject());
                        noteData.put(fieldName, actualValue);
                    }
                } else if (fieldValue instanceof cn.hutool.json.JSONArray) {
                    cn.hutool.json.JSONArray fieldArray = (cn.hutool.json.JSONArray) fieldValue;

                    // 检查数组中第一个元素的结构
                    if (fieldArray.size() > 0) {
                        Object firstItem = fieldArray.get(0);

                        if (firstItem instanceof JSONObject) {
                            JSONObject firstItemObj = (JSONObject) firstItem;

                            // 检查是否是简单结构（直接包含noteId和value）
                            if (firstItemObj.containsKey("noteId") && firstItemObj.containsKey("value")) {
                                // 处理简单数组（如skills）
                                handleSimpleArray(fieldName, fieldArray, noteDataMap);
                            } else {
                                // 处理复杂数组（如projects）
                                handleComplexArray(fieldName, fieldArray, noteDataMap);
                            }
                        }
                    }
                }
            }

            // 将每个noteId的数据转换为JSON字符串
            for (Map.Entry<Long, JSONObject> entry : noteDataMap.entrySet()) {
                Long noteId = entry.getKey();
                JSONObject noteData = entry.getValue();
                resultMap.put(noteId, noteData.toString());
            }

        } catch (Exception e) {
            throw new RuntimeException("解析合并JSON失败", e);
        }

        return resultMap;
    }

    // 处理简单数组结构（如skills）
    private void handleSimpleArray(String fieldName, cn.hutool.json.JSONArray fieldArray, Map<Long, JSONObject> noteDataMap) {
        Map<Long, cn.hutool.json.JSONArray> arrayByNoteId = new HashMap<>();

        for (int i = 0; i < fieldArray.size(); i++) {
            Object arrayItem = fieldArray.get(i);
            if (arrayItem instanceof JSONObject) {
                JSONObject itemObj = (JSONObject) arrayItem;
                Long noteId = itemObj.getLong("noteId");
                Object actualValue = itemObj.get("value");

                if (noteId != null && actualValue != null) {
                    cn.hutool.json.JSONArray noteArray = arrayByNoteId.computeIfAbsent(noteId, k -> new cn.hutool.json.JSONArray());
                    noteArray.add(actualValue);
                }
            }
        }

        // 将分组后的数组添加到对应noteId的数据中
        for (Map.Entry<Long, cn.hutool.json.JSONArray> entry : arrayByNoteId.entrySet()) {
            Long noteId = entry.getKey();
            cn.hutool.json.JSONArray noteArray = entry.getValue();

            JSONObject noteData = noteDataMap.computeIfAbsent(noteId, k -> new JSONObject());
            noteData.put(fieldName, noteArray);
        }
    }

    // 处理复杂数组结构（如projects）
    private void handleComplexArray(String fieldName, cn.hutool.json.JSONArray fieldArray, Map<Long, JSONObject> noteDataMap) {
        // 按noteId分组复杂对象
        Map<Long, cn.hutool.json.JSONArray> complexArrayByNoteId = new HashMap<>();

        for (int i = 0; i < fieldArray.size(); i++) {
            Object arrayItem = fieldArray.get(i);
            if (arrayItem instanceof JSONObject) {
                JSONObject complexItem = (JSONObject) arrayItem;

                // 收集这个复杂对象中所有的noteId
                Set<Long> noteIds = new HashSet<>();
                collectNoteIds(complexItem, noteIds);

                // 为每个noteId创建相应的对象
                for (Long noteId : noteIds) {
                    JSONObject extractedItem = extractByNoteId(complexItem, noteId);
                    if (!extractedItem.isEmpty()) {
                        cn.hutool.json.JSONArray noteArray = complexArrayByNoteId.computeIfAbsent(noteId, k -> new cn.hutool.json.JSONArray());
                        noteArray.add(extractedItem);
                    }
                }
            }
        }

        // 将分组后的复杂数组添加到对应noteId的数据中
        for (Map.Entry<Long, cn.hutool.json.JSONArray> entry : complexArrayByNoteId.entrySet()) {
            Long noteId = entry.getKey();
            cn.hutool.json.JSONArray noteArray = entry.getValue();

            JSONObject noteData = noteDataMap.computeIfAbsent(noteId, k -> new JSONObject());
            noteData.put(fieldName, noteArray);
        }
    }

    // 递归收集所有的noteId
    private void collectNoteIds(JSONObject obj, Set<Long> noteIds) {
        for (String key : obj.keySet()) {
            Object value = obj.get(key);
            if (value instanceof JSONObject) {
                JSONObject valueObj = (JSONObject) value;
                if (valueObj.containsKey("noteId")) {
                    Long noteId = valueObj.getLong("noteId");
                    if (noteId != null) {
                        noteIds.add(noteId);
                    }
                } else {
                    collectNoteIds(valueObj, noteIds);
                }
            }
        }
    }

    // 提取指定noteId的字段
    private JSONObject extractByNoteId(JSONObject complexItem, Long targetNoteId) {
        JSONObject result = new JSONObject();

        for (String key : complexItem.keySet()) {
            Object value = complexItem.get(key);
            if (value instanceof JSONObject) {
                JSONObject valueObj = (JSONObject) value;
                if (valueObj.containsKey("noteId") && valueObj.containsKey("value")) {
                    Long noteId = valueObj.getLong("noteId");
                    if (targetNoteId.equals(noteId)) {
                        result.put(key, valueObj.get("value"));
                    }
                }
            }
        }

        return result;
    }

    private TalentInfoInput mergeByRedisOrDbData(TalentV3 dbTalent, String requestBody) {
        JSONObject jsonObject = JSONUtil.parseObj(requestBody);
        String parseResumeUuid = jsonObject.getStr("parseResumeUuid");

        //禁猎客户功能：如果companyName被改变，移除公司id（businessInfoCompanyId，bdCompanyId，recogLeadsCompanyId，recogCRMAccountId，recogCompanyId）
        String dbExtendInfo = dbTalent.getTalentExtendedInfo();
        // 转换为JSONObject实现可修改性
        JSONObject requestJson = JSONUtil.parseObj(requestBody);
        JSONObject dbJson = JSONUtil.parseObj(dbExtendInfo);
        // 执行修改（直接操作JSONObject）
        removeCompanyIdsWhenCompanyNameChange(requestJson, dbJson);
        // 重新序列化为字符串（覆盖原参数）
        requestBody = JSONUtil.toJsonStr(requestJson);
        dbExtendInfo = JSONUtil.toJsonStr(dbJson);

        if(StringUtils.isEmpty(parseResumeUuid)) {
            requestBody = TalentV3.mergeExtendedInfoV3(requestBody, dbExtendInfo);
        } else {
            ParserRedisResponse response = getResumeParseResponse(parseResumeUuid);
            if (ObjectUtil.isNotEmpty(response.getStatus()) && ParseStatus.FINISHED.equals(response.getStatus()) && ObjectUtil.isNotEmpty(response.getData())) {
                String extendedInfoRedis = response.getData();
                requestBody = TalentV3.mergeExtendedInfoV3(requestBody, extendedInfoRedis);
            }
        }
        requestBody = addIdsToArrayObjects(requestBody);
        return translate2TalentInfoInput(requestBody);
    }

    private static void removeCompanyIdsWhenCompanyNameChange(JSONObject requestExtendInfo, JSONObject preExtendInfo) {
        cn.hutool.json.JSONArray preExperiences = preExtendInfo.getJSONArray("experiences");
        cn.hutool.json.JSONArray requestExperiences = requestExtendInfo.getJSONArray("experiences");

        if (CollUtil.isEmpty(preExperiences) || CollUtil.isEmpty(requestExperiences)) {
            return;
        }

        // 构建双向ID映射
        Map<String, cn.hutool.json.JSONObject> preExpMap = buildExperienceMap(preExperiences);
        Map<String, cn.hutool.json.JSONObject> reqExpMap = buildExperienceMap(requestExperiences);

        // 处理公共ID条目
        preExpMap.keySet().stream()
                .filter(reqExpMap::containsKey)
                .forEach(id -> {
                    JSONObject preExp = preExpMap.get(id);
                    JSONObject reqExp = reqExpMap.get(id);

                    if (companyNameChanged(preExp, reqExp)) {
                        removeFieldsFromBoth(preExp, reqExp);
                    }
                });
    }

    private static Map<String, JSONObject> buildExperienceMap(cn.hutool.json.JSONArray experiences) {
        Map<String, JSONObject> map = new HashMap<>();
        for (int i = 0; i < experiences.size(); i++) {
            JSONObject exp = experiences.getJSONObject(i);
            String id = exp.getStr("id");
            if (StrUtil.isNotBlank(id)) {
                map.put(id, exp);
            }
        }
        return map;
    }

    private static boolean companyNameChanged(JSONObject preExp, JSONObject reqExp) {
        return !Objects.equals(
                preExp.getStr("companyName"),
                reqExp.getStr("companyName")
        );
    }

    private static void removeFieldsFromBoth(JSONObject preExp, JSONObject reqExp) {
        FIELDS_TO_REMOVE.forEach(field -> {
            preExp.remove(field);
            reqExp.remove(field);
        });
    }

    public void syncClientContactToCrm(Long talentId) {
        talentCrmSyncService.syncClientContactToCrm(talentId);
    }

    @Override
    public TalentDTOV3 singleSaveTalent(Long id, String requestBody) {
        StopWatch stopWatch = new StopWatch("single save talent");
        stopWatch.start("[1] findTalentById");
        TalentV3 dbTalent = talentRepository.findById(id).orElseThrow(() -> new CustomParameterizedException("The talent to update dose not exist"));
        stopWatch.stop();
        stopWatch.start("[2] formatInputData");
        TalentInfoInput input = translate2TalentInfoInput(requestBody);
        // 获取原始birthDate
        ObjectMapper mapper = ObjectMapperUtils.getInstance();
        JsonNode jsonObject = null;
        String birthDate = null;
        try {
            jsonObject = mapper.readTree(requestBody);
            // 先判断birthDate节点是否存在且不为null
            JsonNode birthDateNode = jsonObject.get("birthDate");
            if (birthDateNode != null && !birthDateNode.isNull()) {
                birthDate = birthDateNode.asText();
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        removeEnumsNull(input);
        stopWatch.stop();
        TalentDTOV3 update = talentInfoMapper.toTalentDTO(input);
        update.setId(id);
        if(CollUtil.isNotEmpty(update.getContacts())) {
            try {
                stopWatch.start("[3] startCheckDuplicationTalent");
                //候选人查重
                TalentV3 talentCheckDuplication = TalentV3.fromTalentDTO(update);
                TalentSimilarityDto talentSimilarityDto = new TalentSimilarityDto();
                List<SuspectedDuplications> talentDuplication = getSuspectededDuplicationsByContactAndSmilarity(talentCheckDuplication, update.getContacts(), talentSimilarityDto, id);
                if (CollectionUtils.isNotEmpty(talentDuplication)) {
                    throw new WithDataException("Duplicate entry talent data.bean", cn.hutool.http.Status.HTTP_PRECON_FAILED, talentDuplication);
                }
            } catch (NotFoundException ignored) {
                throw new DuplicateException("Please contact customer service to resolve this issue.");
            }
            stopWatch.stop();
            stopWatch.start("[4] checkExistApproverEmail");
            // 如果候选人存在approver邮箱，那么需要判定不允许修改此邮箱
            checkExistApproverEmail(update);
            stopWatch.stop();
        }
        stopWatch.start("[5] checkPermission");
        checkPermission(dbTalent);
        stopWatch.stop();
        stopWatch.start("[6] checkEditableDataPermission");
        this.checkEditableDataPermission(dbTalent);
        stopWatch.stop();

        // 手动控制事务
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);
        TalentV3 talentV3;
        try {
            talentV3 = TalentV3.fromTalentDTO(update);
            //更新时间重置
            dbTalent.setLastModifiedDate(Instant.now());
            dbTalent.setLastEditedTime(Instant.now());
            stopWatch.start("[7] mergeDBTalentRelation2SingleUpdate");
            //合并talent relation
            mergeDBTalentRelation2SingleUpdate(talentV3, dbTalent);
            //更新talent name
            if(ObjectUtil.isNotNull(talentV3.getFirstName()) || ObjectUtil.isNotNull(talentV3.getLastName()) || ObjectUtil.isNotNull(talentV3.getFullName())) {
                //如果是fullName格式更新, 清空firstName, lastName
                if(ObjectUtil.isNotNull(talentV3.getFullName())) {
                    dbTalent.setFullName(talentV3.getFullName());
                    dbTalent.setFirstName(null);
                    dbTalent.setLastName(null);
                }else {
                    //如果是fullName格式更新, 清空firstName, lastName
                    dbTalent.setFirstName(talentV3.getFirstName());
                    dbTalent.setLastName(talentV3.getLastName());
                    dbTalent.setFullName(null);
                }
            }
            //更新gender, birthDate
            if (update.getGender() != null || birthDate != null) {
                TalentAdditionalInfo talentAdditionalInfo = dbTalent.getTalentAdditionalInfo();
                JSONObject additionalInfoJson = JSONUtil.parseObj(talentAdditionalInfo.getExtendedInfo());
                if(update.getGender() != null) {
                    additionalInfoJson.put(KEY_GENDER, update.getGender());
                }
                // 处理birthDate：为null不处理；为空白字符串则移除；否则更新
                if (birthDate != null) {
                    if (StringUtils.isBlank(birthDate)) {
                        // 空白字符串（包括空串、纯空格）则移除key
                        additionalInfoJson.remove(KEY_BIRTHDATE);
                    } else {
                        // 有效日期字符串则更新或添加key
                        additionalInfoJson.put(KEY_BIRTHDATE, birthDate);
                    }
                }
                talentAdditionalInfo.setExtendedInfo(additionalInfoJson.toString());
                dbTalent.setTalentAdditionalInfo(talentAdditionalInfo);
            }
            stopWatch.stop();
            stopWatch.start("[8] updateClientContact");
            //如果是客户联系人则先调用crm更新客户联系人数据
            if ((ObjectUtil.isNotNull(update.getFirstName()) && ObjectUtil.isNotNull(update.getLastName()))
                    || ObjectUtil.isNotNull(update.getFullName())
                    || CollUtil.isNotEmpty(update.getContacts())
                    || ObjectUtil.isNotNull(update.getCurrentLocation())) {
                talentCrmSyncService.updateClientContact(id, input);
            }
            stopWatch.stop();
            stopWatch.start("[9] getTimeSheetUser and compareTalentEmails");
            //if talent has timeSheet account , compare emails and update timeSheet account info
            TimeSheetUserDTO timeSheetUser = jobdivaService.getTimeSheetUser(id).getBody();
            if (ObjectUtil.isNotNull(timeSheetUser) && timeSheetUser.isActivated() && CollUtil.isNotEmpty(update.getContacts())) {
                compareTalentEmails(update, timeSheetUser);
            }
            stopWatch.stop();
            stopWatch.start("[10] setTalentRelateEntity");
            //update talent contacts, locations
            singleSaveTalentRelateEntity(update, id);
            stopWatch.stop();
            stopWatch.start("[11] saveTalent");
            dbTalent = talentRepository.saveAndFlush(dbTalent);
            stopWatch.stop();

            entityManager.flush();
            transactionManager.commit(status);
        }catch (Exception e) {
            transactionManager.rollback(status);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), StrUtil.format("Talent id:{} update failure, please manual processing， error: {}", id, JSONUtil.toJsonStr(e)));
            log.error("[APN: Talent @{}] update Talent error, talent id : {}， error: {}", SecurityUtils.getUserId(), id, JSONUtil.toJsonStr(e));
            throw e;
        }
        stopWatch.start("[12] tryDeclassifyTalentByTalentUpdate");
        boolean autoDeclassify = talentConfidentialService.tryDeclassifyTalentByTalentUpdate(dbTalent);
        if (autoDeclassify) {
            String i18nMsg = commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(
                    TalentAPIMultilingualEnum.TALENT_AUTO_DECLASSIFY.getKey(),
                    CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),
                    talentApiPromptProperties.getTalentService());
            throw new CustomParameterizedException(HttpStatus.MULTI_STATUS.value(), "Talent auto declassify", i18nMsg, Map.of("id", id));
        }
        stopWatch.stop();
        stopWatch.start("[13] findTalentById");
        TalentDTOV3 result=  findTalentById(id, false);
        stopWatch.stop();
        log.info("[apn @{}] single update talent time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return result;
    }

    private boolean isClientContact(Long id) {
        var contactStatusList = companyService.getTalentClientContactStatus(List.of(id)).getBody();
        Map<Long, Boolean> talentClientContactStatusMap =  contactStatusList
                .stream()
                .collect(Collectors.toMap(
                        TalentClientContactStatusDTO::getTalentId,
                        TalentClientContactStatusDTO::getIsClientContact
                ));
        return Boolean.TRUE.equals(talentClientContactStatusMap.get(id));
    }

    private boolean checkOriginTalentResumeRelationIds(List<TalentResumeDTO> resumes) {
        if(resumes == null) {
            return true;
        }
        List<Long> repeatCheck = new ArrayList<>();
        for(TalentResumeDTO resumeDTO : resumes) {
            List<Long> originTalentResumeRelationIds = resumeDTO.getOriginTalentResumeRelationIds();
            if(originTalentResumeRelationIds == null) {
                continue;
            }
            if(!CollUtil.intersection(repeatCheck, originTalentResumeRelationIds).isEmpty()) {
                return false;
            }
            repeatCheck.addAll(originTalentResumeRelationIds);
        }
        return true;
    }

    @Resource
    private TalentApplicationProcessSubmitToJobRepository submitToJobRepository;

    private boolean checkDeleteSubmitToJobResume(Long id, List<TalentResumeDTO> resumes) {
        List<TalentApplicationProcessSubmitToJob> submitToJobs = submitToJobRepository.findAllByTalent(id);
        List<Long> oldRelationId = submitToJobs.stream().map(TalentApplicationProcessSubmitToJob::getTalentResumeRelationId).toList();
        if(resumes == null) {
            resumes = new ArrayList<>();
        }
        List<TalentResumeRelation> talentResumeRelationList = talentResumeRelationRepository.findAllById(oldRelationId);
        //历史数据已经删除了绑定的简历了就不检查了
        if(checkHistoryDeleteResume(submitToJobs, talentResumeRelationList.stream().filter(p -> CommonDataStatus.AVAILABLE == p.getStatus()).collect(Collectors.toList()))) {
            return true;
        }
        List<Long> oldResumeIds = talentResumeRelationList.stream().map(TalentResumeRelation::getResumeId).collect(Collectors.toList());
        List<Long> newResumeIds = resumes.stream().map(TalentResumeDTO::getResumeId).toList();
        oldResumeIds.removeAll(newResumeIds);
        List<Long> replaceOldRelationIds = new ArrayList<>();
        for(TalentResumeDTO resume : resumes) {
            List<Long> originTalentResumeRelationIds = resume.getOriginTalentResumeRelationIds();
            if(originTalentResumeRelationIds != null) {
                replaceOldRelationIds.addAll(originTalentResumeRelationIds);
            }
        }
        List<Long> replaceOldResumeIds = talentResumeRelationRepository.findAllById(replaceOldRelationIds).stream().map(TalentResumeRelation::getResumeId).collect(Collectors.toList());
        oldResumeIds.removeAll(replaceOldResumeIds);
        return oldResumeIds.isEmpty();
    }

    private boolean checkHistoryDeleteResume(List<TalentApplicationProcessSubmitToJob> submitToJobs, List<TalentResumeRelation> talentResumeRelationList) {
        if(submitToJobs.isEmpty()) {
            return false;
        }
        for(TalentApplicationProcessSubmitToJob submitToJob : submitToJobs) {
            Optional<TalentResumeRelation> findOpt = talentResumeRelationList.stream().filter(p -> p.getId().equals(submitToJob.getTalentResumeRelationId())).findAny();
            if(findOpt.isEmpty()) {
                return true;
            }
        }

        return false;
    }

    private Long resumeBindByOther(List<TalentResumeDTO> resumeDTOS, Long excludeTalentId) {
        if(resumeDTOS == null) {
            return null;
        }
        List<Long> resumeId = resumeDTOS.stream().map(TalentResumeDTO::getResumeId).collect(Collectors.toList());
        List<TalentResumeRelation> talentResumeRelationList = talentResumeRelationRepository.findAllByResumeIdInAndStatusIsAndTenantIdIs(resumeId, CommonDataStatus.AVAILABLE, SecurityUtils.getTenantId());
        for(TalentResumeRelation relation : talentResumeRelationList) {
            if(!relation.getTalentId().equals(excludeTalentId)) {
                return relation.getTalentId();
            }
        }

        return null;
    }

    private void checkExistApproverEmail(TalentDTOV3 update) {
        Set<String> emailSet = update.getContacts().stream().filter(t -> t.getType().equals(ContactType.EMAIL)).map(TalentContactDTO::getContact).collect(Collectors.toSet());
        // 检查更新部分contactList
        Set<String> contactList = talentContactRepository.findAllByTalentIdAndStatusAndTypeIn(update.getId(), TalentContactStatus.AVAILABLE, Collections.singletonList(ContactType.EMAIL)).stream().map(TalentContact::getContact).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(contactList)){
            contactList.removeAll(emailSet);
        }
        List<TimeSheetUserDTO> timeSheetUserList = jobdivaService.findByUsernameOrEmailList(contactList).getBody();
        if (CollUtil.isNotEmpty(timeSheetUserList)){
            throw new CustomParameterizedException("Cannot modify approver's email.");
        }
    }

    public <T extends EnumRelation> Set<T> mergeEnumRelations(Set<T> dbRelations, Set<T> updRelations) {
        Map<Integer, T> relationMap = dbRelations.stream()
                .collect(Collectors.toMap(
                        EnumRelation::getUniqueEnumId,  // The key extractor.
                        Function.identity(),  // The value mapper.
                        (obj1, obj2) -> obj1  // The merge function (in case of key collision, keep the first one).
                ));
        if(CollectionUtil.isEmpty(updRelations)) {
            dbRelations.clear();
            return dbRelations;
        } else {
            return  updRelations.stream().map(c -> {
                if (relationMap.containsKey(c.getUniqueEnumId())) {
                    return relationMap.get(c.getUniqueEnumId());
                } else {
                    return c;
                }
            }).collect(Collectors.toSet());
        }
    }

    private void mergeDBTalentRelation2Update(TalentV3 talentV3, TalentV3 dbTalent) {
        //合并各级联更新表的id 避免未修改时 jpa产生先删后增的操作
        Set<TalentJobFunctionRelation> jobFunctions = dbTalent.getJobFunctions();
        Set<TalentJobFunctionRelation> updJobFunctions = talentV3.getJobFunctions();
        if(jobFunctions != null) {
            Set<TalentJobFunctionRelation> relations = mergeEnumRelations(jobFunctions, updJobFunctions);
            relations.forEach(c -> c.setTalentId(dbTalent.getId()));
            talentV3.setJobFunctions(relations);
        }

        Set<TalentLanguageRelation> languages = dbTalent.getLanguages();
        Set<TalentLanguageRelation> updLanguages = talentV3.getLanguages();
        if(languages != null) {
            Set<TalentLanguageRelation> relations = mergeEnumRelations(languages, updLanguages);
            relations.forEach(c -> c.setTalentId(dbTalent.getId()));
            talentV3.setLanguages(relations);
        }

        Set<TalentIndustryRelation> industries = dbTalent.getIndustries();
        Set<TalentIndustryRelation> updIndustries = talentV3.getIndustries();
        if(industries != null) {
            Set<TalentIndustryRelation> relations = mergeEnumRelations(industries, updIndustries);
            relations.forEach(c -> c.setTalentId(dbTalent.getId()));
            talentV3.setIndustries(relations);
        }

        Set<TalentWorkAuthorizationRelation> workAuthorization = dbTalent.getWorkAuthorization();
        Set<TalentWorkAuthorizationRelation> updWorkAuthorization = talentV3.getWorkAuthorization();
        if(workAuthorization != null) {
            Set<TalentWorkAuthorizationRelation> relations = mergeEnumRelations(workAuthorization, updWorkAuthorization);
            relations.forEach(c -> c.setTalentId(dbTalent.getId()));
            talentV3.setWorkAuthorization(relations);
        }
    }

    private void mergeDBTalentRelation2SingleUpdate(TalentV3 talentV3, TalentV3 dbTalent) {
        Long talentId = dbTalent.getId();

        // 合并 jobFunctions（null跳过，空集合清空，有值合并）
        if (talentV3.getJobFunctions() != null) {
            // 为传入的所有元素设置talentId
            talentV3.getJobFunctions().forEach(relation -> relation.setTalentId(talentId));

            if (talentV3.getJobFunctions().isEmpty()) {
                dbTalent.getJobFunctions().clear(); // 清空原集合
            } else {
                mergeRelations(dbTalent.getJobFunctions(), talentV3.getJobFunctions(), talentId);
            }
        }

        // 合并 languages
        if (talentV3.getLanguages() != null) {
            // 为传入的所有元素设置talentId
            talentV3.getLanguages().forEach(relation -> relation.setTalentId(talentId));

            if (talentV3.getLanguages().isEmpty()) {
                dbTalent.getLanguages().clear();
            } else {
                mergeRelations(dbTalent.getLanguages(), talentV3.getLanguages(), talentId);
            }
        }

        // 合并 industries
        if (talentV3.getIndustries() != null) {
            // 为传入的所有元素设置talentId
            talentV3.getIndustries().forEach(relation -> relation.setTalentId(talentId));

            if (talentV3.getIndustries().isEmpty()) {
                dbTalent.getIndustries().clear();
            } else {
                mergeRelations(dbTalent.getIndustries(), talentV3.getIndustries(), talentId);
            }
        }

        // 合并 workAuthorization
        if (talentV3.getWorkAuthorization() != null) {
            // 为传入的所有元素设置talentId
            talentV3.getWorkAuthorization().forEach(relation -> relation.setTalentId(talentId));

            if (talentV3.getWorkAuthorization().isEmpty()) {
                dbTalent.getWorkAuthorization().clear();
            } else {
                mergeRelations(dbTalent.getWorkAuthorization(), talentV3.getWorkAuthorization(), talentId);
            }
        }
    }

    /**
     * 安全合并两个关联集合，保持原集合引用不变
     */
    private <T extends EnumRelation> void mergeRelations(
            Set<T> originalSet,
            Set<T> incomingSet,
            Long parentTalentId
    ) {
        // 1. 移除已删除的关联（不在 incomingSet 中的元素）
        Iterator<T> iterator = originalSet.iterator();
        while (iterator.hasNext()) {
            T originalItem = iterator.next();
            boolean existsInIncoming = incomingSet.stream()
                    .anyMatch(incoming -> incoming.getUniqueEnumId().equals(originalItem.getUniqueEnumId()));

            if (!existsInIncoming) {
                iterator.remove(); // 触发 orphanRemoval 删除
            }
        }

        // 2. 添加或更新关联
        for (T incomingItem : incomingSet) {
            // 查找原集合中是否存在相同 ID 的关联
            Optional<T> existingOptional = originalSet.stream()
                    .filter(item -> item.getUniqueEnumId().equals(incomingItem.getUniqueEnumId()))
                    .findFirst();

            if (!existingOptional.isPresent()) {
                // 不存在则添加到原集合（此时talentId已在调用前设置）
                originalSet.add(incomingItem);
            } else {
                // 如需更新现有元素的其他字段，可在此处添加逻辑
                T existingItem = existingOptional.get();
                // existingItem.setXXX(incomingItem.getXXX());
            }
        }
    }



    private boolean checkOwnership(List<TalentOwnershipDTO> ownerships) {
        if(ownerships == null || ownerships.isEmpty()) {
            return true;
        }
        List<Long> share = ownerships.stream().filter(p -> p.getOwnershipType() == TalentOwnershipType.SHARE).map(TalentOwnershipDTO::getUserId).collect(Collectors.toList());
        List<Long> talentOwner = ownerships.stream().filter(p -> p.getOwnershipType() == TalentOwnershipType.TALENT_OWNER).map(TalentOwnershipDTO::getUserId).collect(Collectors.toList());
        return CollUtil.intersection(share, talentOwner).isEmpty();
    }

    /**
     * 保存事务记录
     * @param busId
     * @param mqParam
     * @param type
     * @return
     */
    private CommonMqTransactionRecord saveCommonMqRecord(BigInteger busId,String mqParam,Integer type){
        //保存记录
        CommonMqTransactionRecord record = new CommonMqTransactionRecord();
        record.setBusType(type);
        record.setBusId(busId);
        record.setSendStatus(MqTranRecordStatusEnums.PENDING.toDbValue());
        record.setSendContent(mqParam);
        record.setSendCount(1);
        talentCommonMqTransactionRecordRepository.save(record);
        log.info("[APN: Talent @{}] update Talent create mq trans record success -> result: {}", SecurityUtils.getUserId(), record);
        return record;
    }

//    private List<TalentDTOV3> checkUpdateTalent(TalentDTOV3 update) {
//        TalentContactSearchVM talentContactSearchVM = new TalentContactSearchVM();
//        talentContactSearchVM.setContacts(Convert.toList(TalentContactVM.class, update.getContacts()));
//        talentContactSearchVM.setIgnoreTalentId(update.getId());
//        return getTalentsByContacts(talentContactSearchVM);
//    }

    private void compareTalentEmails(TalentDTOV3 update, TimeSheetUserDTO timeSheetUser) {
        List<TalentContact> talentContactList = talentContactRepository.findAllByTalentIdAndTenantIdAndStatusOrderBySort(update.getId(), update.getTenantId(), TalentContactStatus.AVAILABLE);
        Optional<TalentContact> contact = talentContactList.stream().filter(s -> ContactType.EMAIL.equals(s.getType())).min(Comparator.comparing(TalentContact::getSort));
        Optional<TalentContactDTO> contactUpdate = update.getContacts().stream().filter(s -> ContactType.EMAIL.equals(s.getType())).min(Comparator.comparing(TalentContactDTO::getSort));
        if (contact.isPresent() && contactUpdate.isPresent()) {
            //if primary email changed, update timeSheet account info and send email
            if (!contact.get().getContact().equals(contactUpdate.get().getContact())) {
                //update timeSheet user
                timeSheetUser.setUsername(contactUpdate.get().getContact());
                timeSheetUser.setEmail(contactUpdate.get().getContact());
                ResponseEntity<Boolean> saveResponse = jobdivaService.saveTimeSheetUser(timeSheetUser);
                Boolean result = saveResponse.getBody() != null && saveResponse.getBody();
                if(!result) {
                    throw new DuplicateException("This email is already in use by another timesheet user.");
                }
                //send change timeSheet userName email
                sendRemindAccountEmail(contactUpdate.get().getContact(), CommonUtils.formatFullName(update.getFirstName(), update.getLastName(), update.getFullName()));
                //delete timeSheet account token
//TODO                timeSheetUserService.logoutByUid(timeSheetUser.getUid());
            }
        }
    }

    public void sendRemindAccountEmail(String email, String fullName) {
        String subject = "Your account has been updated";
        StringBuilder sb = new StringBuilder();
        sb.append("<body>");
        HtmlUtil.appendParagraphCell(sb, "Hi " + fullName + StrUtil.COMMA);
        HtmlUtil.appendBrTags(sb, 1);
        HtmlUtil.appendParagraphCell(sb, fullName + StrUtil.COMMA + " when there are changes to your Hitalent account access we want to be sure it was you who");
        HtmlUtil.appendParagraphCell(sb, "authorized those changes.");
        HtmlUtil.appendBrTags(sb, 1);
        HtmlUtil.appendParagraphCell(sb, "Our records show account manager recently updated your login account. Your new account is " + email);
        HtmlUtil.appendParagraphCell(sb, "If you have any question, please contact your account manager ");
        HtmlUtil.appendBrTags(sb, 1);
        HtmlUtil.appendParagraphCell(sb, "Thank you");
        sb.append("</body>");
        MailVM mailVm = new MailVM(applicationProperties.getSupportSender(), Collections.singletonList(email), null, null, subject, sb.toString(), null, true);
        mailService.sendHtmlMail(mailVm);
    }

    @Resource
    private TalentReviewNoteRepository reviewNoteRepository;

    @Override
    public TalentDTOV3 findTalentById(Long id, boolean checkLimit) {
        StopWatch stopWatch = new StopWatch("findTalentById");
        stopWatch.start("[1.1] findTalentById");
        TalentV3 talent = talentRepository.findById(id).orElseThrow(() -> new NotFoundException("Cannot find talent"));

        if (!SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.ADMIN) && !SecurityUtils.isCurrentTenant(talent.getTenantId())) {
            return null;
        }
        stopWatch.stop();
        stopWatch.start("[1.2] confidentialTalentViewAble");
        // 如果没有保密查看权限，只返回 id lastName 等部分信息
        if (!talentConfidentialService.confidentialTalentViewAble(id)) {
            List<TalentOwnershipDTO> ownerships = toDtos(talentOwnershipRepository.findAllByTalentId(talent.getId()));
            TalentDTOV3 result =  TalentDTOV3.confidentialResult(talent, talentConfidentialService.getConfidentialInfo(id).orElse(null));
            result.setOwnerships(ownerships);
            return result;
        }
        stopWatch.stop();
        stopWatch.start("[1.3] talentAdditionalInfo");
        String talentFormConfig = null;
        // 客户端模式不用过滤additionalInfo中的额外信息
        if (!(SecurityContextHolder.getContext().getAuthentication() instanceof OAuth2ClientAuthenticationToken)) {
            ResponseEntity<String> response = userService.getTalentFormConfig();
            if (response.getStatusCode() != HttpStatus.OK) {
                throw new ExternalInterfaceException("/user/api/v3/talents/config/talent-form Interface exception", 500);
            }
            talentFormConfig = response.getBody();
            //根据form配置过滤additionalInfo中的额外信息
            TalentAdditionalInfo talentAdditionalInfo = talent.getTalentAdditionalInfo();
            if (talentAdditionalInfo != null) {
                talentAdditionalInfo.setExtendedInfo(filterByFormConfig(talentAdditionalInfo.getExtendedInfo(), talentFormConfig));
            }
        }
        stopWatch.stop();
        stopWatch.start("[1.3] resignedAndTerminated");
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<Set<Long>> resignedApplicationIdsFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return applicationService.getResignationsByTalentId(id).getBody().stream()
                    .map(TalentRecruitmentProcessResignationVO::getTalentRecruitmentProcessId)
                    .collect(Collectors.toSet());
        });

        CompletableFuture<Set<Long>> terminatedApplicationIdsFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return financeService.findTerminatedApplicationIdsByTalentId(id).getBody();
        });

        TalentDTOV3 talentDTOV3 = TalentDTOV3.fromTalent(talent, SetUtils.union(resignedApplicationIdsFuture.join(), terminatedApplicationIdsFuture.join()));
        stopWatch.stop();
        stopWatch.start("[1.4] search data task");

        Authentication authentication = SecurityUtils.getAuthentication();

        var contactSetFuture = CompletableFuture.supplyAsync(() -> {
            List<TalentContact> contactSet = talentContactRepository.findAllByTalentIdAndStatusAndTypeIn(talent.getId(), TalentContactStatus.AVAILABLE, ContactTypeConstants.COMPREHENSIVE_CONTACT_TYPES);
            List<TalentContactDTO> contactDTOList = Convert.toList(TalentContactDTO.class, contactSet.stream().sorted(Comparator
                    .comparing((TalentContact contact) ->
                            TalentContactVerificationStatus.WRONG_CONTACT.equals(contact.getVerificationStatus()),
                            Comparator.reverseOrder()) // WRONG_CONTACT 为 true 时，值为 1，排在后面
                    .thenComparing(TalentContact::getSort) // 继续按 sort 排序
            ).collect(Collectors.toList()));
            addApproverTagToContact(contactDTOList);
            return contactDTOList;
        }, executor);
        var resumesFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return talentResumeService.findAllWithoutPortraitByTalentId(talent.getId());
        }, executor);
        var noteListFuture = CompletableFuture.supplyAsync(() -> {
            List<TalentNote> noteList = talentNoteRepository.findAllByTalentId(talent.getId());
            List<TalentNoteDTO> noteListDTO = Convert.toList(TalentNoteDTO.class, noteList);
            setNoteAttachUserInfo(noteListDTO);
            return noteListDTO;
        }, executor);
        var reviewNoteListFuture = CompletableFuture.supplyAsync(() -> {
            List<TalentReviewNote> reviewNoteList = reviewNoteRepository.findAllByTalentIdIs(talent.getId());
            List<TalentReviewNoteDTO> noteListDTO = Convert.toList(TalentReviewNoteDTO.class, reviewNoteList);
            setReviewNoteAttachUserInfo(noteListDTO);
            return noteListDTO;
        }, executor);
        var ownershipsFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return toDtos(talentOwnershipRepository.findAllByTalentId(talent.getId()));
        }, executor);
        var isAMFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return checkAMPermission(talent.getId());
        }, executor);
        var currentLocationFuture = CompletableFuture.supplyAsync(() -> talentLocationRepository.findByTalentId(id), executor);
        /* companyService.queryContactLocationIdByTalentId 没有执行任务业务 返回了1
            var companyLocationIdFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return companyService.queryContactLocationIdByTalentId(talent.getId()).getBody();
        }, executor);*/
        var agencyIdFuture = CompletableFuture.supplyAsync(() -> {
            Optional<AgencyTalentRelation> agencyTalentRelation = agencyTalentRelationRepository.findByTalentId(talent.getId());
            if (agencyTalentRelation.isPresent()) {
                return agencyTalentRelation.get().getAgencyId();
            }
            return null;
        }, executor);
        var confidentialInfoFuture = CompletableFuture.supplyAsync(() -> talentConfidentialService.getConfidentialInfo(id), executor);

        CompletableFuture.allOf(contactSetFuture, resumesFuture, noteListFuture, reviewNoteListFuture, ownershipsFuture, isAMFuture, currentLocationFuture, agencyIdFuture, confidentialInfoFuture)
                .exceptionally(t -> {
                    log.error("Error occurred when fetching talent data: ", t);
                    throw new ExternalServiceInterfaceException("Error occurred when fetching talent data: ");
                }).join();
        stopWatch.stop();
        stopWatch.start("[1.4] assemble data task");

        List<TalentContactDTO> contactSet = contactSetFuture.join();
        if (checkLimit && !hasRemainByTalent(talent,ownershipsFuture.join())) {
            filterContact(contactSet);
        }
        talentDTOV3.setContacts(contactSet);
        talentDTOV3.setResumes(resumesFuture.join());
        talentDTOV3.setNotes(noteListFuture.join());
        talentDTOV3.setReviewNotes(reviewNoteListFuture.join());
        talentDTOV3.setOwnerships(ownershipsFuture.join());
        talentDTOV3.setIsAM(isAMFuture.join());
        talentDTOV3.setAgencyId(agencyIdFuture.join());
        TalentCurrentLocation currentLocation = currentLocationFuture.join();
        if (ObjectUtil.isNotEmpty(currentLocation)) {
            talentDTOV3.setCurrentLocation(JSONUtil.toBean(currentLocation.getOriginalLoc(), LocationDTO.class));
            talentDTOV3.setZipCode(currentLocation.getZipCode());
            talentDTOV3.setAddressLine(talentDTOV3.getCurrentLocation().getAddressLine());
        }
        talentDTOV3.setCompanyLocationId(1L);
        //追加返回公司联系人以及所属的公司
        List<SalesLeadClientContact> clientContacts = getTalentIsClientContract(id);
        if(CollUtil.isNotEmpty(clientContacts)) {
            Optional<SalesLeadClientContact> optContact = clientContacts.stream().filter(SalesLeadClientContact::isActive).findFirst();
            if (optContact.isPresent()) {
                talentDTOV3.setClientContactCompanyId(optContact.get().getCompanyId());
            }
            //设置客户联系人所属的公司
            talentDTOV3.setCompanyAffiliations(clientContacts.stream()
                    .map(contact -> new ClientContactDTO(contact.getCompanyId(), contact.isActive()))
                    .collect(Collectors.toList()));
        }

        talentDTOV3.setConfidentialTalentViewAble(true);
        talentDTOV3.setConfidentialInfo(confidentialInfoFuture.join().orElse(null));

        stopWatch.stop();
        stopWatch.start("[1.5] sortTalentDTO");

        TalentDTOV3 ret = sortTalentDTO(talentDTOV3);
        //TODO 整理旧数据前 currency preferredCurrency做下转换
        String currency = ret.getCurrency();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(currency) && NumberUtil.isNumber(currency)) {
            EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(Integer.valueOf(currency));
            ret.setCurrency(enumCurrency.getName());
        }
//        String preferredCurrency = ret.getPreferredCurrency();
//        if (org.apache.commons.lang3.StringUtils.isNotEmpty(preferredCurrency) && NumberUtil.isNumber(preferredCurrency)) {
//            EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(Integer.valueOf(preferredCurrency));
//            ret.setPreferredCurrency(enumCurrency.getName());
//        }
        //根据form配置过滤关系型数据
        filterRelationData(ret, talentFormConfig);
        stopWatch.stop();
        log.info("[apn @{}] findTalentById time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return ret;
    }


    @Override
    public TalentOwnershipDTO findTalentOwnerShip(Long id) {
        TalentV3 talent = talentRepository.findById(id).orElseThrow(() -> new NotFoundException("Cannot find talent"));

        if (!SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.ADMIN) && !SecurityUtils.isCurrentTenant(talent.getTenantId())) {
            return null;
        } else {
            Authentication authentication = SecurityUtils.getAuthentication();
            var ownershipsFuture = CompletableFuture.supplyAsync(() -> {
                SecurityUtils.setAuthentication(authentication);
                return toDtos(talentOwnershipRepository.findAllByTalentId(talent.getId()));
            }, executor);

            CompletableFuture.allOf(ownershipsFuture)
                    .exceptionally(t -> {
                        log.error("Error occurred when fetching talent ownership data: ", t);
                        throw new ExternalServiceInterfaceException("Error occurred when fetching talent data: ");
                    }).join();

            List<TalentOwnershipDTO> ownerships = ownershipsFuture.join().stream().filter(ownership -> ownership.getOwnershipType() != null && ownership.getOwnershipType().equals(TalentOwnershipType.TALENT_OWNER)).toList();
            TalentOwnershipDTO owner = (ownerships != null && !ownerships.isEmpty()) ? ownerships.get(0) : null;
            return owner;
        }
    }

    @Override
    public boolean hasTalentViewAuthority(Long id) {
        if(SecurityUtils.isAdmin()) {
            return true;
        }
        if(isClientContact(id)) {
            //ownership相关人员可以访问
            Long userId= SecurityUtils.getUserId();
            List<Long> viewableContactTalentIds = talentOwnershipService.getViewableTalentIdsOnUser(List.of(id), userId);
            if(viewableContactTalentIds.contains(id)) {
                return true;
            }
            //分享给所有人的都可以访问
            List<TalentOwnership> allShare = talentOwnershipRepository.findAllByTalentIdAndOwnershipType(id, TalentOwnershipType.TENANT_SHARE);
            if(!allShare.isEmpty()) {
                return true;
            }
            // 创建者可以访问
            Optional<TalentV3> optTalent = talentRepository.findById(id);
            if (optTalent.isPresent()){
                TalentV3 talentV3 = optTalent.get();
                if (userId.equals(talentV3.getPermissionUserId()) || String.valueOf(userId).equals(talentV3.getCreatedBy().split(",")[0])) {
                    return true;
                }
            }
//            if (talentRepository.existsByIdAndPermissionUserId(id, userId)){
//                return true;
//            }
            //流程相关人员可以访问
            Set<Long> participant = new HashSet<>();
            List<TalentRecruitmentProcessVO> processVOList = applicationService.getTalentRecruitmentProcessAllByTalentId(id).getBody();
            processVOList.forEach(c -> {
                List<TalentRecruitmentProcessKpiUserVO> kpiUsers = c.getKpiUsers();
                participant.addAll(kpiUsers.stream().map(TalentRecruitmentProcessKpiUserVO::getUserId).collect(Collectors.toSet()));
            });
            if(participant.contains(userId)) {
                return true;
            }
            //和创建者/owner同团队，有团队权限的人可以访问
//            TeamDataPermissionRespDTO teamDataPermission = cachePermission.getTeamDataPermissionFromCacheOnly(SecurityUtils.getUserId());
            Long tenantId = SecurityUtils.getTenantId();
            TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateClientContactDataPermissionByUserId(tenantId, userId).getBody();
            log.info("DataPermission (user: {}) = {}", userId, teamDataPermission);
            if(teamDataPermission.getAll()) {
                return true;
            }
            Set<Long> readableTeamIds = teamDataPermission.getReadableTeamIds();
            Set<Long> permissionTeamIds = getTalentPermissionTeamIds(id);
            return containsAny(readableTeamIds, permissionTeamIds);
        } else {
            return true;
        }
    }

    @Override
    public List<SuspectedDuplications> suspectedDuplicatePhonesCheck(String requestBody) {
        //获取候选人id
        Long talentId = JSONUtil.parseObj(requestBody).getLong("id");

        TalentInfoInput input = translate2TalentInfoInput(requestBody);
        removeEnumsNull(input);

        TalentDTOV3 talentDTO = talentInfoMapper.toTalentDTO(input);
        TalentV3 checkDuplicationTalent = TalentV3.fromTalentDTO(talentDTO);
        setTalentDefaultValue(talentDTO);
        List<TalentContactDTO> contacts = input.getContacts();
        //如果沒有phone类型的contact，直接返回
        if (contacts == null || contacts.stream().noneMatch(c -> c.getType().equals(ContactType.PHONE))) {
            return null;
        }
        //过滤错误的联系方式
        contacts = contacts.stream().filter(c -> !TalentContactVerificationStatus.WRONG_CONTACT.equals(c.getVerificationStatus())).collect(Collectors.toList());
        //组装esfiller查重参数
        TalentSimilarityDto talentSimilarityDto = new TalentSimilarityDto();
        talentSimilarityDto.setCurrentLocation(input.getCurrentLocation());

        return esFillerTalentService.checkTalentDuplicationWithResult(checkDuplicationTalent, contacts, talentSimilarityDto, applicationProperties.getSimilarity().toString(), talentId);
    }

    @Override
    public void duplicateContactsCheck(TalentInfoInput input) {
        log.info("Talent duplicate contacts Check");
        TalentDTOV3 update = talentInfoMapper.toTalentDTO(input);
        //判断联系方式是否为空
        if (ObjectUtil.isNull(input) || CollUtil.isEmpty(input.getContacts())) {
            return;
        }
        //候选人查重
        TalentV3 talentCheckDuplication = TalentV3.fromTalentDTO(update);
        TalentSimilarityDto talentSimilarityDto = new TalentSimilarityDto();
        List<SuspectedDuplications> talentDuplication = getSuspectededDuplicationsByContactAndSmilarity(talentCheckDuplication, update.getContacts(), talentSimilarityDto, input.getTalentId());
        if (CollectionUtils.isNotEmpty(talentDuplication)) {
            throw new WithDataException("Duplicate entry talent data.bean", cn.hutool.http.Status.HTTP_PRECON_FAILED, talentDuplication);
        }
    }


    @Override
    public List<SuspectedDuplications> apnProDuplicateCheck(TalentInfoInput input) {
        //需求变更: APN-Pro中talent查重时只根据contact精确查重, 而不通过talent的experiences,educations等信息模糊查重; 所以不需要构建talentDTO传给esfillter查重, 只需将contacts信息传给esfiller来模糊匹配区号等
//        removeEnumsNull(input);
//        TalentDTOV3 talentDTO = talentInfoMapper.toTalentDTO(input);
//        setTalentDefaultValue(talentDTO);
        log.info("startCheckDuplicationTalent");
//        TalentV3 checkDuplicationTalent = TalentV3.fromTalentDTO(talentDTO);
        List<TalentContactDTO> contacts = input.getContacts();
        TalentSimilarityDto talentSimilarityDto = new TalentSimilarityDto();
//        talentSimilarityDto.setCurrentLocation(input.getCurrentLocation()); //也不需要根据currentLocation查重
        return getSuspectededDuplicationsByContactAndSmilarityByAPNPro(null, contacts, talentSimilarityDto, null);
    }

    public List<SuspectedDuplications> getSuspectededDuplicationsByContactAndSmilarityByAPNPro(TalentV3 talentV3, List<TalentContactDTO> contacts, TalentSimilarityDto talentSimilarityDto, Long ignoreTalentId) {
        contacts = contacts.stream().filter(c -> !TalentContactVerificationStatus.WRONG_CONTACT.equals(c.getVerificationStatus())).collect(Collectors.toList());

        List<TalentContactVM> contactVmList = JSONUtil.toList(JSONUtil.parseArray(contacts), TalentContactVM.class);
        List<SuspectedDuplications> result = esFillerTalentService.checkTalentDuplicationWithResult(talentV3, contacts, talentSimilarityDto, applicationProperties.getSimilarity().toString(), ignoreTalentId);
        //疑似重复的不同分机号，不算重复候选人，但是需要提示给用户
        if (CollUtil.isNotEmpty(result)) {
            result = result.stream().filter(dto -> dto.get_similarity()
                    .compareTo(applicationProperties.getSimilarity()) >= 0 || CollUtil.isNotEmpty(dto.getSuspectedDuplicatedPhones()))
                    .collect(Collectors.toList());
        }
        Set<TalentContact> talentDuplicationsByContacts = talentContactService.getTalentDuplicationsByContacts(ignoreTalentId, contactVmList);
        if(!talentDuplicationsByContacts.isEmpty()) {
            addContactCheckDuplication(result, talentDuplicationsByContacts);
        }
        fillConfidentialDuplicateInfo(result);


        //如果重复数据的owner为爬虫用户： <EMAIL> 的userid ：12243   则设置当前用户为onwer
        resetTalentOwner(result);
        // 填充重复候选人的工作经历
        fillDuplicationsTalentExperiences(result);

        log.info("getSuspectededDuplicationsByContactAndSmilarity result : {}", result);
        return result;
    }

    private void resetTalentOwner(List<SuspectedDuplications> result) {
        if(CollUtil.isEmpty(result)) {
            return;
        }
        //获取这些重复候选人的所有 owners
        List<Long> talentIds = result.stream().map(talent  -> Long.parseLong(talent.get_id())).distinct().toList();
        List<TalentOwnership> owners = talentOwnershipRepository.findAllByTalentIdIn(talentIds);
        if(CollUtil.isNotEmpty(owners)) {
            List<TalentOwnership> toBeSaved = new ArrayList<>();
            owners.forEach(owner -> {
                if(TALENT_CRAWLER_BOT_USER_ID.equals(owner.getUserId())) {
                    owner.setUserId(SecurityUtils.getUserId());
                    toBeSaved.add(owner);
                }
            });
            //更新talent owner
            if(CollUtil.isNotEmpty(toBeSaved)) {
                // 手动控制事务
                DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
                TransactionStatus status = transactionManager.getTransaction(definition);
                try {
                    talentOwnershipRepository.saveAll(toBeSaved);
                    entityManager.flush();
                    transactionManager.commit(status);
                } catch (Exception e) {
                    transactionManager.rollback(status);
                    log.error("[APN: resetTalentOwner @{}] update Talent owner error, talent info : {}， error: {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(toBeSaved), JSONUtil.toJsonStr(e));
                    throw e;
                }
            }
        }
    }

    private void fillDuplicationsTalentExperiences(List<SuspectedDuplications> result) {
        if (CollUtil.isEmpty(result)) {
            return;
        }
        List<Long> talentIds = result.stream().map(talent  -> Long.parseLong(talent.get_id())).distinct().toList();
        Map<Long, TalentV3> talentV3Map = talentRepository.findAllByIdIsIn(talentIds).stream().collect(Collectors.toMap(TalentV3::getId, Function.identity()));
        result.forEach(duplication -> {
            TalentV3 talent = talentV3Map.get(Long.parseLong(duplication.get_id()));
            if (talent != null) {
                TalentDTOV3 talentDTOV3 = TalentDTOV3.fromTalent(talent);
                List<TalentExperienceDTO> experiences = talentDTOV3.getExperiences();
                if(CollUtil.isNotEmpty(experiences)) {
                    duplication.setExperiences(experiences);
                }
            }
        });
    }

    private Set<Long> getTalentPermissionTeamIds(Long id) {
        Set<Long> ret = new HashSet<>();
        TalentV3 talent = talentRepository.findById(id).orElseThrow(() -> new NotFoundException("Cannot find talent"));
        Set<Long> permissionUserIds = new HashSet<>();
        permissionUserIds.add(talent.getPermissionUserId());
        List<TalentOwnership> owner = talentOwnershipRepository.findAllByTalentIdAndOwnershipType(id, TalentOwnershipType.TALENT_OWNER);
        permissionUserIds.addAll(owner.stream().map(TalentOwnership::getUserId).collect(Collectors.toSet()));
        permissionUserIds = permissionUserIds.stream().filter(Objects::nonNull).collect(Collectors.toSet());
        permissionUserIds.forEach(c -> {
            if(c == null) {
                return;
            }
            PermissionUserTeamPermissionVM.PermissionDetail permissionVM = userService.getAllDataPermissionsByUserId(c).getBody();
            Long primaryTeamId = permissionVM.getPrimaryTeamId();
            if(primaryTeamId != null) {
                ret.add(primaryTeamId);
            }
            Set<Long> teamIds = permissionVM.getTeamIds();
            if(teamIds != null) {
                ret.addAll(teamIds);
            }
        });
        return ret;
    }

    private boolean containsAny(Set<Long> setA, Set<Long> setB) {
        for (Long element : setB) {
            if (setA.contains(element)) {
                return true;
            }
        }
        return false;
    }


    private void filterContact(List<TalentContactDTO> contactSet) {
        if(contactSet != null) {
            contactSet.forEach(c -> {
                String contact = c.getContact();
                if(StringUtils.isNotEmpty(contact)) {
                    c.setContact(StrUtil.repeat('*', contact.length()));
                }
                String details = c.getDetails();
                if(StringUtils.isNotEmpty(details)) {
                    c.setDetails(StrUtil.repeat('*', details.length()));
                }
                String info = c.getInfo();
                if(StringUtils.isNotEmpty(info)) {
                    c.setInfo(StrUtil.repeat('*', info.length()));
                }
            });
        }
    }

    @Override
    public void setReviewNoteAttachUserInfo(List<TalentReviewNoteDTO> noteList) {
        if(noteList == null) {
            return;
        }
        Map<Long, SimpleUser> reviewUserMap = getReviewNoteUsers(noteList);
        Map<Long, FolderSharedTeamDTO> reviewTeamMap = getReviewNoteTeams(noteList);
        Map<Long, SimpleUser> simpleUsers = getSimpleUsers(noteList);
        for(TalentReviewNoteDTO dto : noteList) {
            String createdBy = dto.getCreatedBy();
            if(createdBy != null && dto.getCreatedUser() == null) {
                dto.setCreatedUser(simpleUsers.get(extractUserId(createdBy)));
            }
            String lastModifiedBy = dto.getLastModifiedBy();
            if(lastModifiedBy != null && dto.getLastModifiedUser() == null) {
                dto.setLastModifiedUser(simpleUsers.get(extractUserId(lastModifiedBy)));
            }
            if(ReviewedByType.USER.equals(dto.getReviewedByType())) {
                SimpleUser simpleUser = reviewUserMap.get(dto.getReviewedBy());
                if(simpleUser != null) {
                    dto.setReviewedByName(CommonUtils.formatFullName(simpleUser.getFirstName(), simpleUser.getLastName()));
                }
            } else {
                FolderSharedTeamDTO folderSharedTeamDTO = reviewTeamMap.get(dto.getReviewedBy());
                if(folderSharedTeamDTO != null) {
                    dto.setReviewedByName(folderSharedTeamDTO.getTeamName());
                }
            }
        }
    }

    private static Integer talentBrowseQuota = null;

    public BrowseQuotaDTO getBrowseQuota() {
        BrowseQuotaDTO ret = new BrowseQuotaDTO();
        Integer browseQuota = getCacheBrowseQuota();
        ret.setRemain(getRemainBrowseCount(browseQuota));
        ret.setTotal(browseQuota);
        return ret;
    }

    private Integer getCacheBrowseQuota() {
        if(talentBrowseQuota == null) {
            talentBrowseQuota = userService.getTenantParamValue(TALENT_BROWSE_QUOTA).getBody();
        }
        return talentBrowseQuota;
    }

    @Override
    public List<TalentContactDTO> getTalentContacts(String sId, Long jobId) {
        //兼容推荐时搜索commonpool id的联系方式
        if(!NumberUtil.isNumber(sId)) {
            try {
                List<TalentContactDTO> ret = getCommonPoolContacts(sId);
                if(jobId != null) {
                    RecommendFeedback dto = new RecommendFeedback();
                    dto.setTalentId(sId);
                    dto.setJobId(jobId);
                    dto.setReason(RecommendFeedbackReason.GET_TALENT_CONTACT);
                    talentServiceV3.recordTalentJobRecommend(dto);
                }
                return ret;
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
        Long id = Long.parseLong(sId);
        if(!hasTalentViewAuthority(id)) {
            throw new CustomParameterizedException(Status.FORBIDDEN.getStatusCode(), "No permission", "No client contact access permission");
        }
        if(!hasRemain(id)) {
            throw new CustomParameterizedException(Status.TOO_MANY_REQUESTS.getStatusCode(), "No limit", "Today's browsing limit has been reached");
        }

//        List<TalentContact> contactSet = talentContactRepository.findAllByTalentIdAndStatusAndTypeIn(id, TalentContactStatus.AVAILABLE, ContactTypeConstants.COMPREHENSIVE_CONTACT_TYPES);
        List<TalentContact> contactSet = talentContactRepository.findAllByTalentIdAndStatusAndVerificationStatusIsNotAndTypeIn(id, TalentContactStatus.AVAILABLE, TalentContactVerificationStatus.WRONG_CONTACT, ContactTypeConstants.COMPREHENSIVE_CONTACT_TYPES);
        return Convert.toList(TalentContactDTO.class, contactSet.stream().sorted(Comparator.comparing(TalentContact::getSort)).collect(Collectors.toList()));
    }


    @Override
    public List<TalentContactDTO> getTalentAllVerificationStatusContacts(Long id) {
        //only used for voip report, otherwise please use getTalentContacts
        if(!hasTalentViewAuthority(id)) {
            throw new CustomParameterizedException(Status.FORBIDDEN.getStatusCode(), "No permission", "No client contact access permission");
        }
        if(!hasRemain(id)) {
            throw new CustomParameterizedException(Status.TOO_MANY_REQUESTS.getStatusCode(), "No limit", "Today's browsing limit has been reached");
        }
        List<TalentContact> contactSet = talentContactRepository.findAllByTalentIdAndStatusAndTypeIn(id, TalentContactStatus.AVAILABLE, ContactTypeConstants.COMPREHENSIVE_CONTACT_TYPES);
        return Convert.toList(TalentContactDTO.class, contactSet.stream().sorted(Comparator.comparing(TalentContact::getSort)).collect(Collectors.toList()));
    }


    private List<TalentContactDTO> getCommonPoolContacts(String id) throws JsonProcessingException {
        List<CreditTransaction> creditTransactions = userService.findAllCreditTransactionByTenantIdAndStatusAndSearchEsIdIn(SecurityUtils.getTenantId(), com.altomni.apn.common.domain.enumeration.user.Status.Available, List.of(id)).getBody();
        if(creditTransactions == null || creditTransactions.isEmpty()) {
            return new ArrayList<>();
        }
        CreditTransaction creditTransaction = creditTransactions.get(0);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL);
        objectMapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);

        List<TalentContactDTO> contacts = objectMapper.readValue(creditTransaction.getEsContacts(), new TypeReference<List<TalentContactDTO>>() {});
        if (ObjectUtil.isEmpty(contacts)) {
            contacts = new ArrayList<>();
        }
        return contacts;
    }

//    @Override
//    public List<SuspectedDuplications> getTalentContacts(Long id) {
//        if(!hasRemain(id)) {
//            throw new CustomParameterizedException(Status.TOO_MANY_REQUESTS.getStatusCode(), "No limit", "Today's browsing limit has been reached");
//        }
//
//        List<TalentContact> contactSet = talentContactRepository.findAllByTalentIdAndStatusAndTypeIn(id, TalentContactStatus.AVAILABLE, ContactTypeConstants.COMPREHENSIVE_CONTACT_TYPES);
//        if(contactSet == null || contactSet.isEmpty()) {
//            return new ArrayList<>();
//        }
//        List<SuspectedDuplications> result = new ArrayList<>();
//        addContactCheckDuplication(result, new HashSet<>(contactSet));
//        return result;
//    }

    private boolean hasRemainByTalent(TalentV3 talentV3,List<TalentOwnershipDTO> ownerships) {
        //候选人的创建者，拥有者，共享人，流程拥有者始终可看
        List<Long> notLimit = new ArrayList<>();
        notLimit.add(extractUserId(talentV3.getCreatedBy()));
        notLimit.addAll(ownerships.stream().map(TalentOwnershipDTO::getUserId).toList());
        if(notLimit.contains(SecurityUtils.getUserId())) {
            return true;
        }
        return checkRemainPermission(talentV3.getId());
    }


    private boolean checkRemainPermission(Long id) {
        boolean exist = commonRedisService.setExist(TalentServiceImpl.getUserBrowseTalentKey(), id.toString());
        Integer browseQuota = getCacheBrowseQuota();
        if(exist) {
            return true;
        } else {
            if(getRemainBrowseCount(browseQuota) > 0) {
                commonRedisService.addSetValue(TalentServiceImpl.getUserBrowseTalentKey(), id.toString());
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean hasRemain(Long id) {
        //候选人的创建者，拥有者，共享人，流程拥有者始终可看
        Optional<TalentV3> optTalent = talentRepository.findById(id);
        if(optTalent.isEmpty()) {
            return false;
        }
        List<Long> notLimit = getNotLimitUserId(optTalent);
        if(notLimit.contains(SecurityUtils.getUserId())) {
            return true;
        }
        return checkRemainPermission(id);
    }

    private List<Long> getNotLimitUserId(Optional<TalentV3> optTalent) {
        TalentV3 talentV3 = optTalent.get();
        List<Long> ret = new ArrayList<>();
        ret.add(extractUserId(talentV3.getCreatedBy()));
        ret.addAll(talentOwnershipRepository.findAllByTalentId(talentV3.getId()).stream().map(TalentOwnership::getUserId).toList());


        return ret;
    }

    private int getRemainBrowseCount(Integer browseQuota) {
        int setCount = commonRedisService.getSetCount(getUserBrowseTalentKey());

        log.info("getRemainBrowseCount setCount:{}, browseQuota:{}", setCount, browseQuota);
        int remain = browseQuota - setCount;
        return Math.max(remain, 0);
    }

    private static final String BROWSE_RECORD = "BROWSE_RECORD:";

    public static String getUserBrowseTalentKey() {
        return BROWSE_RECORD + SecurityUtils.getUserId();
    }

    private Map<Long, SimpleUser> getReviewNoteUsers(List<TalentReviewNoteDTO> talentReviewNoteDTOList) {
        Set<Long> userIds = talentReviewNoteDTOList.stream().filter(f -> ReviewedByType.USER.equals(f.getReviewedByType())).map(TalentReviewNoteDTO::getReviewedBy).collect(Collectors.toSet());
        return simpleUserRepository.findAllById(userIds).stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity()));
    }

    private Map<Long, FolderSharedTeamDTO> getReviewNoteTeams(List<TalentReviewNoteDTO> talentReviewNoteDTOList) {
        Set<Long> teamIds = talentReviewNoteDTOList.stream().filter(f -> ReviewedByType.TEAM.equals(f.getReviewedByType())).map(TalentReviewNoteDTO::getReviewedBy).collect(Collectors.toSet());
        return reviewNoteRepository.findFolderSharedTeamDTO(teamIds).stream().collect(Collectors.toMap(FolderSharedTeamDTO::getTeamId, Function.identity()));
    }

    private void filterRelationData(TalentDTOV3 ret, String body) {
        if(StringUtils.isEmpty(body)) {
            return;
        }
        JSONObject jsonObject = JSONUtil.parseObj(body);
        String config = jsonObject.getStr("customConfig");
        List<Map<String, Object>> list = new Gson().fromJson(config, new TypeToken<List<HashMap<String, Object>>>() {}.getType());
        Map<String, Map<String, Object>> resultMap = list.stream()
                .collect(Collectors.toMap(map -> (String) map.get("field"), map -> map));
        if(!isVisible(resultMap, TalentFormRelateField.FIRST_NAME)) {
            ret.setFirstName(null);
        }
        if(!isVisible(resultMap, TalentFormRelateField.LAST_NAME)) {
            ret.setLastName(null);
        }
        if(!isVisible(resultMap, TalentFormRelateField.BIRTHDATE)) {
            ret.setBirthDate(null);
        }
        if(!isVisible(resultMap, TalentFormRelateField.CONTACTS)) {
            ret.setContacts(null);
        }
        if(!isVisible(resultMap, TalentFormRelateField.CURRENT_LOCATION)) {
            ret.setCurrentLocation(null);
        }
        if(!isVisible(resultMap, TalentFormRelateField.ZIP_CODE)) {
            ret.setZipCode(null);
        }
        if(!isVisible(resultMap, TalentFormRelateField.LANGUAGES)) {
            ret.setLanguages(null);
        }
        if(!isVisible(resultMap, TalentFormRelateField.INDUSTRIES)) {
            ret.setIndustries(null);
        }
        if(!isVisible(resultMap, TalentFormRelateField.JOB_FUNCTIONS)) {
            ret.setJobFunctions(null);
        }
        if(!isVisible(resultMap, TalentFormRelateField.WORK_AUTHORIZATION)) {
            ret.setWorkAuthorization(null);
        }
        if(!isVisible(resultMap, TalentFormRelateField.MOTIVATION_ID)) {
            ret.setMotivationId(null);
        }
        if(!isVisible(resultMap, TalentFormRelateField.OWNER)) {
            List<TalentOwnershipDTO> ownerships = ret.getOwnerships();
            if(ownerships != null) {
                ret.setOwnerships(ownerships.stream().filter(p -> p.getOwnershipType() != TalentOwnershipType.TALENT_OWNER).collect(Collectors.toList()));
            }
        }
        if(!isVisible(resultMap, TalentFormRelateField.SHARED)) {
            List<TalentOwnershipDTO> ownerships = ret.getOwnerships();
            if(ownerships != null) {
                ret.setOwnerships(ownerships.stream().filter(p -> p.getOwnershipType() != TalentOwnershipType.SHARE).collect(Collectors.toList()));
            }
        }
    }

    private boolean isVisible(Map<String, Map<String, Object>> resultMap, String field) {
        Map<String, Object> map = resultMap.get(field);
        if(map == null) {
            return true;
        }
        Object oVisible = map.get("visible");
        return oVisible == null || (Boolean) oVisible;
    }

    private void addApproverTagToContact(List<TalentContactDTO> contactList) {
        Set<String> emailSet = contactList.stream().filter(t -> t.getType().equals(ContactType.EMAIL)).map(TalentContactDTO::getContact).collect(Collectors.toSet());
        List<TimeSheetUserDTO> timeSheetUserList = jobdivaService.findByUsernameOrEmailList(emailSet).getBody();
        if (CollUtil.isNotEmpty(timeSheetUserList)){
            Set<String> existsEmails = new HashSet<>();
            timeSheetUserList.forEach(t -> {
                existsEmails.add(t.getUsername());
                existsEmails.add(t.getEmail());
            });
            contactList.stream().filter(t -> t.getType().equals(ContactType.EMAIL) && existsEmails.contains(t.getContact())).forEach(t -> t.setApproverEmail(true));
        }
    }

    private void setNoteAttachUserInfo(List<TalentNoteDTO> noteList) {
        if(noteList == null) {
            return;
        }
        Map<Long, SimpleUser> simpleUsers = getSimpleUsers(noteList);
        for(TalentNoteDTO dto : noteList) {
            String createdBy = dto.getCreatedBy();
            if(createdBy != null && dto.getCreatedUser() == null) {
                dto.setCreatedUser(simpleUsers.get(extractUserId(createdBy)));
            }
            String lastModifiedBy = dto.getLastModifiedBy();
            if(lastModifiedBy != null && dto.getLastModifiedUser() == null) {
                dto.setLastModifiedUser(simpleUsers.get(extractUserId(lastModifiedBy)));
            }
        }
    }


    private Map<Long, SimpleUser> getSimpleUsers(List<? extends AbstractAuditingEntity> entities) {
        List<Long> userIds = entities.stream().filter(Objects::nonNull).flatMap(entity -> {
            Optional<Long> creatorId = Optional.ofNullable(entity.getCreatedBy()).map(createBy -> Long.parseLong(createBy.split(",")[0]));
            Optional<Long> modifierId = Optional.ofNullable(entity.getLastModifiedBy()).map(modifyBy -> Long.parseLong(modifyBy.split(",")[0]));
            return Stream.of(creatorId, modifierId).filter(Optional::isPresent).map(Optional::get);
        }).toList();
        return simpleUserRepository.findAllById(userIds).stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity()));

    }
    private Long extractUserId(String str) {
        return Long.parseLong(str.split(",")[0]);
    }

    private List<TalentOwnershipDTO> toDtos(List<TalentOwnership> talentOwnerships) {
        List<Long> userIds = talentOwnerships.stream().filter(talentOwnership-> !TalentOwnershipType.TENANT_SHARE.equals(talentOwnership.getOwnershipType())).map(TalentOwnership::getUserId).toList();
        List<User> users = userService.findByIds(userIds).getBody();
        Map<Long, User> userMap = CollectionUtils.isEmpty(users) ? Collections.emptyMap()
            : users.stream().collect(Collectors.toMap(User::getId, Function.identity()));

        return talentOwnerships.stream().map(this::toDto).map(dto -> {
            if(!TalentOwnershipType.TENANT_SHARE.equals(dto.getOwnershipType())){
                dto.setUser(ServiceUtils.convert2DTO(userMap.get(dto.getUserId()), NameOnlyUser.class));
            }
            return dto;
        }).collect(Collectors.toList());
    }

    private TalentOwnershipDTO toDto(TalentOwnership talentOwnership) {
        TalentOwnershipDTO result = new TalentOwnershipDTO();
        ServiceUtils.myCopyProperties(talentOwnership, result);
        return result;
    }

    @Autowired
    private TalentAdditionalInfoRepository talentAdditionalInfoRepository;

    @Override
    public List<TelephoneChatScript> getTelephoneChatScripts() {
        return enumCommonService.findAllTelephoneChatScript();
    }

    private List<TalentDTOV3> findTalentDtoByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }


        List<TalentV3> talentV3List = talentRepository.findAllByIdIn(ids).stream().filter(o -> o.getTenantId().equals(SecurityUtils.getTenantId())).collect(Collectors.toList());
        List<Long> filterTalentIdList = talentV3List.stream().map(TalentV3::getId).collect(Collectors.toList());

        CompletableFuture<List<TalentContact>> contactFuture = CompletableFuture.supplyAsync(() -> talentContactRepository.findAllByTalentIdInAndStatus(filterTalentIdList, TalentContactStatus.AVAILABLE), executor);
        CompletableFuture<List<TalentResumeDTO>> talentResumeFuture = CompletableFuture.supplyAsync(() -> talentResumeService.findAllWithoutPortraitByTalentIds(filterTalentIdList), executor);
        CompletableFuture<List<TalentNote>> talentNoteFuture = CompletableFuture.supplyAsync(() -> talentNoteRepository.findAllByTalentIdInOrderByCreatedDateDesc(filterTalentIdList), executor);
        CompletableFuture<List<TalentOwnership>> talentOwnershipFuture = CompletableFuture.supplyAsync(() -> talentOwnershipRepository.findAllByTalentIdInAndExpireTimeGreaterThanEqual(filterTalentIdList, Instant.now()), executor);
        CompletableFuture<List<TalentCurrentLocation>> talentCurrentLocationFuture = CompletableFuture.supplyAsync(() -> talentLocationRepository.findByTalentIdIn(filterTalentIdList), executor);
        CompletableFuture.allOf(contactFuture, talentResumeFuture, talentNoteFuture, talentOwnershipFuture, talentCurrentLocationFuture).exceptionally(FutureExceptionUtil::handleFutureException);

        List<TalentContact> contactList = contactFuture.join();
        List<TalentResumeDTO> talentResumeDTOList = talentResumeFuture.join();
        List<TalentNote> talentNoteList = talentNoteFuture.join();
        List<TalentOwnership> talentOwnershipList = talentOwnershipFuture.join();
        List<TalentCurrentLocation> talentCurrentLocationList = talentCurrentLocationFuture.join();

        Map<Long, List<TalentContact>> contactListMap = contactList.stream().collect(Collectors.groupingBy(TalentContact::getTalentId));
        Map<Long, List<TalentResumeDTO>> talentResumeDTOListMap = talentResumeDTOList.stream().collect(Collectors.groupingBy(TalentResumeDTO::getTalentId));
        Map<Long, List<TalentNote>> talentNoteListMap = talentNoteList.stream().collect(Collectors.groupingBy(TalentNote::getTalentId));
        Map<Long, List<TalentOwnership>> talentOwnershipListMap = talentOwnershipList.stream().collect(Collectors.groupingBy(TalentOwnership::getTalentId));
        Map<Long, TalentCurrentLocation> talentCurrentLocationMap = talentCurrentLocationList.stream().collect(Collectors.toMap(TalentCurrentLocation::getTalentId, TalentCurrentLocation -> TalentCurrentLocation));
        List<TalentDTOV3> result = new ArrayList<>();
        for (TalentV3 talent : talentV3List) {
            TalentDTOV3 talentDTOV3 = TalentDTOV3.fromTalent(talent);
            List<TalentContact> contactSet = contactListMap.get(talent.getId());
            if (contactSet != null) {
                talentDTOV3.setContacts(Convert.toList(TalentContactDTO.class, contactSet.stream().sorted(Comparator.comparing(TalentContact::getSort)).collect(Collectors.toList())));
            }
            talentDTOV3.setResumes(talentResumeDTOListMap.get(talent.getId()));
            talentDTOV3.setNotes(Convert.toList(TalentNoteDTO.class, talentNoteListMap.get(talent.getId())));
            talentDTOV3.setOwnerships(Convert.toList(TalentOwnershipDTO.class, talentOwnershipListMap.get(talent.getId())));
            talentDTOV3.setIsAM(checkAMPermission(talent.getId()));
            TalentCurrentLocation currentLocation = talentCurrentLocationMap.get(talent.getId());
            if (ObjectUtil.isNotEmpty(currentLocation)) {
                talentDTOV3.setCurrentLocation(JSONUtil.toBean(currentLocation.getOriginalLoc(), LocationDTO.class));
            }
            result.add(talentDTOV3);
        }
        return result;
    }

    private List<TalentDTOV3> findTalentByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return null;
        }

        List<TalentV3> talentV3List = talentRepository.findAllByIdIn(ids).stream().filter(o -> o.getTenantId().equals(SecurityUtils.getTenantId())).collect(Collectors.toList());
        List<Long> filterTalentIdList = talentV3List.stream().map(TalentV3::getId).collect(Collectors.toList());

        List<TalentContact> contactList = talentContactRepository.findAllByTalentIdInAndStatus(filterTalentIdList, TalentContactStatus.AVAILABLE);
        List<TalentResumeDTO> talentResumeDTOList = talentResumeService.findAllWithoutPortraitByTalentIds(filterTalentIdList);
        List<TalentNote> talentNoteList = talentNoteRepository.findAllByTalentIdInOrderByCreatedDateDesc(filterTalentIdList);
        List<TalentOwnership> talentOwnershipList = talentOwnershipRepository.findAllByTalentIdInAndExpireTimeGreaterThanEqual(filterTalentIdList, Instant.now());
        List<TalentCurrentLocation> talentCurrentLocationList = talentLocationRepository.findByTalentIdIn(filterTalentIdList);

        Map<Long, List<TalentContact>> contactListMap = contactList.stream().collect(Collectors.groupingBy(TalentContact::getTalentId));
        Map<Long, List<TalentResumeDTO>> talentResumeDTOListMap = talentResumeDTOList.stream().collect(Collectors.groupingBy(TalentResumeDTO::getTalentId));
        Map<Long, List<TalentNote>> talentNoteListMap = talentNoteList.stream().collect(Collectors.groupingBy(TalentNote::getTalentId));
        Map<Long, List<TalentOwnership>> talentOwnershipListMap = talentOwnershipList.stream().collect(Collectors.groupingBy(TalentOwnership::getTalentId));
        Map<Long, TalentCurrentLocation> talentCurrentLocationMap = talentCurrentLocationList.stream().collect(Collectors.toMap(TalentCurrentLocation::getTalentId, TalentCurrentLocation -> TalentCurrentLocation));

        if (!SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.ADMIN) && CollUtil.isEmpty(talentV3List)) {
            return null;
        } else {
            List<TalentDTOV3> result = new ArrayList<>();
            for (TalentV3 talent : talentV3List) {
                TalentDTOV3 talentDTOV3 = TalentDTOV3.fromTalent(talent);
                List<TalentContact> contactSet = contactListMap.get(talent.getId());
                if(contactSet != null) {
                    talentDTOV3.setContacts(Convert.toList(TalentContactDTO.class, contactSet.stream().sorted(Comparator.comparing(TalentContact::getSort)).collect(Collectors.toList())));
                }
                talentDTOV3.setResumes(talentResumeDTOListMap.get(talent.getId()));
                talentDTOV3.setNotes(Convert.toList(TalentNoteDTO.class, talentNoteListMap.get(talent.getId())));
                talentDTOV3.setOwnerships(Convert.toList(TalentOwnershipDTO.class, talentOwnershipListMap.get(talent.getId())));
                talentDTOV3.setIsAM(checkAMPermission(talent.getId()));
                TalentCurrentLocation currentLocation = talentCurrentLocationMap.get(talent.getId());
                if (ObjectUtil.isNotEmpty(currentLocation)) {
                    talentDTOV3.setCurrentLocation(JSONUtil.toBean(currentLocation.getOriginalLoc(), LocationDTO.class));
                }
                result.add(talentDTOV3);
            }
            return result;
        }
    }

    private boolean checkAMPermission(Long talentId) {
        // universal version: tenant 4
        if (!ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())) {
            return false;
        }

        SecurityContext context = SecurityContextHolder.getContext();

        try {
            CompletableFuture<Integer> countApplicationsFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return applicationService.countApplicationByTalentId(talentId, NodeType.ON_BOARD.toDbValue()).getBody();
            });

            CompletableFuture<Integer> accountManagerIdsFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return companyService.countCompanyAccountManager(talentId, SecurityUtils.getUserId()).getBody();
            });

            return countApplicationsFuture.thenCombine(accountManagerIdsFuture, (countApplications, accountManagerIds) -> {
                return countApplications != 0 &&
                        (accountManagerIds != 0 || SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.TENANT_ADMIN));
            }).join();
        } finally {
            // 清理安全上下文
            SecurityContextHolder.clearContext();
        }
    }

    /*private Boolean checkAMPermission(Long talentId) {
        Boolean b = false;
        //universal version: tenant 4
        if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())) {
            SecurityContext context = SecurityContextHolder.getContext();

            CompletableFuture<Integer> countApplicationsFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return applicationService.countApplicationByTalentId(talentId, NodeType.ON_BOARD.toDbValue()).getBody();
            });

            CompletableFuture<Integer> accountManagerIdsFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return companyService.countCompanyAccountManager(talentId, SecurityUtils.getUserId()).getBody();
            });
            Integer countApplications = countApplicationsFuture.join();
            Integer accountManagerIds = accountManagerIdsFuture.join();
            if (countApplications != 0 && (accountManagerIds != 0 || SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.TENANT_ADMIN))) {
                b = true;
            }
        }
        return b;
    }*/

    @Override
    public TalentDTOV3 findTalentByIdWithoutEntity(Long id) {
        TalentV3 talent = talentRepository.findById(id).orElseThrow(() -> new NotFoundException("Cannot find talent"));
        if (!SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.ADMIN) && !SecurityUtils.isCurrentTenant(talent.getTenantId())) {
            return null;
        } else {
            return sortTalentDTO(TalentDTOV3.fromTalent(talent));
        }
    }

    private void checkEditableDataPermission(TalentV3 dbTalent) {
        if (SecurityUtils.isAdmin()) {
            return;
        }
        Long talentId = dbTalent.getId();
        Long userId = SecurityUtils.getUserId();
        Long tenantId = SecurityUtils.getTenantId();
        Set<Long> allAllowUserIdSet = new HashSet<>(); // creator, shares, owner
        allAllowUserIdSet.add(dbTalent.getPermissionUserId());
        List<TalentOwnershipType> ownershipTypeList = List.of(TalentOwnershipType.SHARE, TalentOwnershipType.TALENT_OWNER, TalentOwnershipType.TENANT_SHARE);
        List<TalentOwnership> allOwnership = talentOwnershipRepository.findAllByTalentIdAndOwnershipTypeIn(talentId, ownershipTypeList);
        List<Long> dbOwnerList = allOwnership.stream().filter(p -> TalentOwnershipType.TALENT_OWNER.equals(p.getOwnershipType())).map(TalentOwnership::getUserId).sorted().collect(Collectors.toList());
        List<Long> dbShareList = allOwnership.stream().filter(p -> TalentOwnershipType.SHARE.equals(p.getOwnershipType())).map(TalentOwnership::getUserId).sorted().collect(Collectors.toList());
        List<Long> publicTalents = allOwnership.stream().filter(p -> TalentOwnershipType.TENANT_SHARE.equals(p.getOwnershipType())).map(TalentOwnership::getUserId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(publicTalents)){
            return;
        }
        allAllowUserIdSet.addAll(dbOwnerList);
        allAllowUserIdSet.addAll(dbShareList);
        allAllowUserIdSet.add(dbTalent.getPermissionUserId());
//        if(checkCreateByAndOwner(dbTalent, dbOwnerList)) {
//            return;
//        }
        if (isClientContact(talentId)){ // 客户联系人的判断逻辑
            TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateClientContactDataPermissionByUserId(tenantId, userId).getBody();
            log.info("teamDataPermission=" + teamDataPermission);
            log.info("tenant id=" + tenantId);
            log.info("user id=" + userId);
            log.info("talentId id=" + talentId);
            if (BooleanUtils.isTrue(teamDataPermission.getAll())) {
                return;
            }
            //分享给所有人的都可以访问
//            List<TalentOwnership> allShare = talentOwnershipRepository.findAllByTalentIdAndOwnershipType(talentId, TalentOwnershipType.TENANT_SHARE);
//            if(!allShare.isEmpty()) {
//                return;
//            }
            if (allAllowUserIdSet.contains(userId)) {
                return;
            }
            log.info("allAllowUserIdSet=" + allAllowUserIdSet);
            if (BooleanUtils.isTrue(teamDataPermission.getSelf())) {
                if (!allAllowUserIdSet.contains(userId)) {
                    throw new ForbiddenException("You have no permission to edit this contact!");
                }
            } else if (CollUtil.isNotEmpty(teamDataPermission.getReadableTeamIds())) {
                Set<Long> talentTeamIds = talentOwnershipRepository.getTeamIdsByTalentId(talentId);
                log.info("talentTeamIds=" + talentTeamIds);
                Collection<? extends Serializable> intersection = CollectionUtils.intersection(teamDataPermission.getWritableTeamIds(), talentTeamIds);
                log.info("intersection" + intersection.toString());
                if (intersection.isEmpty()) {
                    throw new ForbiddenException("You have no permission to edit this contact!");
                }
            } else {
                throw new ForbiddenException("You have no permission to edit this contact!");
            }
        }
    }

    private void checkModifyOwnershipPermission(TalentV3 dbTalent, TalentDTOV3 update) {
        List<TalentOwnershipType> ownershipTypeList = List.of(TalentOwnershipType.SHARE, TalentOwnershipType.TALENT_OWNER);
        List<TalentOwnership> allOwnership = talentOwnershipRepository.findAllByTalentIdAndOwnershipTypeIn(dbTalent.getId(), ownershipTypeList);
        List<Long> dbOwnerList = allOwnership.stream().filter(p -> TalentOwnershipType.TALENT_OWNER.equals(p.getOwnershipType())).map(TalentOwnership::getUserId).sorted().collect(Collectors.toList());
        if(checkCreateByAndOwner(dbTalent, dbOwnerList)) {
            return;
        }
        List<Long> dbShareList = allOwnership.stream().filter(p -> TalentOwnershipType.SHARE.equals(p.getOwnershipType())).map(TalentOwnership::getUserId).sorted().collect(Collectors.toList());
        List<TalentOwnershipDTO> ownerships = update.getOwnerships();
        if(ownerships != null) {
            List<Long> inputOwnerList = ownerships.stream().filter(p -> TalentOwnershipType.TALENT_OWNER.equals(p.getOwnershipType())).map(TalentOwnershipDTO::getUserId).sorted().collect(Collectors.toList());
            List<Long> inputShareList = ownerships.stream().filter(p -> TalentOwnershipType.SHARE.equals(p.getOwnershipType())).map(TalentOwnershipDTO::getUserId).sorted().collect(Collectors.toList());
           if(!dbOwnerList.equals(inputOwnerList) || !dbShareList.equals(inputShareList)) {
               throw new CustomParameterizedException("You have no permission to update Ownership Information - shares .");
           }
        }
    }

    private boolean checkCreateByAndOwner(TalentV3 dbTalent, List<Long> dbOwnerList) {
        String createdBy = dbTalent.getCreatedBy();
        String createUserId = getUserIdFromCreatedBy(createdBy);
        return createUserId.equals(String.valueOf(SecurityUtils.getUserId())) || dbOwnerList.contains(SecurityUtils.getUserId());
    }


    private String getUserIdFromCreatedBy(String createdBy) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(createdBy)) {
            String[] idStrings = createdBy.split(",");
            if (idStrings.length == 2) {
                return idStrings[0];
            } else {
                return createdBy;
            }
        }
        return "unknown";
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public TalentDTOV3 updateTalentInfo(TalentDTOV3 dto) {
        // 手动控制事务
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);

        try {
            TalentV3 dbTalent = talentRepository.getById(dto.getId());
            checkPermission(dbTalent);
            dbTalent.setPhotoUrl(dto.getPhotoUrl());
            //names
            if (ObjectUtil.isNotNull(dto.getFirstName()) || ObjectUtil.isNotNull(dto.getLastName())) {
                if (ObjectUtil.isNotNull(dto.getFirstName())) {
                    dbTalent.setFirstName(dto.getFirstName());
                }
                if (ObjectUtil.isNotNull(dto.getLastName())) {
                    dbTalent.setLastName(dto.getLastName());
                }
                if ((ObjectUtil.isNotNull(dto.getFirstName()) && ObjectUtil.isNotNull(dto.getLastName())) || ObjectUtil.isNotNull(dto.getFullName())) {
                    dbTalent.setFullName(CommonUtils.formatFullName(dto.getFirstName(), dto.getLastName(), dto.getFullName()));
                }
            }

            //additional info
            TalentAdditionalInfo talentAdditionalInfo = dbTalent.getTalentAdditionalInfo();
            if (talentAdditionalInfo == null) {
                talentAdditionalInfo = new TalentAdditionalInfo();
            }
            JSONObject additionalJson = new JSONObject();
            if (ObjectUtil.isNotEmpty(talentAdditionalInfo.getExtendedInfo())) {
                additionalJson = JSONUtil.parseObj(talentAdditionalInfo.getExtendedInfo());
            }
            if (ObjectUtil.isNotEmpty(dto.getCurrency())) {
                additionalJson.put("currency", dto.getCurrency());
            }else{
                additionalJson.remove("currency");
            }
            if (ObjectUtil.isNotEmpty(dto.getPayType())) {
                additionalJson.put("payType", dto.getPayType());
            }else{
                additionalJson.remove("payType");
            }
            if (ObjectUtil.isNotEmpty(dto.getSalaryRange()) && (ObjectUtil.isNotNull(dto.getSalaryRange().getGte()) || ObjectUtil.isNotNull(dto.getSalaryRange().getLte()))) {
                additionalJson.put("salaryRange", dto.getSalaryRange());
            }else {
                additionalJson.remove("salaryRange");
            }
            //locations
            LocationDTO currentLocation = dto.getCurrentLocation();
            currentLocation.setZipcode(dto.getZipCode());

            //业务调整，候选人如果是联系人的话也不需要和companyLocation强关联
            if (ObjectUtil.isNotEmpty(currentLocation)) {
                if(ObjectUtil.isNotNull(dto.getAddressLine())){
                    currentLocation.setAddressLine(dto.getAddressLine());
                }
                updateLocation(dbTalent.getId(), dto.getZipCode(), currentLocation);
            }
            if (ObjectUtil.isNotEmpty(additionalJson)) {
                talentAdditionalInfo.setExtendedInfo(cn.hutool.core.convert.Convert.toStr(additionalJson));
                dbTalent.setTalentAdditionalInfo(talentAdditionalInfo);
            }
            dbTalent.setLastEditedTime(Instant.now());

            //sync to crm client contact
            talentCrmSyncService.updateClientContact(dbTalent.getId(), dbTalent);
            TalentV3 talent = talentRepository.saveAndFlush(dbTalent);

            //contacts
            updateTalentContact(dto, talent.getId());
            entityManager.flush();
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), StrUtil.format("Talent id:{} update failure, please manual processing", dto.getId()));
            log.error("[APN: Talent @{}] update Talent error, talent id : {}， error: {}", SecurityUtils.getUserId(), dto.getId(), JSONUtil.toJsonStr(e));
            throw e;
        }
        return findTalentById(dto.getId(), false);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public TalentDTOV3 updateForPro(Long id, TalentInfoInput input) {
        removeEnumsNull(input);
        StopWatch stopWatch = new StopWatch("updateForPro");
        stopWatch.start("[1] findById");
        // For pro update talent. Need update talent basic info, notes, and contacts.
        TalentDTOV3 update = talentInfoMapper.toTalentDTO(input);
        update.setId(id);
        Long talentId = update.getId();
        TalentV3 dbTalent = talentRepository.findById(talentId).orElseThrow(() -> new CustomParameterizedException("The talent to update dose not exist"));
        checkPermission(dbTalent);
        stopWatch.stop();
        stopWatch.start("[2] startCheckDuplicationTalent");
        //候选人查重
        try {
            log.info("startCheckDuplicationTalent");
            TalentV3 talentCheckDuplication = TalentV3.fromTalentDTO(update);
            TalentSimilarityDto talentSimilarityDto = new TalentSimilarityDto();
            talentSimilarityDto.setCurrentLocation(update.getCurrentLocation());
            List<SuspectedDuplications> talentDuplication = getSuspectededDuplicationsByContactAndSmilarity(talentCheckDuplication, update.getContacts(), talentSimilarityDto, talentId);
            if(CollectionUtils.isNotEmpty(talentDuplication)) {
                throw new WithDataException("Duplicate entry talent data.", cn.hutool.http.Status.HTTP_PRECON_FAILED, talentDuplication);
            }
        } catch (NotFoundException ignored) {
            throw new DuplicateException("Please contact customer service to resolve this issue.");
        }
        // set tenant id for contacts
        if (CollectionUtils.isNotEmpty(update.getContacts())) {
            update.getContacts().forEach(c -> {
                c.setTenantId(SecurityUtils.getTenantId());
                c.setTalentId(talentId);
            });
        }
        stopWatch.stop();
        stopWatch.start("[3] talentNoteSave");
        //  set user id for notes
        if (CollectionUtils.isNotEmpty(update.getNotes())) {
            List<TalentNote> notes = new ArrayList<>();
            for (TalentNote t : Convert.toList(TalentNote.class, update.getNotes())) {
                t.setUserId(SecurityUtils.getUserId());
                t.setTalentId(talentId);
                notes.add(t);
            }
            talentNoteRepository.saveAllAndFlush(notes);
        }
        stopWatch.stop();
        stopWatch.start("[4] updateTalentContact");
        // update contacts
        updateTalentContact(update, talentId);
        dbTalent.setLastEditedTime(Instant.now());
        stopWatch.stop();
        stopWatch.start("[5] copyBaseInfo");
        copyBaseInfo(update, dbTalent);
        try {
            stopWatch.stop();
            stopWatch.start("[6] updateClientContact");
            talentCrmSyncService.updateClientContact(talentId, dbTalent);
            stopWatch.stop();
            stopWatch.start("[7] talentSave");
            talentRepository.saveAndFlush(dbTalent);
        } catch (Exception e) {
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), StrUtil.format("Talent id:{} update failure, please manual processing", talentId));
            log.error("[APN: Talent @{}] update Talent error, talent id : {}， error: {}", SecurityUtils.getUserId(), talentId, JSONUtil.toJsonStr(e));
            throw e;
        }
        stopWatch.stop();
        stopWatch.start("[6] findByIdFromTalent");
        TalentDTOV3 ret = TalentDTOV3.fromTalent(talentRepository.findById(talentId).get());
        stopWatch.stop();
        log.info("[apn @{}] updateForPro time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        asyncGenerateResume(id, input);
        return ret;
    }

    private GenerateResumeDTO generateResume(TalentInfoInput input) {
        String dataMD5 = getTalentinfoInputUUID(input);
        String liePinPDFName = getLiePinPDFName(input);
        Resume resume = resumeRepository.findByDataMD5(dataMD5);
        if(resume != null) {
            GenerateResumeDTO ret = new GenerateResumeDTO();
            ret.setText(resume.getText());
            ret.setUuid(resume.getUuid());
            ret.setDataMD5(dataMD5);
            ret.setLiePinPDFName(liePinPDFName);
            return ret;
        }
        StringBuilder text = new StringBuilder();
        text.append("猎聘\n");
        MultipartFile generateResume = getGenerateResume(input, text);
        String uuid = null;
        try {
            uuid = getMD5FromByteArray(generateResume.getBytes());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        ResumeParseInfo resumeParseInfo = new ResumeParseInfo();
        resumeParseInfo.setFilename(liePinPDFName);
        resumeParseInfo.setUuid(uuid);
        resumeParseInfo.setContentType("application/pdf");
        resumeParseInfo.setPriority(1);
        parserService.putResumeParseInfoToRedis(resumeParseInfo);
        parserService.uploadDocument(generateResume, uuid, "resume");
        GenerateResumeDTO ret = new GenerateResumeDTO();
        ret.setText(text.toString());
        ret.setUuid(uuid);
        ret.setDataMD5(dataMD5);
        ret.setLiePinPDFName(liePinPDFName);
        return ret;
    }

    //判断是否生成过
    private String getTalentinfoInputUUID(TalentInfoInput input) {
        JSONObject object = new JSONObject();
        object.put("firstName", input.getFirstName());
        object.put("lastName", input.getLastName());
        object.put("fullName", input.getFullName());
        object.put("gender", input.getGender());
        object.put("zipCode", input.getZipCode());
        object.put("birthDate", input.getBirthDate());
        object.put("photoUrl", input.getPhotoUrl());
        object.put("currentLocation", input.getCurrentLocation());
        object.put("preference", input.getPreferences());
//        object.put("preferredLocations", input.getPreferredLocations());
//        object.put("preferredPayType", input.getPreferredPayType());
//        object.put("preferredSalaryRange", input.getPreferredSalaryRange());
//        object.put("preferredCurrency", input.getPreferredCurrency());
//        object.put("preferredPayTimes", input.getPreferredPayTimes());
//        object.put("preferredTitle", input.getPreferredTitle());
        object.put("skills", input.getSkills());
        object.put("currency", input.getCurrency());
        object.put("payTimes", input.getPayTimes());
        object.put("payType", input.getPayType());
        object.put("salaryRange", input.getSalaryRange());
        object.put("source", input.getSource());
        object.put("workAuthorization", input.getWorkAuthorization());
        object.put("jobFunctions", input.getJobFunctions());
        object.put("languages", input.getLanguages());
        object.put("industries", input.getIndustries());
        object.put("contacts", input.getContacts());
        object.put("experiences", input.getExperiences());
        object.put("educations", input.getEducations());
        object.put("projects", input.getProjects());
        object.put("selfEvaluation", input.getSelfEvaluation());

        String json = JSONUtil.toJsonStr(object);
        String md5 = DigestUtil.md5Hex(json);
        log.info("generateResume md5:{} || jsonObject: {} || input:{}", md5, json, JSONUtil.toJsonStr(input));
        return md5;
    }
    
    public static String getMD5FromByteArray(byte[] data) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] hash = md.digest(data);
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }

    private void saveResumeAndRelation(Long id, String uuid, String fileName, String dataMD5, String text) {
        Resume resume = resumeRepository.findByUuid(uuid);
        if(resume == null) {
            resume = new Resume();
            resume.setUuid(uuid);
            resume.setText(text);
            resume.setHasPortrait(false);
            resume.setNPages(0);
            resume.setDataMD5(dataMD5);
            resumeRepository.saveAndFlush(resume);
        }
        TalentResumeRelation talentResumeRelation = talentResumeRelationRepository.findByResumeIdAndTenantIdAndTalentId(resume.getId(), SecurityUtils.getTenantId(), id);
        if(talentResumeRelation != null) {
            talentResumeRelation.setStatus(CommonDataStatus.AVAILABLE);
            talentResumeRelationRepository.saveAndFlush(talentResumeRelation);
        } else {
            talentResumeRelation = new TalentResumeRelation();
            talentResumeRelation.setTalentId(id);
            talentResumeRelation.setResumeId(resume.getId());
            talentResumeRelation.setTenantId(SecurityUtils.getTenantId());
            talentResumeRelation.setFileName(fileName);
            talentResumeRelation.setSourceType(ResumeSourceType.LIEPIN);
            talentResumeRelation.setStatus(CommonDataStatus.AVAILABLE);
            talentResumeRelationRepository.saveAndFlush(talentResumeRelation);
        }
    }

    private MultipartFile getGenerateResume(TalentInfoInput input, StringBuilder text) {
        String userName = SecurityUtils.getUserName();
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()){
            generateLinpinPDFReport(input, outputStream, userName, text);
            return new ByteArrayResourceMultipartFile(outputStream.toByteArray(), "file", getLiePinPDFName(input), "application/pdf");
        } catch (Exception e) {
            log.error("Generate liepin PDF exception.", e);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_GETRECOMMENDATIONREPORT_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
    }

    private String getLiePinPDFName(TalentInfoInput input) {
        return "猎聘候选人-" + getTalentInfoInputFullName(input) + ".pdf";
    }

    private String getTalentInfoInputFullName(TalentInfoInput input) {
        String fullName = input.getFullName();
        if(fullName != null) {
            String trim = fullName.trim();
            if(StringUtils.isNotEmpty(trim)) {
                return trim;
            }
        }
        return CommonUtils.formatFullName(input.getFirstName(), input.getLastName());
    }


    public void generateLinpinPDFReport(TalentInfoInput input, ByteArrayOutputStream outputStream, String userName, StringBuilder text) throws Exception {
        TalentDTOV3 talentDTO = talentInfoMapper.toTalentDTO(input);
        talentDTO = sortTalentDTO(talentDTO);
        Document document = null;
        PdfWriter writer = null;
        try {
            document = new Document(PageSize.A4, 50, 50, 100, 50);
            writer = PdfWriter.getInstance(document, outputStream);
            writer.setPageEvent(new LiePinHeaderFooterEventHandler(userName));
            document.open();
            writeTalentBasicInfoPDF(document, writer, talentDTO, input, text);
            writePreferredMotivation(document, writer, input.getOriginPreferences(), text);
            writeExperience(document, writer, talentDTO.getExperiences(), text);
            writeProject(document, writer, talentDTO.getProjects(), text);
            writeEducation(document, writer, talentDTO.getEducations(), text);
            writeLanguage(document, writer, talentDTO.getLanguages(), text);
            text.append(talentDTO.getSelfEvaluation());
            writeSelfEvaluation(document, writer, talentDTO.getSelfEvaluation());

        } finally {
            if(writer != null) {
                writer.close();
            }
            if(document != null) {
                document.close();
            }
        }
    }

    private void writeSelfEvaluation(Document document, PdfWriter writer, String selfEvaluation) throws DocumentException {
        writeTitleLine(document, "自我评价");
        if(selfEvaluation == null) {
            Paragraph dateParagraph = new Paragraph("无", normalFont);
            dateParagraph.setSpacingBefore(10);
            dateParagraph.setIndentationLeft(5);
            document.add(dateParagraph);
        } else {
            Paragraph dateParagraph = new Paragraph(selfEvaluation, normalFont);
            dateParagraph.setSpacingBefore(10);
            dateParagraph.setIndentationLeft(5);
            document.add(dateParagraph);
        }
    }

    private void writeEducation(Document document, PdfWriter writer, List<TalentEducationDTO> educations, StringBuilder text) throws DocumentException {
        writeTitleLine(document, "教育经历");
        if(educations == null || educations.isEmpty()) {
            Paragraph dateParagraph = new Paragraph("无", boldFont);
            dateParagraph.setSpacingBefore(10);
            dateParagraph.setIndentationLeft(5);
            document.add(dateParagraph);
        } else {
            List<EnumDegree> allEnumDegree = enumCommonService.findAllEnumDegree();
            for (TalentEducationDTO edu : educations) {
                // Date range
                String dateRange = formatEducationDateRange(edu);
                text.append(dateRange);
                Paragraph dateParagraph = new Paragraph(dateRange, boldFont);
                dateParagraph.setSpacingBefore(10);
                dateParagraph.setIndentationLeft(5);
                document.add(dateParagraph);

                // School, major, degree
                StringBuilder eduInfo = new StringBuilder();
                eduInfo.append(edu.getCollegeName());
                if (StringUtils.isNotEmpty(edu.getMajorName())) {
                    eduInfo.append("    ").append(edu.getMajorName());
                }
                if (StringUtils.isNotEmpty(edu.getDegreeName())) {
                    eduInfo.append(" | ").append(edu.getDegreeName());
                } else {
                    if (StringUtils.isNotEmpty(edu.getDegreeLevel())) {
                        EnumDegree degree = allEnumDegree.stream().filter(p -> p.getName().equals(edu.getDegreeLevel())).findFirst().orElse(null);
                        if(degree != null) {
                            eduInfo.append(" | ").append(degree.getCnDisplay());
                        }
                    }
                }
                text.append(eduInfo);
                text.append("\n");

                Paragraph eduInfoParagraph = new Paragraph(eduInfo.toString(), boldFont);
                eduInfoParagraph.setIndentationLeft(5);
                document.add(eduInfoParagraph);
            }
        }
    }

    private String formatEducationDateRange(TalentEducationDTO edu) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy.MM");
        String start = edu.getStartDate() != null ? edu.getStartDate().format(formatter) : "";
        Boolean current = edu.getCurrent();
        if(current == null) {
            current = false;
        }
        String end = current ? "至今" : (edu.getEndDate() != null ? edu.getEndDate().format(formatter) : "");
        return start + "-" + end;
    }

    private void writeLanguage(Document document, PdfWriter writer, List<EnumRelationDTO> languages, StringBuilder text) throws DocumentException {
        writeTitleLine(document, "语言能力");
        if(languages == null || languages.isEmpty()) {
            Paragraph dateParagraph = new Paragraph("无", boldFont);
            dateParagraph.setSpacingBefore(10);
            dateParagraph.setIndentationLeft(5);
            document.add(dateParagraph);
        } else {
            Map<Long, EnumLanguage> allEnumLanguagesMap = enumCommonService.findAllEnumLanguagesMap();
            List<String> languageList = new ArrayList<>();
            for (EnumRelationDTO language : languages) {
                EnumLanguage enumLanguage = allEnumLanguagesMap.get(Long.valueOf(language.getEnumId()));
                if(enumLanguage != null) {
                    languageList.add(enumLanguage.getCnDisplay());
                }
            }
            if(!languageList.isEmpty()) {
                String join = String.join(", ", languageList);
                text.append(join);
                text.append("\n");
                Paragraph dateParagraph = new Paragraph(join, normalFont);
                dateParagraph.setSpacingBefore(10);
                dateParagraph.setIndentationLeft(5);
                document.add(dateParagraph);
            }

        }
    }

    private void writeProject(Document document, PdfWriter writer, List<TalentProjectDTO> projects, StringBuilder text) throws DocumentException {
        writeTitleLine(document, "项目经历");
        if(projects == null || projects.isEmpty()) {
            Paragraph dateParagraph = new Paragraph("无", boldFont);
            dateParagraph.setSpacingBefore(10);
            dateParagraph.setIndentationLeft(5);
            document.add(dateParagraph);
        } else {
            for (TalentProjectDTO project : projects) {
                // Date range
                String dateRange = formatProjectDateRange(project);
                text.append(dateRange);
                Paragraph dateParagraph = new Paragraph(dateRange, boldFont);
                dateParagraph.setSpacingBefore(10);
                dateParagraph.setIndentationLeft(5);
                document.add(dateParagraph);

                Paragraph projectTitleParagraph = new Paragraph(project.getProjectName(), boldFont);
                text.append(project.getProjectName());
                text.append("\n");
                projectTitleParagraph.setIndentationLeft(5);
                document.add(projectTitleParagraph);

                // Additional details
                PdfPTable detailsTable = new PdfPTable(4);
                detailsTable.setWidthPercentage(100);
                detailsTable.setSpacingBefore(5);
                // 设置列宽
                float[] columnWidths = {0.5f, 1.5f, 0.5f, 1.5f};
                detailsTable.setWidths(columnWidths);

                addTableCell(detailsTable, "项⽬职务:", project.getTitle());
                addTableCell(detailsTable, "", "");
                addTableCell(detailsTable, "所在公司:", project.getCompanyName());
                addTableCell(detailsTable, "", "");

                document.add(detailsTable);
                // Project description
                addLabeledParagraph(document, "项目描述:", project.getDescription());
            }
        }
    }

    private void addLabeledParagraph(Document document, String label, String content) throws DocumentException {
        Paragraph labelParagraph = new Paragraph(label, normalFont);
        labelParagraph.setSpacingBefore(10);
        labelParagraph.setIndentationLeft(5);
        document.add(labelParagraph);

        Paragraph contentParagraph = new Paragraph(content, normalFont);
        contentParagraph.setIndentationLeft(5);
        document.add(contentParagraph);
    }


    private String formatProjectDateRange(TalentProjectDTO project) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy.MM");
        String start = project.getStartDate() != null ? project.getStartDate().format(formatter) : "";
        Boolean current = project.getCurrent();
        if(current == null) {
            current = false;
        }
        String end = current ? "至今" : (project.getEndDate() != null ? project.getEndDate().format(formatter) : "");
        String duration = calculateDuration(project.getStartDate(), project.getEndDate(), project.getCurrent());
        return start + "-" + end + (duration.isEmpty() ? "" : " (" + duration + ")");
    }

    private String calculateDuration(LocalDate startDate, LocalDate endDate, Boolean current) {
        if(Boolean.TRUE.equals(current)) {
            return "";
        }
        if (startDate == null || endDate == null) {
            return "";
        }

        Period period = Period.between(startDate, endDate);
        int years = period.getYears();
        int months = period.getMonths();

        if (years > 0) {
            return years + "年" + (months > 0 ? months + "个月" : "");
        } else {
            return months > 0 ? months + "个月" : "";
        }
    }

    private void writeExperience(Document document, PdfWriter writer, List<TalentExperienceDTO> experiences, StringBuilder text) throws DocumentException {
        writeTitleLine(document, "工作经历");
        if(experiences == null || experiences.isEmpty()) {
            Paragraph dateParagraph = new Paragraph("无", boldFont);
            dateParagraph.setSpacingBefore(10);
            dateParagraph.setIndentationLeft(5);
            document.add(dateParagraph);
        } else {
            for (TalentExperienceDTO exp : experiences) {
                // Date range
                String dateRange = formatDateRange(exp);
                text.append(dateRange);
                Paragraph dateParagraph = new Paragraph(dateRange, boldFont);
                dateParagraph.setSpacingBefore(10);
                dateParagraph.setIndentationLeft(5);
                document.add(dateParagraph);

                // Company name and title
                String companyTitle = exp.getCompanyName() + " | " + exp.getTitle();
                text.append(companyTitle);
                text.append("\n");
                Paragraph companyTitleParagraph = new Paragraph(companyTitle, boldFont);
                companyTitleParagraph.setIndentationLeft(5);
                document.add(companyTitleParagraph);

                // Additional details
                PdfPTable detailsTable = new PdfPTable(4);
                detailsTable.setWidthPercentage(100);
                detailsTable.setSpacingBefore(5);
                // 设置列宽
                float[] columnWidths = {0.5f, 1.5f, 0.5f, 1.5f};
                detailsTable.setWidths(columnWidths);

                addTableCell(detailsTable, "所属部门:", exp.getDepartment());
                addTableCell(detailsTable, "汇报对象:", exp.getReportTo());
                addTableCell(detailsTable, "下属人数:", exp.getNumberOfSubordinates() != null ? exp.getNumberOfSubordinates().toString() : "");
                addTableCell(detailsTable, "工作地点:", exp.getLocation());
                addTableCell(detailsTable, "月薪:", formatSalary(exp));
                addTableCell(detailsTable, "", "");

                document.add(detailsTable);

                // Job responsibilities
                Paragraph responsibilitiesLabel = new Paragraph("职责业绩:", normalFont);
                responsibilitiesLabel.setSpacingBefore(10);
                responsibilitiesLabel.setIndentationLeft(5);
                document.add(responsibilitiesLabel);

                Paragraph descParagraph = new Paragraph(exp.getDescription(), normalFont);
                descParagraph.setIndentationLeft(5);
                document.add(descParagraph);
            }
        }
    }

    private void addTableCell(PdfPTable table, String label, String value) {
        PdfPCell labelCell = new PdfPCell(new Phrase(label, normalFont));
        labelCell.setBorder(Rectangle.NO_BORDER);
        labelCell.setPaddingLeft(5);
        table.addCell(labelCell);

        PdfPCell valueCell = new PdfPCell(new Phrase(value, normalFont));
        valueCell.setBorder(Rectangle.NO_BORDER);
        table.addCell(valueCell);
    }

    private PdfPCell createEmptyCell() {
        PdfPCell cell = new PdfPCell();
        cell.setBorder(Rectangle.NO_BORDER);
        return cell;
    }

    private void addTableRow(PdfPTable table, String label, String value) {
        PdfPCell labelCell = new PdfPCell(new Phrase(label, boldFont));
        labelCell.setBorder(Rectangle.NO_BORDER);
        table.addCell(labelCell);

        PdfPCell valueCell = new PdfPCell(new Phrase(value, normalFont));
        valueCell.setBorder(Rectangle.NO_BORDER);
        table.addCell(valueCell);
    }

    private String formatDateRange(TalentExperienceDTO exp) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy.MM");
        String start = exp.getStartDate() != null ? exp.getStartDate().format(formatter) : "";
        Boolean current = exp.getCurrent();
        if(current == null) {
            current = false;
        }
        String end = current ? "至今" : (exp.getEndDate() != null ? exp.getEndDate().format(formatter) : "");
        String duration = calculateDuration(exp.getStartDate(), exp.getEndDate(), exp.getCurrent());
        return start + "-" + end + (duration.isEmpty() ? "" : " (" + duration + ")");
    }


    private String formatSalary(TalentExperienceDTO exp) {
        Double payTime = exp.getPayTimes();
        RangeDTO rangeDTO = exp.getSalaryRange();
        com.altomni.apn.common.domain.enumeration.RateUnitType rateUnitType = exp.getPayType();
        if(payTime == null && com.altomni.apn.common.domain.enumeration.RateUnitType.MONTHLY.equals(rateUnitType)) {
            payTime = 12.0;
        }
        if(payTime != null && rangeDTO != null && com.altomni.apn.common.domain.enumeration.RateUnitType.MONTHLY.equals(rateUnitType)) {
            String salaryRangeStr = rangeDTO.getGte().toString();
            if(!Objects.equals(rangeDTO.getGte(), rangeDTO.getLte())) {
                salaryRangeStr = rangeDTO.getGte() + "-" + rangeDTO.getLte();
            }
            return String.format("%s ×%s薪",
                    salaryRangeStr,
                    payTime.intValue());
        }
        return "";
    }

    private void writePreferredMotivation(Document document, PdfWriter writer, List<TalentPreference> preferences, StringBuilder text) throws DocumentException {
        writeTitleLine(document, "求职意向");
        if(preferences == null || preferences.isEmpty()) {
            Paragraph dateParagraph = new Paragraph("无", boldFont);
            dateParagraph.setSpacingBefore(10);
            dateParagraph.setIndentationLeft(5);
            document.add(dateParagraph);
        } else {
            for (TalentPreference preference : preferences) {
                String title = preference.getTitle();
                List<LocationDTO> location = preference.getLocations();
                com.altomni.apn.common.domain.enumeration.RateUnitType rateUnitType = preference.getPayType();
                Double payTime = preference.getPayTimes() != null ? preference.getPayTimes() : 12.0;
                RangeDTO rangeDTO = preference.getSalaryRange();

                List<String> infoItems = new ArrayList<>();
                if(StringUtils.isNotEmpty(title)) {
                    infoItems.add(title);
                }
                String industryList = getIndustryList(preference.getIndustries());
                if(StringUtils.isNotEmpty(industryList)) {
                    infoItems.add(industryList);
                }
                if(location != null && !location.isEmpty()) {
                    infoItems.add(getLocationTogether(location));
                }
                if(payTime != null && rangeDTO != null && com.altomni.apn.common.domain.enumeration.RateUnitType.MONTHLY.equals(rateUnitType) && rangeDTO.getGte() != null) {
                    String salaryRangeStr = rangeDTO.getGte().toString();
                    if(!Objects.equals(rangeDTO.getGte(), rangeDTO.getLte()) && rangeDTO.getLte() != null) {
                        salaryRangeStr = rangeDTO.getGte() + "-" + rangeDTO.getLte();
                    }
                    infoItems.add(String.format("%s ×%s薪",
                            salaryRangeStr,
                            payTime.intValue()));
                }

                String motivation = String.join(" | ", infoItems);
                text.append(motivation);
                text.append("\n");
                Paragraph elements = new Paragraph(motivation, boldFont);
                elements.setSpacingBefore(10);
                elements.setIndentationLeft(5);
                document.add(elements);
            }
        }
    }

    private String getIndustryList(List<Integer> industries) {
        if(industries == null || industries.isEmpty()) {
            return "全部行业";
        }
        List<EnumIndustry> allEnumIndustry = enumCommonService.findAllEnumIndustry();
        List<String> result = new ArrayList<>();
        for(int i = 0; i < industries.size(); i++) {
            Long dbValue = Long.valueOf(industries.get(i));
            EnumIndustry enumIndustry = getEnumIndustry(allEnumIndustry, dbValue);
            if(enumIndustry == null) {
                continue;
            }
            result.add(enumIndustry.getCnDisplay());
        }
        return Joiner.on(",").skipNulls().join(result);
    }

    private EnumIndustry getEnumIndustry(List<EnumIndustry> allEnumIndustry, Long dbValue) {
        for(EnumIndustry enumIndustry : allEnumIndustry) {
            if(Objects.equals(enumIndustry.getId(), dbValue)) {
                return enumIndustry;
            }
        }
        return null;
    }

    private String getLocationTogether(List<LocationDTO> locations) {
        if (locations == null || locations.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();

        for (int i = 0; i < locations.size(); i++) {
            LocationDTO location = locations.get(i);
            if (location != null && location.getLocation() != null) {
                result.append(location.getLocation());

                // 如果不是最后一个元素，添加斜杠分隔符
                if (i < locations.size() - 1) {
                    result.append("/");
                }
            }
        }

        return result.toString();
    }



    private void writeTalentBasicInfoPDF(Document document, PdfWriter writer, TalentDTOV3 talentInfo, TalentInfoInput input, StringBuilder text) throws Exception {
        try {
            PdfPTable table = new PdfPTable(2);
            table.setWidthPercentage(100);
            float[] columnWidths = {0.2f, 0.8f};
            table.setWidths(columnWidths);

            // 添加头像
            PdfPCell imageCell = new PdfPCell();
            Image avatar = null;
            if (talentInfo.getPhotoUrl() != null && StringUtils.isNotEmpty(talentInfo.getPhotoUrl())) {
                try {
                    avatar = Image.getInstance(talentInfo.getPhotoUrl());
                } catch (Exception e) {
                    // 如果获取 URL 头像失败，记录日志并忽略错误
                    log.error("Failed to load photo from URL: {}", talentInfo.getPhotoUrl(), e);
                }
            }
            if(avatar == null) {
                ClassPathResource resource = new ClassPathResource(ReportTemplateConstants.DEFAULT_PHOTO);
                avatar = Image.getInstance(resource.getInputStream().readAllBytes());
            }

            avatar.scaleToFit(80, 80);
            imageCell.addElement(avatar);
            imageCell.setBorder(Rectangle.NO_BORDER);
            imageCell.setRowspan(5);
            table.addCell(imageCell);


            // 添加姓名和状态
            PdfPCell nameCell = new PdfPCell();
            if (talentInfo.getFullName() != null) {
                String fullname = getTalentInfoInputFullName(input);
                text.append(fullname);
                text.append("\n");
                Paragraph namePara = new Paragraph(fullname, boldFont);
                nameCell.addElement(namePara);
            }
            nameCell.setBorder(Rectangle.NO_BORDER);
            table.addCell(nameCell);

            // 添加基本信息
            PdfPCell infoCell = new PdfPCell();
            String basicinfo = getBasicinfo(talentInfo);
            text.append(basicinfo);
            text.append("\n");
            Paragraph infoPara = new Paragraph(basicinfo, normalFont);
            infoCell.addElement(infoPara);

            infoCell.setBorder(Rectangle.NO_BORDER);
            table.addCell(infoCell);

            // 添加职位和公司信息
            PdfPCell jobCell = new PdfPCell();
            String companyInfo = getCompanyInfo(talentInfo);
            text.append(companyInfo);
            text.append("\n");
            addParagraphIfNotEmpty(jobCell, companyInfo, normalFont);
            jobCell.setBorder(Rectangle.NO_BORDER);
            table.addCell(jobCell);

            // 添加联系方式
            PdfPCell contactCell = new PdfPCell();
            String contactInfo = getContactInfo(talentInfo, ContactType.PHONE);
            text.append(contactInfo);
            text.append("\n");
            addParagraphIfNotEmpty(contactCell, contactInfo, normalFont);
            contactCell.setBorder(Rectangle.NO_BORDER);
            table.addCell(contactCell);

            PdfPCell emailCell = new PdfPCell();
            String email = getContactInfo(talentInfo, ContactType.EMAIL);
            text.append(email);
            text.append("\n");
            addParagraphIfNotEmpty(emailCell, email, normalFont);
            emailCell.setBorder(Rectangle.NO_BORDER);
            table.addCell(emailCell);

            document.add(table);


        } catch (Exception e) {
            log.error("生成人才基本信息PDF时发生异常", e);
            throw e;
        }
    }

    private String getContactInfo(TalentDTOV3 talentInfo, ContactType contactType) {
        List<TalentContactDTO> contacts = talentInfo.getContacts();
        if(contacts != null && !contacts.isEmpty()) {
            List<String> phones = contacts.stream().filter(p -> contactType.equals(p.getType())).map(TalentContactDTO::getContact).collect(Collectors.toList());
//            List<String> emails = contacts.stream().filter(p -> ContactType.EMAIL.equals(p.getType())).map(TalentContactDTO::getContact).collect(Collectors.toList());
            StringBuilder sb = new StringBuilder();
            if(!phones.isEmpty()) {
                sb.append(contactType.equals(ContactType.PHONE) ? "⼿机：" : "邮箱：");
                sb.append(String.join(",", phones));
            }
//            if(!emails.isEmpty()) {
//                if(!sb.isEmpty()) {
//                    sb.append("    ");
//                }
//                sb.append("邮箱：");
//                sb.append(String.join(",", emails));
//            }
            return sb.toString();
        }
        return null;
    }

    private String getCompanyInfo(TalentDTOV3 talentInfo) {
        List<String> infoItems = new ArrayList<>();
        List<TalentExperienceDTO> experiences = talentInfo.getExperiences();
        List<EnumRelationDTO> industries = talentInfo.getIndustries();
        if(experiences != null && !experiences.isEmpty()) {
            TalentExperienceDTO experienceDTO = experiences.get(0);
            String companyName = experienceDTO.getCompanyName();
            String title = experienceDTO.getTitle();
            if(StringUtils.isNotEmpty(title)) {
                infoItems.add(title);
            }
            if(StringUtils.isNotEmpty(companyName)) {
                infoItems.add(companyName);
            }
        }
        if(industries != null && !industries.isEmpty()) {
            List<String> industryIds = industries.stream().map(EnumRelationDTO::getEnumId).collect(Collectors.toList());
            if(!industryIds.isEmpty()) {
                List<EnumIndustry> allEnumIndustry = enumCommonService.findAllEnumIndustry();
                List<String> industryNames = allEnumIndustry.stream().filter(p -> industryIds.contains(p.getId().toString())).map(EnumIndustry::getCnDisplay).collect(Collectors.toList());
                infoItems.add(String.join(",", industryNames));
            }
        }
        return String.join(" | ", infoItems);
    }

    private void addParagraphIfNotEmpty(PdfPCell cell, String text, Font font) {
        if (text != null && !text.isEmpty()) {
            cell.addElement(new Paragraph(text, font));
        }
    }

    public long calculateAge(String birthDateString) {
        return DateUtil.betweenYear(DateUtil.parseDate(birthDateString), DateUtil.date(), false);
    }

    public static boolean isValidDate(String birthday) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        try {
            LocalDate.parse(birthday, formatter);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    private String getBasicinfo(TalentDTOV3 talentInfo) {
        List<String> infoItems = new ArrayList<>();

        if (StringUtils.isNotEmpty(talentInfo.getGender())) {
            List<EnumGenderIdentity> allEnumGender = enumCommonService.findAllEnumGender();
            EnumGenderIdentity gender = allEnumGender.stream().filter(p -> p.getEnumName().equals(talentInfo.getGender())).findFirst().orElse(null);
            if(gender != null) {
                infoItems.add(gender.getCnDisplay());
            }
        }
        if (StringUtils.isNotEmpty(talentInfo.getBirthDate()) && isValidDate(talentInfo.getBirthDate())) {
            infoItems.add(calculateAge(talentInfo.getBirthDate()) + "岁");
        }

        if (talentInfo.getCurrentLocation() != null) {
            String location = talentInfo.getCurrentLocation().getLocation();
            if(StringUtils.isNotEmpty(location)) {
                infoItems.add(location);
            }
        }
        if (talentInfo.getEducations() != null && !talentInfo.getEducations().isEmpty()) {
            TalentEducationDTO educationDTO = talentInfo.getEducations().get(0);
            if(StringUtils.isNotEmpty(educationDTO.getDegreeName())) {
                infoItems.add(educationDTO.getDegreeName());
            } else {
                String degreeLevel = educationDTO.getDegreeLevel();
                List<EnumDegree> allEnumDegree = enumCommonService.findAllEnumDegree();
                EnumDegree degree = allEnumDegree.stream().filter(p -> p.getName().equals(degreeLevel)).findFirst().orElse(null);
                if(degree != null) {
                    infoItems.add(degree.getCnDisplay());
                }
            }
        }

        com.altomni.apn.common.domain.enumeration.RateUnitType payType = talentInfo.getPayType();
        RangeDTO salaryRange = talentInfo.getSalaryRange();
        Double payTimes = talentInfo.getPayTimes();
        if(payTimes == null && com.altomni.apn.common.domain.enumeration.RateUnitType.MONTHLY.equals(payType)) {
            payTimes = 12.0;
        }
        if(salaryRange != null && payTimes != null && com.altomni.apn.common.domain.enumeration.RateUnitType.MONTHLY.equals(payType)) {
            String salaryRangeStr = salaryRange.getGte().toString();
            if(!Objects.equals(salaryRange.getGte(), salaryRange.getLte())) {
                salaryRangeStr = salaryRange.getGte() + "-" + salaryRange.getLte();
            }
            String total = salaryRange.getGte().multiply(new BigDecimal(payTimes)).divide(new BigDecimal(10000), 1, RoundingMode.DOWN).toString();
            if(!Objects.equals(salaryRange.getGte(), salaryRange.getLte())) {
                total = salaryRange.getGte().multiply(new BigDecimal(payTimes)).divide(new BigDecimal(10000), 1, RoundingMode.DOWN) + "-" + salaryRange.getLte().multiply(new BigDecimal(payTimes)).divide(new BigDecimal(10000));
            }
            infoItems.add(String.format("当前薪资: %s万 (%s元/月 ×%s个月)",
                    total,
                    salaryRangeStr,
                    payTimes.intValue()));
        }

        return String.join(" | ", infoItems);
    }

    private boolean existIdenticalGenerateResume(Long id, String dataMD5, String liePinPDFName) {
        if(dataMD5 == null) {
            return true;
        }
        Resume resume = resumeRepository.findByDataMD5(dataMD5);
        if(resume != null) {
            TalentResumeRelation talentResumeRelation = talentResumeRelationRepository.findByResumeIdAndTenantIdAndTalentId(resume.getId(), SecurityUtils.getTenantId(), id);
            if(talentResumeRelation != null) {
                talentResumeRelation.setStatus(CommonDataStatus.AVAILABLE);
                talentResumeRelationRepository.save(talentResumeRelation);
            } else {
                talentResumeRelation = new TalentResumeRelation();
                talentResumeRelation.setTalentId(id);
                talentResumeRelation.setResumeId(resume.getId());
                talentResumeRelation.setTenantId(SecurityUtils.getTenantId());
                talentResumeRelation.setFileName(liePinPDFName);
                talentResumeRelation.setSourceType(ResumeSourceType.LIEPIN);
                talentResumeRelation.setStatus(CommonDataStatus.AVAILABLE);
                talentResumeRelationRepository.save(talentResumeRelation);
            }
            return true;
        }
        return false;
    }

    private void copyBaseInfo(TalentDTOV3 update, TalentV3 dbTalent) {
        if(StringUtils.isNotEmpty(update.getFullName())) {
            dbTalent.setFullName(update.getFullName());
            dbTalent.setFirstName(null);
            dbTalent.setLastName(null);
        }
        String firstName = update.getFirstName();
        if(firstName != null && StringUtils.isNotEmpty(firstName.trim())) {
            dbTalent.setFirstName(firstName);
        }
        String lastName = update.getLastName();
        if(lastName != null && StringUtils.isNotEmpty(lastName.trim())) {
            dbTalent.setLastName(lastName);
        }
        if(StringUtils.isNotEmpty(dbTalent.getFirstName()) && StringUtils.isNotEmpty(dbTalent.getLastName())) {
            dbTalent.setFullName();
        }
        if(StringUtils.isNotEmpty(update.getPhotoUrl())) {
            dbTalent.setPhotoUrl(update.getPhotoUrl());
        }
        if(update.getGender() != null || update.getBirthDate() != null) {
            TalentAdditionalInfo talentAdditionalInfo = dbTalent.getTalentAdditionalInfo();
            if (talentAdditionalInfo != null) {
                String extendedInfo = talentAdditionalInfo.getExtendedInfo();
                JSONObject jsonObject = JSONUtil.parseObj(extendedInfo);
                //设置gender
                if (update.getGender() != null) {
                    List<EnumGenderIdentity> enumGenderList = enumCommonService.findAllEnumGender();
                    Optional<EnumGenderIdentity> findEnumGender = enumGenderList.stream().filter(p -> p.getEnumName().equalsIgnoreCase(update.getGender())).findFirst();
                    findEnumGender.ifPresent(p -> {
                        jsonObject.put("gender", p.getName());
                    });
                }
                //设置birthDate
                if (update.getBirthDate() != null) {
                    jsonObject.put("birthDate", update.getBirthDate());
                }
                talentAdditionalInfo.setExtendedInfo(JSONUtil.toJsonStr(jsonObject));
            }
        }
    }

    @Override
    public GetTalentDTO getTalentInfo(Long id) {
        if(!hasTalentViewAuthority(id)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_NO_PERMISSION_VISIT_CLIENT_CONTACT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        StopWatch stopWatch = new StopWatch("getTalentInfo");
        stopWatch.start("[1] findTalentById");
        TalentDTOV3 talentV3 = findTalentById(id, true);
        if(Objects.isNull(talentV3)) {
            return null;
        }
        GetTalentDTO ret = new GetTalentDTO();
        BeanUtil.copyProperties(talentV3, ret);
        stopWatch.stop();
        stopWatch.start("[2] getTalentIsClientContract");
        //追加返回公司联系人以及所属的公司
        List<SalesLeadClientContact> clientContacts = getTalentIsClientContract(id);
        if(CollUtil.isNotEmpty(clientContacts)) {
            Optional<SalesLeadClientContact> optContact = clientContacts.stream().filter(SalesLeadClientContact::isActive).findFirst();
            if (optContact.isPresent()) {
                ret.setClientContactCompanyId(optContact.get().getCompanyId());
                ret.setIsKeyContact(optContact.get().getIsKeyContact());
            }
            //设置客户联系人所属的公司
            ret.setCompanyAffiliations(clientContacts.stream()
                    .map(contact -> new ClientContactDTO(contact.getCompanyId(), contact.isActive()))
                    .collect(Collectors.toList()));
        }
        stopWatch.stop();
        stopWatch.start("[3] suppleNotesUserInfo");
        suppleNotesUserInfo(ret.getNotes());
        stopWatch.stop();
        stopWatch.start("[4] auditTalentDetailRetrievingRecord");
        if (BooleanUtil.isTrue(ret.getConfidentialTalentViewAble())){
            this.auditTalentDetailRetrievingRecord(id);
        }
        stopWatch.stop();
        stopWatch.start("[5] setPersonalizationConfig");
        ret.setPersonalizationConfig(userService.getPersonalizationConfig().getBody());
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            this.countContactRetrieving(id, talentV3.getFullName());
        });
        stopWatch.stop();
        log.info("[apn @{}] getTalentInfo time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        setEnrichRemindFlag(ret);
        return ret;
    }

    private void setEnrichRemindFlag(GetTalentDTO ret) {
        List<TalentNoteDTO> notes = ret.getNotes();
        if (notes == null) {
            ret.setEnrichRemind(false);
        } else {
            boolean need = notes.stream().anyMatch(note -> {
                if (!TalentNoteType.CALL_CANDIDATE_VOICE_MESSAGE.equals(note.getNoteType())) {
                    if (StringUtils.isNotEmpty(note.getParsedResult()) && StringUtils.isEmpty(note.getEnrichResult()) && SecurityUtils.getUserId().equals(note.getUserId())) {
                        return true;
                    }
                }
                return false;
            });
            ret.setEnrichRemind(need);
        }

    }

    private void buildAndSendMessage(Long tenantId, Map<String, String> tenantIdToTenantName, String user, Set<String> contacts){
        JSONObject msg = new JSONObject();
        // set title
        msg.put("title", String.format("WARNING (Tenant: %d-%s)", tenantId, tenantIdToTenantName.get(String.valueOf(tenantId))));
        cn.hutool.json.JSONArray content = new cn.hutool.json.JSONArray();
        //set sub title
        cn.hutool.json.JSONArray subTitle = new cn.hutool.json.JSONArray();
        JSONObject subContent = new JSONObject();
        subContent.put("tag", "text");
        subContent.put("text", String.format("%s viewed %d contacts over the past 24 hours!", user, contacts.size()));
        subTitle.add(subContent);
        content.add(subTitle);
        // set pre line
        cn.hutool.json.JSONArray preLine = new cn.hutool.json.JSONArray();
        JSONObject preLineContent = new JSONObject();
        preLineContent.put("tag", "text");
        preLineContent.put("text", "------------------------------------------------------------------------");
        preLine.add(preLineContent);
        content.add(preLine);
        // set content details
        for (String contactIdName : contacts) {
            cn.hutool.json.JSONArray row = new cn.hutool.json.JSONArray();
            JSONObject detail = new JSONObject();
            detail.put("tag", "a");
            detail.put("text", contactIdName);
            detail.put("href", String.format(applicationProperties.getMonitorContactUrl(), contactIdName.split("-")[0]));
            row.add(detail);
            content.add(row);
        }
        msg.put("content", content);
        NotificationUtils.sendRichTextMessage(applicationProperties.getMonitorWebhookKey(), applicationProperties.getMonitorWebhookUrl(), msg);
    }

    private void countContactRetrieving(Long talentId, String contactName){
        log.info("countContactRetrieving");
        Set<Long> crmContactIds = talentRepository.findCrmContactIds(talentId);
        if (CollectionUtils.isEmpty(crmContactIds)){
            return;
        }
        Long tenantId = SecurityUtils.getTenantId();
        String user = String.format("%s(%d)", SecurityUtils.getEmail(), SecurityUtils.getUserId());
        String key = String.format(com.altomni.apn.common.config.constants.RedisConstants.DATA_KEY_MONITOR_CONTACT, tenantId, user);
        Set<String> newContacts = crmContactIds.stream().map(crmContactId -> crmContactId + "-" + contactName).collect(Collectors.toSet());
        if (commonRedisService.exists(key)){
            Set<String> contacts = commonRedisService.smembers(key);
            if (contacts.containsAll(newContacts)){
                return;
            }
            contacts.addAll(newContacts);
            commonRedisService.sadd(key, newContacts, null);
            long totalContacts = contacts.size();
            Map<String, String> tenantIdToTenantName = Arrays.stream(applicationProperties.getMonitorContactForTenants()
                            .split(";"))
                    .collect(Collectors.toMap(v -> v.split("-")[0], v -> v.split("-")[1]));
            if (totalContacts >= applicationProperties.getMonitorContactThreshold()
                    && applicationProperties.isMonitorEnabled() && tenantIdToTenantName.containsKey(String.valueOf(tenantId))){
                // send to lark
                this.buildAndSendMessage(tenantId, tenantIdToTenantName, user, contacts);
            }
        }else {
            commonRedisService.sadd(key, newContacts, com.altomni.apn.common.config.constants.RedisConstants.EXPIRE_IN_1_DAY);
        }
    }

    /**
     * Send to Statistic Service
     */
    private void auditTalentDetailRetrievingRecord(Long talentId){
        Optional<User> userOptional = userRepository.findById(SecurityUtils.getUserId());
        if (userOptional.isPresent()){
            User user = userOptional.get();
            TalentDetailRetrievingRecordDTO talentDetailRetrievingRecordDTO = new TalentDetailRetrievingRecordDTO();
            talentDetailRetrievingRecordDTO.setTalentId(talentId)
                    .setUserId(user.getId())
                    .setUserEmail(user.getEmail())
                    .setUsername(user.getUsername())
                    .setFullname(user.getFirstName() + " " + user.getLastName())
                    .setTenantId(SecurityUtils.getTenantId())
                    .setRetrievingTime(Instant.now());
            httpService.asyncPost(applicationProperties.getTalentDetailRetrievingAuditApi(), JsonUtil.toJson(talentDetailRetrievingRecordDTO));
        }
    }

    @Override
    public String searchTalentDetailRetrievingRecord(TalentDetailRetrievingRecordSearchDTO talentDetailRetrievingRecordSearchDTO, Pageable pageable) throws IOException {
        String pageParameter = String.format("?size=%d&page=%d&sort=%s", pageable.getPageSize(), pageable.getPageNumber(), pageable.getSort().toString().replaceAll(": ", ","));
        return httpService.post(applicationProperties.getSearchTalentDetailRetrievingAuditApi() + pageParameter, JsonUtil.toJson(talentDetailRetrievingRecordSearchDTO)).getBody();
    }

    private void suppleNotesUserInfo(List<TalentNoteDTO> notes) {
        if(notes == null) {
            return;
        }
        Map<Long, SimpleUser> simpleUsers = getSimpleUsers(notes);
        notes.forEach(n -> {
            n.setCreatedUser(simpleUsers.get(extractUserId(n.getCreatedBy())));
            if(n.getLastModifiedBy() != null) n.setLastModifiedUser(simpleUsers.get(extractUserId(n.getLastModifiedBy())));
        });

    }

    private List<SalesLeadClientContact> getTalentIsClientContract(Long id) {
        ResponseEntity<List<SalesLeadClientContact>> listResponseEntity = companyService.findClientContactByTalentId(id);
        if (listResponseEntity == null || !listResponseEntity.getStatusCode().equals(HttpStatus.OK)) {
            log.error("companyService queryCompanyByTalent request error");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_GETTALENTISCLIENTCONTACT_LISTRESPONSEENTITYNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        return listResponseEntity.getBody();
    }

    public List<TalentDTOV3> findAllWithEntity(List<Long> ids) {
        return findTalentByIds(ids);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TalentDTOV3> getTalentsByContacts(TalentContactSearchVM talentContactSearchVM) {
        //At least one entry of the contact information is not null
        if (talentContactSearchVM.getContacts().stream()
                .noneMatch(c -> ContactType.EMAIL.equals(c.getType()) || ContactType.PHONE.equals(c.getType()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_GETTALENTSBYCONTACTS_CONTACTINFONULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
//        List<Long> talentIds = new ArrayList<>(talentContactService.getTalentIdsByContacts(talentContactSearchVM.getIgnoreTalentId(), talentContactSearchVM.getContacts()));
        List<Long> talentIds = new ArrayList<>(talentContactService.getTalentIdsByContactsIgnoreWrongContact(talentContactSearchVM.getIgnoreTalentId(), talentContactSearchVM.getContacts()));
        return findAllWithEntity(talentIds);
    }

    private void checkPermission(TalentV3 talent) {
        if (SecurityUtils.isSystemAdmin()) {
            return;
        }
        if (!SecurityUtils.isCurrentTenant(talent.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }

        if (talent.getTenantId().intValue() == Constants.INDIVIDUAL_TENANT_ID && !SecurityUtils.isCreatedByCurrentUser(talent.getCreatedBy())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
    }

    @Override
    @Transactional(readOnly = true)
    public String getTalentName(Long talentId) {
        return talentRepository.findTalentFullName(talentId);
    }


    @Override
    public List<ResignUserReportTalentDTO> findTalentsByIds(List<Long> talentIds) {
        List<TalentV3> result = talentRepository.findAllByIdIn(talentIds);
        List<ResignUserReportTalentDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {
            result.stream().forEach(t -> {
                ResignUserReportTalentDTO dto = new ResignUserReportTalentDTO();
                ServiceUtils.myCopyProperties(t, dto);
                com.alibaba.fastjson.JSONObject extendedInfo = JSON.parseObject(t.getTalentExtendedInfo(), com.alibaba.fastjson.JSONObject.class);
                JSONArray ja = extendedInfo.getJSONArray("currentLocation");
                if (ObjectUtil.isNotEmpty(ja)) {
                    dto.setCurrentLocation(ja.toJavaObject(LocationDTO.class));
                }
                if(t.getJobFunctions() != null) {
                    dto.setJobFunctions(t.getJobFunctions().stream().map(c -> String.valueOf(c.getEnumId())).toList());
                }
                dto.setIndustries(enumIndustryService.getIndustriesUINameByIds(JSON.toJSONString(EnumRelationDTO.convert(t.getIndustries()))));
                List<String> languagesList = enumLanguageService.getLanguagesUINameByIds(JSON.toJSONString(EnumRelationDTO.convert(t.getLanguages())));
                if (ObjectUtil.isNotEmpty(languagesList)) {
                    List<TalentLanguageDTO> languageDTOList = new ArrayList<>();
                    TalentLanguageDTO talentLanguageDTO = new TalentLanguageDTO();
                    languagesList.stream().forEach(s -> {
                        talentLanguageDTO.setRegulatedName(s);
                        languageDTOList.add(talentLanguageDTO);
                    });
                    dto.setLanguages(languageDTOList);
                }
                List<String> titleList = new ArrayList<>();
                List<String> companyList = new ArrayList<>();
                List<TalentExperienceDTO> talentExperienceList = JSON.parseObject(t.getTalentExtendedInfo()).containsKey("experiences") ? JSON.parseObject(t.getTalentExtendedInfo()).getJSONArray("experiences").toJavaList(TalentExperienceDTO.class) : new ArrayList<>();
                if (CollectionUtils.isNotEmpty(talentExperienceList)) {
                    titleList.add(talentExperienceList.get(0).getTitle());
                    companyList.add(talentExperienceList.get(0).getCompanyName());

                }
                if (ObjectUtil.isNotEmpty(titleList)) {
                    dto.setTitle(titleList);
                }
                if (ObjectUtil.isNotEmpty(companyList)) {
                    dto.setCompany(companyList);
                }
                dtoList.add(dto);
            });
        } else {
            return new ArrayList<>();
        }
        return dtoList;
    }

    @Override
    public List<TalentBriefDTO> getTalentsByIdsWithoutEntity(Set<Long> talentIds){
        List<TalentV3> talentList = talentRepository.findAllByIdIn(new ArrayList<>(talentIds));
        List<String> createdByList = talentList.stream().map(TalentV3::getCreatedBy).toList();
        Map<String, SimpleUser> userMap = simpleUserRepository.findAllByUidIn(createdByList)
                .stream()
                .collect(Collectors.toMap(
                        SimpleUser::getUid,  // key映射函数
                        user -> user,        // value映射函数
                        (existing, replacement) -> existing  // 如果有重复key的处理策略
                ));
        return talentList.stream().map(t -> {
            TalentBriefDTO dto = new TalentBriefDTO();
            SimpleUser simpleUser = userMap.get(t.getCreatedBy());
            dto.setTalentId(t.getId());
            dto.setFullName(t.getFullName());
            if(simpleUser != null) {
                dto.setCreateUserId(simpleUser.getId());
                dto.setCreateUserName(CommonUtils.formatFullNameWithBlankCheck(simpleUser.getFirstName(), simpleUser.getLastName()));
            }
            dto.setCreatedAt(t.getCreatedDate());
            dto.setTenantId(t.getTenantId());
            if(t.getTalentAdditionalInfo() != null) {
                TalentDTOV3 additionalInfoExpand = Convert.convert(TalentDTOV3.class, com.alibaba.fastjson.JSONObject.parseObject(t.getTalentExtendedInfo()));
                if(ObjectUtil.isNotNull(additionalInfoExpand.getSource())) {
                    dto.setSource(additionalInfoExpand.getSource());
                }
                //禁猎客户根据候选人中的experience公司id显示标签
                if(CollUtil.isNotEmpty(additionalInfoExpand.getExperiences())) {
                    dto.setExperiences(additionalInfoExpand.getExperiences());
                }
            }
            return dto;
        }).toList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<TalentDTOV3> checkExistTalents(TalentDTOV3 talentDTO) {
        Set<Long> talentIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(talentDTO.getContacts())) {
            Set<TalentContact> exclusiveContacts = talentDTO.getContacts().stream().filter(c -> StringUtils.isNotEmpty(c.getContact()) && ContactTypeConstants.EXCLUSIVE_CONTACT_TYPES.contains(c.getType()))
                    .map(tc -> {
                        Set<TalentContact> set = new HashSet<>();
                        //apn pro data
                        if (!tc.getType().equals(ContactType.WECHAT) && !"None".equals(tc.getContact())) {
                            set.add(Convert.convert(TalentContact.class, tc));
                        }
                        if (ContactType.LINKEDIN.equals(tc.getType())) {
                            String encoded = CommonUtils.urlEncodeIgnoreDuplicate(tc.getContact());
                            if (StringUtils.isNotEmpty(encoded)) {
                                TalentContact encodedLinkedIn = new TalentContact();
                                encodedLinkedIn.setContact(encoded);
                                encodedLinkedIn.setType(ContactType.LINKEDIN);
                                set.add(encodedLinkedIn);
                            }
                        }
                        return set;
                    }).flatMap(Collection::stream).collect(Collectors.toSet());
            //通过ESfiller接口查询重复
            if (ObjectUtil.isNotEmpty(exclusiveContacts)) {
                TalentV3 checkDuplicationTalent = TalentV3.fromTalentDTO(talentDTO);
                List<TalentContactDTO> contacts = talentDTO.getContacts();
                TalentSimilarityDto talentSimilarityDto = new TalentSimilarityDto();
                talentSimilarityDto.setCurrentLocation(talentDTO.getCurrentLocation());
                List<SuspectedDuplications> talentDTOV3List = getSuspectededDuplicationsByContactAndSmilarity(checkDuplicationTalent, contacts, talentSimilarityDto, null);
                if(CollUtil.isNotEmpty(talentDTOV3List)) {
                    talentIds.addAll(talentDTOV3List.stream().map(talent -> Long.parseLong(talent.get_id())).collect(Collectors.toSet()));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(talentDTO.getResumes())) {
            for (TalentResumeDTO t : talentDTO.getResumes()) {
                if (StringUtils.isNotEmpty(t.getUuid())) {
                    Collections.singletonList(talentResumeService.findByUuidAndTenantIdByCheckExistTalents(t.getUuid(), SecurityUtils.getTenantId())).forEach(talentResume -> {
                        if (talentResume.getTalentId() != null) {
                            talentIds.add(talentResume.getTalentId());
                        }
                    });
                }
            }
        }

        if (CollUtil.isEmpty(talentIds)) {
            return null;
        }
        List<TalentV3> talentV3List = talentRepository.findAllByIdIn(new ArrayList<>(talentIds)).stream().filter(o -> o.getTenantId().equals(SecurityUtils.getTenantId())).collect(Collectors.toList());
        if (!SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.ADMIN) && CollUtil.isEmpty(talentV3List)) {
            return null;
        } else {
            List<TalentDTOV3> result = new ArrayList<>();
            for (TalentV3 talent : talentV3List) {
                TalentDTOV3 talentDTOV3 = TalentDTOV3.fromTalent(talent);
                result.add(talentDTOV3);
            }
            return result;
        }
        //return findAllWithEntity(new ArrayList<>(talentIds));
    }

    @Transactional(readOnly = true)
    public List<TalentContact> findDuplicatedContacts(TalentContact contact) {
        return talentContactRepository.findAllByTypeAndContactAndTenantIdAndStatus(contact.getType(), contact.getContact(), SecurityUtils.getTenantId(), TalentContactStatus.AVAILABLE);
    }

    @Transactional(readOnly = true)
    public Set<Long> findExistingTalentIdsByExclusiveContacts(Set<TalentContact> contacts) {
        if (CollectionUtils.isEmpty(contacts)) {
            return null;
        }
        Set<Long> talentIds = new HashSet<>();
        contacts.stream().map(this::findDuplicatedContacts) //TODO
                .filter(CollectionUtils::isNotEmpty)
                .forEach(existing -> existing.stream().map(TalentContact::getTalentId).forEach(talentId -> {
                    if (talentId != null) {
                        talentIds.add(talentId);
                    }
                }));
        return talentIds;
    }

    @Override
    public List<SuspectedDuplications> checkContactExist(List<TalentContactDTO> contacts) {
        if(contacts == null) {
            return new ArrayList<>();
        }
        Set<Long> talentIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(contacts)) {
            Set<TalentContact> exclusiveContacts = contacts.stream().filter(c -> StringUtils.isNotEmpty(c.getContact()) && ContactTypeConstants.EXCLUSIVE_CONTACT_TYPES.contains(c.getType()))
                    .map(tc -> {
                        Set<TalentContact> set = new HashSet<>();
                        //apn pro data
                        if (!tc.getType().equals(ContactType.WECHAT) && !"None".equals(tc.getContact())) {
                            set.add(Convert.convert(TalentContact.class, tc));
                        }
                        if (ContactType.LINKEDIN.equals(tc.getType())) {
                            String encoded = CommonUtils.urlEncodeIgnoreDuplicate(tc.getContact());
                            if (StringUtils.isNotEmpty(encoded)) {
                                TalentContact encodedLinkedIn = new TalentContact();
                                encodedLinkedIn.setContact(encoded);
                                encodedLinkedIn.setType(ContactType.LINKEDIN);
                                set.add(encodedLinkedIn);
                            }
                        }
                        return set;
                    }).flatMap(Collection::stream).collect(Collectors.toSet());
            if (ObjectUtil.isNotEmpty(exclusiveContacts)) {
                talentIds.addAll(findExistingTalentIdsByExclusiveContacts(exclusiveContacts));
            }
        }
        List<TalentContact> repeatTalentContact = talentContactRepository.findAllByTalentIdInAndStatus(talentIds.stream().toList(), TalentContactStatus.AVAILABLE);
        if(repeatTalentContact.isEmpty()) {
            return new ArrayList<>();
        }
        List<SuspectedDuplications> result = new ArrayList<>();
        addContactCheckDuplication(result, repeatTalentContact.stream().collect(Collectors.toSet()));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    //@GlobalTransactional
    public TalentDTOV3 createAndUpdateCreditTransaction(TalentDTOV3 talentDTO) {
        StopWatch stopWatch = new StopWatch("createAndUpdateCreditTransaction");
        stopWatch.start("[1] setCommonEsData");
        boolean aiRecommend = false;
        //从ai推荐添加为我的候选人时，去获取commones候选人信息
        if(talentDTO.getRecommendFeedback() != null) {
            talentDTO = setCommonEsData(talentDTO);
            aiRecommend = true;
        }
        if (ObjectUtil.isNotNull(talentDTO.getId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_CREATEANDUPDATECREDITTRANSACTION_TALENTIDEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        log.info("[APN: TalentService @{}] create talent -> talentDTO: {}", SecurityUtils.getUserId(), talentDTO);
        setTalentDefaultValue(talentDTO);

        stopWatch.stop();
        stopWatch.start("[2] checkExistTalents");
        //check duplicate talent
        List<TalentDTOV3> existTalents = checkExistTalents(talentDTO);

        if (CollectionUtils.isNotEmpty(existTalents)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_CREATE_DUPLICATETALENTDATA.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        if(!checkOwnership(talentDTO.getOwnerships())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_UPDATE_CANNOTOWNERANDSHARE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        TalentV3 talentV3 = TalentV3.fromTalentDTO(talentDTO);
        talentV3.setLastEditedTime(Instant.now());
        try {
            stopWatch.stop();
            stopWatch.start("[3] getTalent");
            if (!aiRecommend) {
                TalentDTOV3 esTalent = esCommonService.getTalent(talentDTO.getEsId());
                boolean ownedByCurrentTenant = compareContacts(talentDTO.getContacts(), esTalent.getContacts());
                if (ownedByCurrentTenant) {
                    talentV3.setOwnedByTenants(SecurityUtils.getTenantId());
                }
            } else {
                talentV3.setOwnedByTenants(SecurityUtils.getTenantId());
            }
        } catch (IOException e) {
            log.error("[TalentServiceImpl: createAndUpdateCreditTransaction @{}] IOException when get talent from es by esId: {}", SecurityUtils.getUserId(), talentDTO.getEsId(), e);
            throw new CustomParameterizedException("IOException when get talent from es in createAndUpdateCreditTransaction");
        }
        stopWatch.stop();
        stopWatch.start("[4] saveTalent");
        TalentV3 talent = talentRepository.save(talentV3);
        stopWatch.stop();
        stopWatch.start("[5] setTalentRelateEntity");
        setTalentRelateEntity(talentDTO, talent.getId());
        stopWatch.stop();
        stopWatch.start("[6] checkDuplicates");
        checkDuplicates(talent);
        log.info("[APN: TalentService @{}] create talent success -> result: {}", SecurityUtils.getUserId(), talent);
        CreditTransactionDTO creditTransactionDTO = new CreditTransactionDTO();
        creditTransactionDTO.setId(talentDTO.getCreditTransactionId());
        creditTransactionDTO.setTalentId(talent.getId());
        creditTransactionDTO.setTenantId(talent.getTenantId());
        stopWatch.stop();
        stopWatch.start("[7] updateCreditTalentIdForCommonPool");
        //commonpool转为的候选人中esId也需要同步至es，直接调用保存，不在使用异步分布式事务导致同步2次
        userService.updateCreditTalentIdForCommonPool(creditTransactionDTO);

        stopWatch.stop();
        stopWatch.start("[8] addRecommendFeedback");
        addRecommendFeedback(talentDTO.getRecommendFeedback());
        stopWatch.stop();
        log.info("[apn @{}] createAndUpdateCreditTransaction time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return TalentDTOV3.fromTalent(talent);
    }

    private TalentDTOV3 setCommonEsData(TalentDTOV3 talentDTO) {
        try {
            TalentDTOV3 talent = esCommonService.getTalent(talentDTO.getRecommendFeedback().getTalentId());
            if(talent == null) {
                throw new CustomParameterizedException("Es common talent data acquisition failed");
            }
            talent.setRecommendFeedback(talentDTO.getRecommendFeedback());
            List<TalentOwnershipDTO> ownerships = new ArrayList<>();
            TalentOwnershipDTO owner = new TalentOwnershipDTO();
            owner.setUserId(SecurityUtils.getUserId());
            owner.setOwnershipType(TalentOwnershipType.TALENT_OWNER);
            ownerships.add(owner);
            talent.setOwnerships(ownerships);
            talent.setSource(ResumeSourceType.COMMON_POOL);
            return talent;
        } catch (Exception e) {
            throw new CustomParameterizedException("Es common talent data acquisition failed");
        }
    }

    @Resource
    private TalentServiceV3 talentServiceV3;

    private void addRecommendFeedback(RecommendFeedback recommendFeedback) {
        if(recommendFeedback == null) {
            return;
        }
        recommendFeedback.setReason(RecommendFeedbackReason.ADD_TO_TALENT);
        talentServiceV3.recordTalentJobRecommend(recommendFeedback);
    }

    private boolean compareContacts(List<TalentContactDTO> userInput, List<TalentContactDTO> es) {
        if (CollectionUtils.isEmpty(userInput)) {
            return false;
        }
        if (CollectionUtils.isEmpty(es)) {
            return true;
        }
        if (userInput.size() < es.size()) {
            return false;
        }
        if (userInput.size() > es.size()) {
            return true;
        }
        Set<String> set = new HashSet<>();
        for (TalentContactDTO contactDTO: userInput) {
            set.add(contactDTO.getTypeAndContact());
        }
        for (TalentContactDTO contactDTO: es) {
            if (!set.contains(contactDTO.getTypeAndContact())) {
                return true;
            }
        }
        return false;
    }

    private String filterByFormConfig(String data, String body) {
        if(StringUtils.isEmpty(data)) {
            return data;
        }
        JSONObject jsonObject = JSONUtil.parseObj(body);
        String config = jsonObject.getStr("customConfig");
        Map<String, Object> dataMap = new Gson().fromJson(data, new TypeToken<HashMap<String, Object>>() {}.getType());
        List<Map<String, Object>> formConfigMap = new Gson().fromJson(config, new TypeToken<List<HashMap<String, Object>>>() {}.getType());
        Map<String, Object> specifiedInfo = getSpecifiedInfo(dataMap, formConfigMap);
        return JSONUtil.toJsonStr(specifiedInfo);
    }

    public Map<String, Object> getSpecifiedInfo(Map<String, Object> fullProfile, List<Map<String, Object>> requirements) {
        if (requirements == null || requirements.isEmpty()) {
            return fullProfile;
        }
        Map<String, Object> info = new HashMap<>();
        for (Map<String, Object> req : requirements) {
            Object oVisible = req.get("visible");
            if(oVisible == null) {
                continue;
            }
            if (!(Boolean)oVisible) {
                continue;
            }
            Object oSubFields = req.get("subFields");
            List<String> subFields = oSubFields == null ? new ArrayList<>() : (List<String>) oSubFields;
            Object oAdditionalInfoKeys = req.get("additionalInfoKeys");
            List<String> additionalInfoKeys = oAdditionalInfoKeys == null ? new ArrayList<>() : (List<String>) oAdditionalInfoKeys;
            for (String key : additionalInfoKeys) {
                if (info.containsKey(key)) {
                    continue;
                }
                Object deeperFullProfile = fullProfile.get(key);
                if (deeperFullProfile == null) {
                    continue;
                }
                Object value;
                if (deeperFullProfile instanceof List) {
                    value = new ArrayList<>();
                    for (Object element : (List<Object>) deeperFullProfile) {
                        if(element instanceof Number) {
                            ((List<Long>) value).add(NumberUtil.parseLong(element.toString()));
                        } else if(element instanceof String) {
                            ((List<String>) value).add(String.valueOf(element));
                        } else if(element instanceof Map) {
                            Map<String, Object> subFullProfile = new Gson().fromJson(JSONUtil.toJsonStr(element), new TypeToken<HashMap<String, Object>>() {}.getType());
                            List<Map<String, Object>> subRequirements = new Gson().fromJson(JSONUtil.toJsonStr(subFields), new TypeToken<List<HashMap<String, Object>>>() {}.getType());
                            Map<String, Object> ele = getSpecifiedInfo(subFullProfile, subRequirements);
                            if (ele != null && !ele.isEmpty()) {
                                ((List<Object>) value).add(ele);
                            }
                        }
                    }
                } else if (deeperFullProfile instanceof Map) {
                    List<Map<String, Object>> subRequirements = new Gson().fromJson(JSONUtil.toJsonStr(subFields), new TypeToken<List<HashMap<String, Object>>>() {}.getType());
                    value = getSpecifiedInfo((Map<String, Object>) deeperFullProfile, subRequirements);
                } else {
                    value = deeperFullProfile;
                }
                if (value != null || value.equals(0)) {
                    info.put(key, value);
                }
            }
        }
        return info;
    }

    @Override
    public List<TalentEmailContactVO> searchTalentEmailContacts(List<Long> talentIdList) {
        if (CollectionUtils.isEmpty(talentIdList)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_SEARCHTALENTEMAILCONTACTS_PARAMLISTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }

        List<Map<String, Object>> talentNameList = talentRepository.findAllTalentNameByIdIn(talentIdList);
        List<TalentNameVM> talentNameVMList = talentNameList.stream().map(o -> BeanUtil.fillBeanWithMap(o, new TalentNameVM(), false)).collect(Collectors.toList());
        List<TalentNameVM> tenantTalentList = talentNameVMList.stream().filter(o -> o.getTenantId().equals(SecurityUtils.getTenantId())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(tenantTalentList)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_SEARCHTALENTEMAILCONTACTS_TALENTLISTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }

        List<Long> tenantTalentIdList = tenantTalentList.stream().map(TalentNameVM::getId).collect(Collectors.toList());
        List<TalentContact> contactList = talentContactRepository.findAllByTalentIdInAndStatusAndTypeInOrderByLastModifiedDateDesc(tenantTalentIdList, TalentContactStatus.AVAILABLE, Arrays.asList(ContactType.PRIMARY_EMAIL, ContactType.EMAIL));
        Map<Long, List<TalentContact>> contactListMap = contactList.stream().filter(o -> o.getTenantId().equals(SecurityUtils.getTenantId())).collect(Collectors.groupingBy(TalentContact::getTalentId));

        List<TalentEmailContactVO> talentEmailContactVOList = new ArrayList<>();
        tenantTalentList.forEach(o -> {
            if (contactListMap.containsKey(o.getId())) {
                TalentContact talentContact = contactListMap.get(o.getId()).get(0);
                talentEmailContactVOList.add(new TalentEmailContactVO(o.getId(), talentContact.getId(), o.getFirstName(), o.getLastName(), CommonUtils.formatFullName(o.getFirstName(), o.getLastName(), o.getFullName()), talentContact.getContact()));
            }
        });

        return talentEmailContactVOList;
    }

    @Override
    public List<TalentDTOV3> searchTalentsByContactAndSimilarityAPNPro(TalentSimilarityDto talentSimilarityDto) {
        List<TalentContactDTO> contacts = talentSimilarityDto.getContacts();
        if(contacts == null) {
            contacts = new ArrayList<>();
        }
        List<TalentContactVM> contactVmList = JSONUtil.toList(JSONUtil.parseArray(contacts), TalentContactVM.class);
        return searchTalentsByContactAndSimilarity(null, contacts, contactVmList); //apn pro不需要更具talent信息进行模糊查重，仅需根据contact信息精确查重
    }

    @Override
    public Set<Long> searchTalentIdsByContactAndSimilarity(TalentSimilarityDto talentSimilarityDto) {
        List<TalentContactDTO> contacts = talentSimilarityDto.getContacts();
        if(contacts == null) {
            contacts = new ArrayList<>();
        }
        List<TalentContactVM> contactVmList = JSONUtil.toList(JSONUtil.parseArray(contacts), TalentContactVM.class);
        return searchTalentIdsByContactAndSimilarity(talentSimilarityDto, contacts, contactVmList);
    }

    @Override
    public List<SuspectedDuplications> getSuspectededDuplicationsByContactAndSmilarity(TalentV3 talentV3, List<TalentContactDTO> contacts, TalentSimilarityDto talentSimilarityDto, Long ignoreTalentId) {
        //filter out wrong contact
        contacts = contacts.stream().filter(c -> !TalentContactVerificationStatus.WRONG_CONTACT.equals(c.getVerificationStatus())).collect(Collectors.toList());

        List<TalentContactVM> contactVmList = JSONUtil.toList(JSONUtil.parseArray(contacts), TalentContactVM.class);
        if (CollUtil.isEmpty(contactVmList)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_SEARCHTALENTSBYCONTACTANDSIMILARITY_CONTACTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        StopWatch stopWatch = new StopWatch("getSuspectededDuplicationsByContactAndSmilarity");
        stopWatch.start("[1] checkTalentDuplicationWithResult");
        List<SuspectedDuplications> result = esFillerTalentService.checkTalentDuplicationWithResult(talentV3, contacts, talentSimilarityDto, applicationProperties.getSimilarity().toString(), ignoreTalentId);
        if (CollUtil.isNotEmpty(result)) {
            result = result.stream().filter(dto -> dto.get_similarity().compareTo(applicationProperties.getSimilarity()) >= 0).collect(Collectors.toList());
        }
        stopWatch.stop();
        stopWatch.start("[2] getTalentDuplicationsByContacts");
        Set<TalentContact> talentDuplicationsByContacts = talentContactService.getTalentDuplicationsByContacts(ignoreTalentId, contactVmList);
        if(!talentDuplicationsByContacts.isEmpty()) {
            stopWatch.stop();
            stopWatch.start("[3] addContactCheckDuplication");
            addContactCheckDuplication(result, talentDuplicationsByContacts);
        }
        stopWatch.stop();
        stopWatch.start("[4] addContactCheckDuplication");
        fillConfidentialDuplicateInfo(result);
        stopWatch.stop();
        log.info("[apn @{}] getSuspectededDuplicationsByContactAndSmilarity time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        log.info("getSuspectededDuplicationsByContactAndSmilarity result : {}", result);
        return result;
    }

    private void fillConfidentialDuplicateInfo(List<SuspectedDuplications> result) {
        if (CollUtil.isEmpty(result)) {
            return;
        }
        Set<Long> duplicationTalentIds = result.stream().map(SuspectedDuplications::get_id).map(Long::parseLong).collect(Collectors.toSet());
        Map<Long, ConfidentialInfoDto> confidentialInfoList = talentConfidentialService.getConfidentialInfoBatch(duplicationTalentIds);
        Set<Long> viewAbleTalentIds = talentConfidentialService.filterViewableTalentIds(confidentialInfoList.keySet());
        List<TalentV3> talentV3List = talentRepository.findAllByIdIn(duplicationTalentIds.stream().toList());
        List<TalentOwnership> ownerships = talentOwnershipRepository.findAllByTalentIdInAndOwnershipTypeIn(duplicationTalentIds.stream().toList(),Arrays.asList(TalentOwnershipType.TALENT_OWNER));
        Map<Long,String> talentOwnerMap = new HashMap<>();
        if (CollUtil.isNotEmpty(ownerships)) {
            List<Long> userIds = ownerships.stream().map(TalentOwnership::getUserId).toList();
            List<User> users = userService.findByIds(userIds).getBody();
            if (CollUtil.isNotEmpty(users)) {
                Map<Long, String> userMap = users.stream().collect(Collectors.toMap(User::getId, u -> u.getFirstName() + "" + u.getLastName()));
                ownerships.forEach(owner -> {
                    if (userMap.containsKey(owner.getUserId())) {
                        talentOwnerMap.put(owner.getTalentId(), userMap.get(owner.getUserId()));
                    }
                });
            }
        }

        Map<Long, String> talentNameMap = talentV3List.stream().collect(Collectors.toMap(TalentV3::getId, TalentV3::getLastName));
        for (SuspectedDuplications duplications : result) {
            String id = duplications.get_id();
            if (id == null) {
                continue;
            }
            Long talentId = Long.parseLong(id);
            if (talentOwnerMap.containsKey(talentId)) {
                duplications.setTalentOwner(talentOwnerMap.get(talentId));
            }
            ConfidentialInfoDto confidentialInfoDto = confidentialInfoList.get(talentId);
            if (confidentialInfoDto != null) {
                duplications.setConfidentialInfo(confidentialInfoDto);
                if (!viewAbleTalentIds.contains(talentId)) {
                    if (!talentNameMap.containsKey(talentId)) {
                        throw new CustomParameterizedException("talent not found");
                    }
                    //TalentV3 talent = talentRepository.findById(talentId).orElseThrow(() -> new CustomParameterizedException("talent not found"));
                    duplications.confidentialDuplicateInfo(talentNameMap.get(talentId));
                }
            }
        }
    }

    private void addContactCheckDuplication(List<SuspectedDuplications> result, Set<TalentContact> talentDuplicationsByContacts) {
        Map<Long, List<TalentContact>> talentDuplication = talentDuplicationsByContacts.stream().collect(Collectors.groupingBy(TalentContact::getTalentId));
        Set<Long> talentIds = talentDuplication.keySet();
        if (CollectionUtils.isEmpty(talentIds)) {
            return;
        }
        List<SuspectedDuplications> contactCheckDuplication = new ArrayList<>();
        List<TalentV3> talentV3List = talentRepository.findAllByIdIn(talentIds.stream().toList());

        List<TalentLastModifiedTimeDTO> talentEsLastModifiedTimeList = talentRepository.getTalentEsLastModifiedTimeList(talentIds.stream().toList());
        Map<Long, Instant> talentEsLastModifiedTimeMap = talentEsLastModifiedTimeList.stream().collect(Collectors.toMap(TalentLastModifiedTimeDTO::getTalentId, TalentLastModifiedTimeDTO::getLastModifiedDate));

        for (TalentV3 talent : talentV3List) {
            if(!existInSuspectedDuplications(talent.getId(), result)) {
                SuspectedDuplications contactExist = new SuspectedDuplications();
                contactExist.set_id(talent.getId().toString());
                contactExist.set_index("talents_" + SecurityUtils.getTenantId());
                contactExist.set_similarity(new BigDecimal(1));

                TalentDTOV3 talentDTOV3 = TalentDTOV3.fromTalent(talent);
                List<TalentExperienceDTO> talentExperienceDTOS = sortTalentExperiences(talentDTOV3.getExperiences());
                contactExist.setFullName(talent.getFullName());
                if(talentExperienceDTOS != null && !talentExperienceDTOS.isEmpty()) {
                    TalentExperienceDTO experienceDTO = talentExperienceDTOS.get(0);
                    contactExist.setCompanyName(experienceDTO.getCompanyName());
                    contactExist.setTitle(experienceDTO.getTitle());
                }
                Instant talentEsLastModifiedTime = talentEsLastModifiedTimeMap.getOrDefault(talent.getId(), null);
                contactExist.setLastModifiedDate(com.altomni.apn.common.utils.DateUtil.fromInstantToUtcDateTimeWithMillisecond(talentEsLastModifiedTime));
                setDuplicateContacts(contactExist, talentDuplication.get(talent.getId()));
                contactCheckDuplication.add(contactExist);
            }
        }
        if(!contactCheckDuplication.isEmpty()) {
            result.addAll(contactCheckDuplication);
        }
    }

    private void setDuplicateContacts(SuspectedDuplications contactExist, List<TalentContact> talentContacts) {
        DuplicateContacts duplicateContacts = contactExist.getDuplicateContacts();
        for(TalentContact contact : talentContacts) {
            if(ContactType.PHONE.equals(contact.getType())) {
                duplicateContacts.setPhones(duplicateContacts.getPhones() + 1);
            } else if(ContactType.EMAIL.equals(contact.getType())) {
                duplicateContacts.setEmails(duplicateContacts.getEmails() + 1);
            } else if(ContactType.WECHAT.equals(contact.getType())) {
                duplicateContacts.setWechats(duplicateContacts.getWechats() + 1);
            } else if(ContactType.WHATSAPP.equals(contact.getType())) {
                duplicateContacts.setWhatsApps(duplicateContacts.getWhatsApps() + 1);
            } else if(ContactType.LINKEDIN.equals(contact.getType())) {
                duplicateContacts.setLinkedIn(duplicateContacts.getLinkedIn() + 1);
            } else if(ContactType.MAIMAI.equals(contact.getType())) {
                duplicateContacts.setMaiMai(duplicateContacts.getMaiMai() + 1);
            } else if(ContactType.GITHUB.equals(contact.getType())) {
                duplicateContacts.setGithub(duplicateContacts.getGithub() + 1);
            } else if(ContactType.DODAX.equals(contact.getType())) {
                duplicateContacts.setDodax(duplicateContacts.getDodax() + 1);
            } else {
                duplicateContacts.setUnknownContacts(duplicateContacts.getUnknownContacts() + 1);
            }
        }
    }

    private boolean existInSuspectedDuplications(Long talentId, List<SuspectedDuplications> result) {
        for(SuspectedDuplications duplication : result) {
            String id = duplication.get_id();
            if(id != null && talentId.equals(Long.valueOf(id))) {
                return true;
            }
        }
        return false;
    }

    public Set<Long> searchTalentIdsByContactAndSimilarity(TalentSimilarityDto talentSimilarityDto, List<TalentContactDTO> contacts, List<TalentContactVM> contactVmList){
        Set<Long> allIds = new HashSet<>();
        TalentV3 talentV3 = null;
        Long ignoreTalentId = null;
        if (Objects.nonNull(talentSimilarityDto)) {
            talentV3 = convertTalent(talentSimilarityDto);
            ignoreTalentId = talentSimilarityDto.getIgnoreTalentId();
        }
        List<SuspectedDuplications> result = esFillerTalentService.checkTalentDuplicationWithResult(talentV3, contacts, talentSimilarityDto, applicationProperties.getSimilarity().toString(), ignoreTalentId);
        if (CollUtil.isNotEmpty(result)) {
            Set<Long> ids = result.stream()
                    .filter(ObjectUtil::isNotEmpty)
                    .filter(dto -> dto.get_similarity().compareTo(applicationProperties.getSimilarity()) >= 0)
                    .map(dto -> Long.parseLong(dto.get_id())).collect(Collectors.toSet());
            allIds.addAll(ids);
        }
        Set<Long> mysqlIds = talentContactService.getTalentIdsByContactsIgnoreWrongContact(ignoreTalentId, contactVmList);
        log.info("checkTalentDuplicationByMySqlWithResult result : {}", mysqlIds);
        allIds.addAll(mysqlIds);
        return allIds;
        //return findTalentDtoByIds(new ArrayList<>(allIds));
    }

    public List<TalentDTOV3> searchTalentsByContactAndSimilarity(TalentSimilarityDto talentSimilarityDto, List<TalentContactDTO> contacts, List<TalentContactVM> contactVmList){
        Set<Long> allIds = searchTalentIdsByContactAndSimilarity(talentSimilarityDto, contacts, contactVmList);
        return findTalentDtoByIds(new ArrayList<>(allIds));
    }

    public LinkedList<Long> searchTalentsByContactAndSimilarityByExcel(TalentSimilarityDto talentSimilarityDto, String json) {
        List<TalentContactDTO> contacts = talentSimilarityDto.getContacts();
        List<TalentContactVM> contactVmList = JSONUtil.toList(JSONUtil.parseArray(contacts), TalentContactVM.class);
        if (CollUtil.isEmpty(contactVmList)) {
            throw new CustomParameterizedException("contact is not null");
        }
        LinkedList<Long> allIds = new LinkedList<>();
        TalentV3 talentV3 = convertTalentByExcel(talentSimilarityDto, json);
        List<SuspectedDuplications> result = esFillerTalentService.checkTalentDuplicationWithResult(talentV3, contacts, talentSimilarityDto, applicationProperties.getSimilarity().toString(), talentSimilarityDto.getIgnoreTalentId());
        if (CollUtil.isNotEmpty(result)) {
            result.stream().filter(ObjectUtil::isNotEmpty)
                    .filter(dto -> dto.get_similarity().compareTo(applicationProperties.getSimilarity()) >= 0)
                    .forEach(dto -> allIds.addLast(Long.parseLong(dto.get_id())));
        }
        Set<Long> mysqlIds = talentContactService.getTalentIdsByContactsIgnoreWrongContact(talentSimilarityDto.getIgnoreTalentId(), contactVmList);
        log.info("checkTalentDuplicationByMySqlWithResult result : {}", mysqlIds);
        mysqlIds.forEach(allIds::addLast);
        return allIds;
    }

    @Override
    public List<Long> searchTalentsByContactAndSimilarity(TalentDTOV3 talentDTOV3) {
        if (talentDTOV3.getContacts() == null) {
            return new ArrayList<>();
        }
        Set<Long> allIds = new HashSet<>();
        List<DuplicationCheckResponseSuspectedDuplicationsDto> result = esFillerTalentService.checkTalentDuplicationWithResult(talentDTOV3.getTenantId(), talentDTOV3.getContacts());
        if (CollUtil.isNotEmpty(result)) {
            Set<Long> ids = result.stream()
                    .filter(ObjectUtil::isNotEmpty)
                    .filter(dto -> dto.get_similarity().compareTo(BigDecimal.valueOf(1.00)) == 0)
                    .map(dto -> Long.parseLong(dto.get_id())).collect(Collectors.toSet());
            allIds.addAll(ids);
        }
        return new ArrayList<>(allIds);
    }

    /**
     * 只需要查联系方式
     * @param talentDTOV3
     * @return
     */
    @Override
    public List<TalentDTOV3> searchTalentsByContact(TalentDTOV3 talentDTOV3) {
        if (CollUtil.isEmpty(talentDTOV3.getContacts())) {
            return new ArrayList<>();
        }
        Set<Long> allIds = new HashSet<>();
        List<DuplicationCheckResponseSuspectedDuplicationsDto> result = esFillerTalentService.checkTalentDuplicationWithResult(talentDTOV3.getTenantId(), talentDTOV3.getContacts());
        if (CollUtil.isNotEmpty(result)) {
            Set<Long> ids = result.stream()
                    .filter(ObjectUtil::isNotEmpty)
                    .filter(dto -> dto.get_similarity().compareTo(BigDecimal.valueOf(1.00)) == 0)
                    .map(dto -> Long.parseLong(dto.get_id())).collect(Collectors.toSet());
            allIds.addAll(ids);
        }
        List<TalentContactVM> contactVmList = JSONUtil.toList(JSONUtil.parseArray(talentDTOV3.getContacts()), TalentContactVM.class);
//        Set<Long> mysqlIds = talentContactService.getTalentIdsByContacts(talentDTOV3.getId(), contactVmList);
        Set<Long> mysqlIds = talentContactService.getTalentIdsByContactsIgnoreWrongContact(talentDTOV3.getId(), contactVmList);
        allIds.addAll(mysqlIds);
        log.info("checkTalentDuplicationByMySqlWithResult result : {}", allIds);
        List<TalentV3> talentV3List = talentRepository.findAllByIdIn(allIds.stream().toList());
        List<TalentContact> talentContactList = talentContactRepository.findAllByTalentIdInAndStatus(allIds.stream().toList(), TalentContactStatus.AVAILABLE);
        List<TalentOwnership> talentOwnershipList = talentOwnershipRepository.findAllByTalentIdInAndOwnershipTypeIn(allIds.stream().toList(), Arrays.asList(TalentOwnershipType.OWNER, TalentOwnershipType.TALENT_OWNER));
        Map<Long, List<TalentContact>> talentContactMap = talentContactList.stream().collect(Collectors.groupingBy(TalentContact::getTalentId));
        Map<Long, List<TalentOwnership>> talentOwnershipMap = talentOwnershipList.stream().collect(Collectors.groupingBy(TalentOwnership::getTalentId));
        return talentV3List.stream().map(o -> {
            TalentDTOV3 talentDTO = TalentDTOV3.fromTalent(o);
            if (talentContactMap.containsKey(o.getId())) {
                talentDTO.setContacts(talentContactMap.get(o.getId()).stream().map(TalentContactDTO::fromTalentContact).toList());
            }
            if (talentOwnershipMap.containsKey(o.getId())) {
                talentDTO.setOwnerships(toDtos(talentOwnershipMap.get(o.getId())));
            }
            return talentDTO;
        }).toList();
    }

    @Override
    public List<TalentDTOV3> findTalentsBasicByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<TalentV3> talentV3List = talentRepository.findAllByIdIn(ids);
        List<TalentContact> talentContactList = talentContactRepository.findAllByTalentIdInAndStatus(ids, TalentContactStatus.AVAILABLE);
        List<TalentOwnership> talentOwnershipList = talentOwnershipRepository.findAllByTalentIdInAndOwnershipTypeIn(ids, Arrays.asList(TalentOwnershipType.OWNER, TalentOwnershipType.TALENT_OWNER));
        Map<Long, List<TalentContact>> talentContactMap = talentContactList.stream().collect(Collectors.groupingBy(TalentContact::getTalentId));
        Map<Long, List<TalentOwnership>> talentOwnershipMap = talentOwnershipList.stream().collect(Collectors.groupingBy(TalentOwnership::getTalentId));
        return talentV3List.stream().map(o -> {
            TalentDTOV3 talentDTO = TalentDTOV3.fromTalent(o);
            if (talentContactMap.containsKey(o.getId())) {
                talentDTO.setContacts(talentContactMap.get(o.getId()).stream().map(TalentContactDTO::fromTalentContact).toList());
            }
            if (talentOwnershipMap.containsKey(o.getId())) {
                talentDTO.setOwnerships(toDtos(talentOwnershipMap.get(o.getId())));
            }
            return talentDTO;
        }).toList();
    }

    private TalentV3 convertTalent(TalentSimilarityDto talentSimilarityDto) {
        TalentDTOV3 talentDTO = new TalentDTOV3();
        BeanUtil.copyProperties(talentSimilarityDto, talentDTO);
        setTalentDefaultValue(talentDTO);
        return TalentV3.fromTalentDTO(talentDTO);
    }

    private TalentV3 convertTalentByExcel(TalentSimilarityDto talentSimilarityDto, String json) {
        TalentDTOV3 talentDTO = new TalentDTOV3();
        BeanUtil.copyProperties(talentSimilarityDto, talentDTO);
        setTalentDefaultValue(talentDTO);
        TalentV3 talent = TalentV3.fromTalentDTO(talentDTO);
        return formatFromJson(talent, json);
    }

    private TalentV3 formatFromJson(TalentV3 talent, String json) {
        if (StrUtil.isNotBlank(json)) {
            String extendedInfo = talent.getTalentExtendedInfo();
            extendedInfo = TalentV3.mergeExtendedInfo(extendedInfo, json);
            talent.setTalentExtendedInfo(extendedInfo);
        }
        return talent;
    }

    private void checkTalentRequiredFiled(TalentDTOV3 talentDTO) {
        if (StrUtil.isBlank(talentDTO.getFirstName())
                && StrUtil.isBlank(talentDTO.getLastName())) {
            throw new CustomParameterizedException("name is required");
        }
        if (CollUtil.isEmpty(talentDTO.getContacts())) {
            throw new CustomParameterizedException("contacts is required");
        }
    }

    @Override
    public List<Long> findIdsByTenant(Long tenantId) {
        return talentRepository.findIdsByTenant(tenantId);
    }

    @Override
    public Object fillResumeInfos() {
        Instant start = Instant.now();
        log.info("fill resume infos start : {}", start);
        int page = 0;
        String pageIndexStr = commonRedisService.get(FILL_RESUME_INFOS_KEY);
        if (StrUtil.isNotBlank(pageIndexStr)) {
            page = Integer.parseInt(pageIndexStr);
        }
        int size = 500;
        long total = 0L;
        AtomicLong success = new AtomicLong(0L);
        AtomicLong error = new AtomicLong(0L);
        List<String> errorResumeIds = new ArrayList<>();
        StringBuffer md5 = new StringBuffer();
        Integer hasDisplay = 0;
        Integer hasPortrait = 0;
        Integer nPages = 0;
        while (true) {
            //1.search from table resume
            Pageable pageable = PageRequest.of(page, size);
            Page<Resume> resumePage = resumeRepository.findAll(pageable);
            List<Resume> contentList = resumePage.getContent().stream().filter(r -> ObjectUtil.isNotEmpty(r.getUuid())).collect(Collectors.toList());
            log.info("fill resume infos task count = {}, size = {}", page + 1, contentList.size());
            total += contentList.size();
            if (CollUtil.isEmpty(contentList)) {
                break;
            }
            //2.get s3 ObjectMetadata and save resume infos
            for (Resume resume : contentList) {
                try {
                    md5.append(resume.getUuid());
                    if (ObjectUtil.isNull(md5)) {
                        continue;
                    }
                    //get has_display， has_portrait， n_pages from s3 ObjectMetadata
                    CloudFileObjectMetadata cloudFileObjectMetadata = storeService.getFileDetailWithoutFileFromS3(md5.toString(), UploadTypeEnum.RESUME.getKey()).getBody();
                    if (cloudFileObjectMetadata == null) {
                        continue;
                    }
                    Map<String, String> userMetadata = cloudFileObjectMetadata.getUserMetadata();
                    if(userMetadata == null){
                        error.getAndIncrement();
                        errorResumeIds.add(resume.getUuid());
                        log.error("fill resume infos error, objectMetadata is empty, resume :" + JSON.toJSONString(resume));
                        continue;
                    }
                    if(ObjectUtil.isNotEmpty(userMetadata.get(USER_METADATA_HAS_DISPLAY))) {
                        hasDisplay = Integer.parseInt(userMetadata.get(USER_METADATA_HAS_DISPLAY));
                    }
                    if(ObjectUtil.isNotEmpty(userMetadata.get(USER_METADATA_HAS_PORTRAIT))) {
                        hasPortrait = Integer.parseInt(userMetadata.get(USER_METADATA_HAS_PORTRAIT));
                    }
                    if(ObjectUtil.isNotEmpty(userMetadata.get(USER_METADATA_N_PAGES))) {
                        nPages = Integer.parseInt(userMetadata.get(USER_METADATA_N_PAGES));
                    }
                    log.info("fill resume infos, userMetadata infos： hasDisplay = {}, hasPortrait = {}, nPages = {}", hasDisplay, hasPortrait, nPages);
                    // update resume infos by uuid
                    resumeRepository.updateInfosById(hasDisplay, hasPortrait, nPages, resume.getId());
                    log.info("fill resume infos, update resume by id = {}, set uuid = {}", resume.getId(), md5);
                    success.getAndIncrement();
                } catch (Exception e) {
                    error.getAndIncrement();
                    errorResumeIds.add(resume.getUuid());
                    log.error("fill resume infos error resume :" + JSON.toJSONString(resume) + "," + "error :" + e.getMessage());
                } finally {
                    md5.setLength(0);
                    hasDisplay = 0;
                    hasPortrait = 0;
                    nPages =0;
                }
            }
            page++;
            commonRedisService.set(FILL_RESUME_INFOS_KEY, String.valueOf(page));
        }
        Instant end = Instant.now();
        log.info("fill resume infos end : {}", end);
        Map<String, Object> result = new HashMap<>();
        result.put("start", start);
        result.put("end", end);
        result.put("error", error);
        result.put("errorResumeIds", errorResumeIds);
        result.put("total", total);
        log.info("result = {}", JSONUtil.toJsonStr(result));
        return result;
    }

    private String formatContentType(String contentType, String fileName) {
        if (CONTENT_TYPE_APPLICATION_JSON.contains(contentType)) {
            return MediaType.PLAIN_TEXT_UTF_8.toString();
        } else if (CONTENT_TYPE_WPS_WRITER.equals(contentType)) {
            return MediaType.MICROSOFT_WORD.toString();
        } else if (CONTENT_TYPE_OCTET_STREAM.contains(contentType)) {
            if (ObjectUtil.isNotNull(fileName)) {
                Path path = Paths.get(fileName);
                String fileExtension = path.getFileName().toString().split("\\.")[1];
                if (ObjectUtil.isNotNull(fileExtension)) {
                    return fileSuffixToContentType(fileExtension);
                }
            }
        }
        return null;
    }

    private void initializeRedisForResumeParser(String uuid, String filename, String contentType, int priority) {
        String key = "parser:resume:" + uuid + ":metadata";
        Boolean exist = commonRedisService.exists(key);
        if (BooleanUtils.isTrue(exist)) {
            log.info("[ParserServiceImpl: uuid: {}, initializeRedisForResumeParser] redis already exists key: {} when init for fileName: {} with priority: {}", uuid, key, filename, priority);
            return;
        }

        Map<String, String> set = new HashMap<>();
        set.put("priority", String.valueOf(priority));
        set.put("requester", "apn");
        set.put("filename", filename);
        set.put("ContentType", contentType);

        commonRedisService.hset(key, set, REDIS_EXPIRE_TIME);
        log.info("[ParserServiceImpl: uuid: {}, initializeRedisForResumeParser] Write metadata(filename: {}, contentType: {}, priority: {}) into redis with key: {} and ttl: {}", uuid, filename, contentType, priority, key, REDIS_EXPIRE_TIME);
    }

    @Nullable
    public List<TalentEducationDTO> sortTalentEducations(List<TalentEducationDTO> talentEducationList) {
        List<EnumDegree> bizDictList = enumCommonService.findAllEnumDegree();
        Map<String, EnumDegree> map = bizDictList.stream().collect(Collectors.toMap(a -> a.getName(), a -> a, (a1,a2) -> a1));
        if (ObjectUtil.isNotNull(talentEducationList)) {
            talentEducationList.sort(new Comparator<TalentEducationDTO>() {
                @Override
                public int compare(TalentEducationDTO o1, TalentEducationDTO o2) {
                    //sorting degree level
                    if (ObjectUtil.isNotNull(o1.getDegreeLevel())) {
                        if (ObjectUtil.isNull(o2.getDegreeLevel())) {
                            return -1;
                        } else {
                            Optional<EnumDegree> dictOptionalO1 = Optional.empty();
                            Optional<EnumDegree> dictOptionalO2 = Optional.empty();
                            if (map.containsKey(o1.getDegreeLevel())) {
                                dictOptionalO1 = Optional.of(map.get(o1.getDegreeLevel()));
                            }
                            if (map.containsKey(o2.getDegreeLevel())) {
                                dictOptionalO2 = Optional.of(map.get(o2.getDegreeLevel()));
                            }
                            if (dictOptionalO1.isPresent() && dictOptionalO2.isPresent()) {
                                if (ObjectUtil.isNotNull(dictOptionalO1.get().getScore()) && ObjectUtil.isNotNull(dictOptionalO2.get().getScore())) {
                                    if (dictOptionalO1.get().getScore() < dictOptionalO2.get().getScore()) {
                                        return 1;
                                    }
                                    if (dictOptionalO1.get().getScore() > dictOptionalO2.get().getScore()) {
                                        return -1;
                                    }
                                }
                            }
                        }
                    } else if (ObjectUtil.isNotNull(o2.getDegreeLevel())) {
                        return 1;
                    }
                    //sorting date
                    if (ObjectUtil.isNotNull(o1.getCurrent()) && o1.getCurrent()) {
                        //if one is current, ignore all end_date. end_dates are bad data.
                        if (ObjectUtil.isNotNull(o2.getCurrent()) && o2.getCurrent()) {
                            if (ObjectUtil.isNotNull(o1.getStartDate())) {
                                if (ObjectUtil.isNotNull(o2.getStartDate())) {
                                    if (o1.getStartDate().isBefore(o2.getStartDate())) {
                                        return 1;
                                    } else if (o1.getStartDate().isAfter(o2.getStartDate())) {
                                        return -1;
                                    } else {
                                        return 0;
                                    }
                                } else {
                                    //# the one without start date is smaller
                                    return -1;
                                }
                            } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                                //the one without start date is smaller
                                return 1;
                            }
                        } else {
                            return -1;
                        }
                    } else if (ObjectUtil.isNotNull(o2.getCurrent()) && o2.getCurrent()) {
                        return 1;
                    }
                    //if neither is current, use end_date
                    if (ObjectUtil.isNotNull(o1.getEndDate())) {
                        if (ObjectUtil.isNotNull(o2.getEndDate())) {
                            if (o1.getEndDate().isBefore(o2.getEndDate())) {
                                return 1;
                            } else if (o1.getEndDate().isAfter(o2.getEndDate())) {
                                return -1;
                            }
                            //if equals, use start_date
                        } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                            if (o1.getEndDate().isBefore(o2.getStartDate())) {
                                return 1;
                            } else if (ObjectUtil.isNotNull(o1.getStartDate())) {
                                if (o1.getStartDate().isAfter(o2.getStartDate())) {
                                    return -1;
                                } else {
                                    return 1;
                                }
                            } else {
                                //end_date is larger than other.start_date
                                return -1;
                            }
                        } else {
                            return -1;
                        }
                    } else if (ObjectUtil.isNotNull(o2.getEndDate())) {
                        if (ObjectUtil.isNotNull(o1.getStartDate())) {
                            if (o1.getStartDate().isAfter(o2.getEndDate())) {
                                return -1;
                            } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                                if (o1.getStartDate().isBefore(o2.getStartDate())) {
                                    return 1;
                                } else {
                                    return -1;
                                }
                            } else {
                                return 1;
                            }
                        } else {
                            return 1;
                        }
                    }
                    //if neither exists end_date, use start_date as well
                    //here, we have already dealt with one end date existing cases
                    if (ObjectUtil.isNotNull(o1.getStartDate())) {
                        if (ObjectUtil.isNotNull(o2.getStartDate())) {
                            if (o1.getStartDate().isBefore(o2.getStartDate())) {
                                return 1;
                            } else if (o1.getStartDate().isAfter(o2.getStartDate())) {
                                return -1;
                            }
                        } else {
                            return -1;
                        }
                    } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                        return 1;
                    }
                    //missing info or equals, unable to determine, return None
                    return 0;
                }
            });
        }
        return talentEducationList;
    }

    @Nullable
    public static List<TalentExperienceDTO> sortTalentExperiences(List<TalentExperienceDTO> talentExperienceDTOList) {
        if (ObjectUtil.isNotNull(talentExperienceDTOList)) {
            talentExperienceDTOList.sort(new Comparator<TalentExperienceDTO>() {
                @Override
                public int compare(TalentExperienceDTO o1, TalentExperienceDTO o2) {
                    //sorting date
                    if (ObjectUtil.isNotNull(o1.getCurrent()) && o1.getCurrent()) {
                        //if one is current, ignore all end_date. end_dates are bad data.
                        if (ObjectUtil.isNotNull(o2.getCurrent()) && o2.getCurrent()) {
                            if (ObjectUtil.isNotNull(o1.getStartDate())) {
                                if (ObjectUtil.isNotNull(o2.getStartDate())) {
                                    if (o1.getStartDate().isBefore(o2.getStartDate())) {
                                        return 1;
                                    } else if (o1.getStartDate().isAfter(o2.getStartDate())) {
                                        return -1;
                                    } else {
                                        return 0;
                                    }
                                } else {
                                    //# the one without start date is smaller
                                    return -1;
                                }
                            } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                                //the one without start date is smaller
                                return 1;
                            }
                        } else {
                            return -1;
                        }
                    } else if (ObjectUtil.isNotNull(o2.getCurrent()) && o2.getCurrent()) {
                        return 1;
                    }
                    //if neither is current, use end_date
                    if (ObjectUtil.isNotNull(o1.getEndDate())) {
                        if (ObjectUtil.isNotNull(o2.getEndDate())) {
                            if (o1.getEndDate().isBefore(o2.getEndDate())) {
                                return 1;
                            } else if (o1.getEndDate().isAfter(o2.getEndDate())) {
                                return -1;
                            }
                            //if equals, use start_date
                        } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                            if (o1.getEndDate().isBefore(o2.getStartDate())) {
                                return 1;
                            } else if (ObjectUtil.isNotNull(o1.getStartDate())) {
                                if (o1.getStartDate().isAfter(o2.getStartDate())) {
                                    return -1;
                                } else {
                                    return 1;
                                }
                            } else {
                                //end_date is larger than other.start_date
                                return -1;
                            }
                        } else {
                            return -1;
                        }
                    } else if (ObjectUtil.isNotNull(o2.getEndDate())) {
                        if (ObjectUtil.isNotNull(o1.getStartDate())) {
                            if (o1.getStartDate().isAfter(o2.getEndDate())) {
                                return -1;
                            } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                                if (o1.getStartDate().isBefore(o2.getStartDate())) {
                                    return 1;
                                } else {
                                    return -1;
                                }
                            } else {
                                return 1;
                            }
                        } else {
                            return 1;
                        }
                    }
                    //if neither exists end_date, use start_date as well
                    //here, we have already dealt with one end date existing cases
                    if (ObjectUtil.isNotNull(o1.getStartDate())) {
                        if (ObjectUtil.isNotNull(o2.getStartDate())) {
                            if (o1.getStartDate().isBefore(o2.getStartDate())) {
                                return 1;
                            } else if (o1.getStartDate().isAfter(o2.getStartDate())) {
                                return -1;
                            }
                        } else {
                            return -1;
                        }
                    } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                        return 1;
                    }
                    //missing info or equals, unable to determine, return None
                    return 0;
                }
            });
        }
        return talentExperienceDTOList;
    }

    @Nullable
    private List<TalentProjectDTO> sortTalentProjects(List<TalentProjectDTO> talentProjectDTOList) {
        if (ObjectUtil.isNotNull(talentProjectDTOList)) {
            talentProjectDTOList.sort(new Comparator<TalentProjectDTO>() {
                @Override
                public int compare(TalentProjectDTO o1, TalentProjectDTO o2) {
                    //sorting date
                    if (ObjectUtil.isNotNull(o1.getCurrent()) && o1.getCurrent()) {
                        //if one is current, ignore all end_date. end_dates are bad data.
                        if (ObjectUtil.isNotNull(o2.getCurrent()) && o2.getCurrent()) {
                            if (ObjectUtil.isNotNull(o1.getStartDate())) {
                                if (ObjectUtil.isNotNull(o2.getStartDate())) {
                                    if (o1.getStartDate().isBefore(o2.getStartDate())) {
                                        return 1;
                                    } else if (o1.getStartDate().isAfter(o2.getStartDate())) {
                                        return -1;
                                    } else {
                                        return 0;
                                    }
                                } else {
                                    //# the one without start date is smaller
                                    return -1;
                                }
                            } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                                //the one without start date is smaller
                                return 1;
                            }
                        } else {
                            return -1;
                        }
                    } else if (ObjectUtil.isNotNull(o2.getCurrent()) && o2.getCurrent()) {
                        return 1;
                    }
                    //if neither is current, use end_date
                    if (ObjectUtil.isNotNull(o1.getEndDate())) {
                        if (ObjectUtil.isNotNull(o2.getEndDate())) {
                            if (o1.getEndDate().isBefore(o2.getEndDate())) {
                                return 1;
                            } else if (o1.getEndDate().isAfter(o2.getEndDate())) {
                                return -1;
                            }
                            //if equals, use start_date
                        } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                            if (o1.getEndDate().isBefore(o2.getStartDate())) {
                                return 1;
                            } else if (ObjectUtil.isNotNull(o1.getStartDate())) {
                                if (o1.getStartDate().isAfter(o2.getStartDate())) {
                                    return -1;
                                } else {
                                    return 1;
                                }
                            } else {
                                //end_date is larger than other.start_date
                                return -1;
                            }
                        } else {
                            return -1;
                        }
                    } else if (ObjectUtil.isNotNull(o2.getEndDate())) {
                        if (ObjectUtil.isNotNull(o1.getStartDate())) {
                            if (o1.getStartDate().isAfter(o2.getEndDate())) {
                                return -1;
                            } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                                if (o1.getStartDate().isBefore(o2.getStartDate())) {
                                    return 1;
                                } else {
                                    return -1;
                                }
                            } else {
                                return 1;
                            }
                        } else {
                            return 1;
                        }
                    }
                    //if neither exists end_date, use start_date as well
                    //here, we have already dealt with one end date existing cases
                    if (ObjectUtil.isNotNull(o1.getStartDate())) {
                        if (ObjectUtil.isNotNull(o2.getStartDate())) {
                            if (o1.getStartDate().isBefore(o2.getStartDate())) {
                                return 1;
                            } else if (o1.getStartDate().isAfter(o2.getStartDate())) {
                                return -1;
                            }
                        } else {
                            return -1;
                        }
                    } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                        return 1;
                    }
                    //missing info or equals, unable to determine, return None
                    return 0;
                }
            });
        }
        return talentProjectDTOList;
    }

    @Override
    public TalentDTOV3 sortTalentDTO(TalentDTOV3 talentDTOV3) {
        talentDTOV3.setExperiences(sortTalentExperiences(talentDTOV3.getExperiences()));
        talentDTOV3.setEducations(sortTalentEducations(talentDTOV3.getEducations()));
        talentDTOV3.setProjects(sortTalentProjects(talentDTOV3.getProjects()));
        return talentDTOV3;
    }

    @Override
    public List<ClientContactCompany> checkAddedToCompanyContacts(Long id) {
        List<TalentContact> allContact = talentContactRepository.findAllByTalentIdAndStatus(id, TalentContactStatus.AVAILABLE);
        if (allContact.stream().noneMatch(talentContact ->
                ContactType.PRIMARY_PHONE.equals(talentContact.getType())
                || ContactType.PHONE.equals(talentContact.getType())
                || ContactType.PRIMARY_EMAIL.equals(talentContact.getType())
                || ContactType.EMAIL.equals(talentContact.getType()))) {
            throw new CustomParameterizedException(BAD_REQUEST.getStatusCode(), "Contact is required", "候选人联系方式为必填项，请在候选人编辑页补充后再次关联。", null);
        }
        TalentV3 talentV3 = talentRepository.findById(id).orElseThrow(() -> new CustomParameterizedException("The talent dose not exist"));
        TalentAdditionalInfo talentAdditionalInfo = talentV3.getTalentAdditionalInfo();
        if(talentAdditionalInfo == null) {
            return new ArrayList<>();
        }
        FindAllByNamesDTO dto = new FindAllByNamesDTO();
        dto.setCompanyNames(extractCompanyNames(talentAdditionalInfo.getExtendedInfo()));
        dto.setExcludeTalentId(id);
        ResponseEntity<List<ClientContactCompany>> response = companyService.findByNames(dto);
        return response.getBody();
    }

    @Autowired
    private RecommendedReportTemplateRepository recommendedReportTemplateRepository;

    @Override
    public List<RecommendedTemplateDTO> getRecommendedReportTemplate(Long talentId) {
        List<TalentResumeRelation> talentResumeList = talentResumeRelationRepository.findAllByTalentId(talentId);
        List<RecommendedReportTemplate> all = recommendedReportTemplateRepository.findAll();
        List<RecommendedTemplateDTO> ret = all.stream().map(r -> {
            RecommendedTemplateDTO out = new RecommendedTemplateDTO();
            out.setId(RecommendTemplateType.TEMPLATE.name() + ":" + r.getId() + ":"  + r.getReportLanguage() + ":" + r.getReportType());
            out.setName(r.getName());
            return out;
        }).collect(Collectors.toList());
        ret.addAll(talentResumeList.stream().flatMap(r -> {
            String fileName = r.getFileName();
            if(fileName == null) {
                fileName = "undefined";
            }
            if (fileName.endsWith(".doc") || fileName.endsWith(".docx")) {
                return getTemplateFileNameList(fileName, r.getResumeId(), ReportTypeEnum.DOC.getDbValue()).stream();
            } else if (fileName.endsWith(".pdf")) {
                return getTemplateFileNameList(fileName, r.getResumeId(), ReportTypeEnum.PDF.getDbValue()).stream();
            }
            return new ArrayList<RecommendedTemplateDTO>().stream();
        }).collect(Collectors.toList()));
        return ret;
    }

    private List<RecommendedTemplateDTO> getTemplateFileNameList(String fileName, Long resumeId, int templateTypeID) {
        List<RecommendedTemplateDTO> out = new ArrayList<>();
        RecommendedTemplateDTO chinese = new RecommendedTemplateDTO();
        chinese.setId(RecommendTemplateType.RESUME.name() + ":" + resumeId + ":" + LanguageEnum.CHINESE.getDbValue() + ":" + templateTypeID);
        chinese.setName(fileName + "(中文模版)");
        out.add(chinese);

        RecommendedTemplateDTO english = new RecommendedTemplateDTO();
        english.setId(RecommendTemplateType.RESUME.name() + ":" + resumeId + ":" + LanguageEnum.ENGLISH.getDbValue() + ":" + templateTypeID);
        english.setName(fileName + "(英文模版)");
        out.add(english);
        return out;
    }


    @Data
    public static class RecommendTemplate {
        private RecommendTemplateType recommendTemplateType;
        private Long id;
        private LanguageEnum languageEnum;
        private ReportTypeEnum reportTypeEnum;
    }

    @Resource
    private UserRepository userRepository;

    @Override
    public boolean deleteTalentResumeRelation(Long talentId) {
        if(talentId == null) {
            return false;
        }
        List<TalentResumeRelation> talentResumeRelationList = talentResumeRelationRepository.findAllByTalentId(talentId);
        if(org.springframework.util.CollectionUtils.isEmpty(talentResumeRelationList)) {
            return false;
        }
        talentResumeRelationList.forEach(t -> t.setStatus(CommonDataStatus.INVALID));
        talentResumeRelationRepository.saveAllAndFlush(talentResumeRelationList);
        return true;
    }


    private void writeTitleLine(Document document, String title) throws DocumentException {
        Paragraph emptyBefore = new Paragraph("");
        emptyBefore.setSpacingBefore(20);
        document.add(emptyBefore);

        PdfPTable table = new PdfPTable(1);
        table.setWidthPercentage(100);
        table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        table.getDefaultCell().setBackgroundColor(new BaseColor(213, 229, 250));
        table.getDefaultCell().setFixedHeight(30);
        table.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setVerticalAlignment(Element.ALIGN_MIDDLE);
        table.addCell(new Paragraph(ReportTemplateConstants.PREFIX_EMPTY + title, boldFont));
        document.add(table);
    }

    Font normalFont = FontFactory.getFont(ReportTemplateConstants.PINGFANG_FONT, BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 10, -1, BaseColor.BLACK);
    Font boldFont = new Font(normalFont.getBaseFont(), 10, Font.BOLD, BaseColor.BLACK);
    Font greyNormalFont = new Font(normalFont.getBaseFont(), 10, Font.NORMAL, BaseColor.GRAY);





    private static final String paraSplit = "⦁";
    private static final String enterSplit = "\n";



    private final int RESUME_MAX_LENGTH = 2600;
    private final int JD_MAX_LENGTH = 1200;
    private final List<String> removeResumeKeyOrder = List.of("projects", "experiences", "educations", "skills");


    private List<String> extractCompanyNames(String extendedInfo) {
        List<String> companyNames = new ArrayList<>();
        if (StringUtils.isNotBlank(extendedInfo)) {
            JSONObject jsonObject = new JSONObject(extendedInfo);
            cn.hutool.json.JSONArray experiences = jsonObject.getJSONArray("experiences");
            if(experiences != null) {
                experiences.forEach(experience -> {
                    JSONObject experienceJson = (JSONObject) experience;
                    if(experienceJson.containsKey("current") && experienceJson.getBool("current")) {
                        companyNames.add(experienceJson.getStr("companyName"));
                    }
                });
            }
        }
        return companyNames;
    }

    @Override
    public List<ExcelTalentProcessVo> findTalentProgressByTaskIds(List<String> taskIdList) {
        if (CollUtil.isEmpty(taskIdList)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_FINDTALENTPROGRESSBYTASKIDS_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        List<ExcelTalentProcessVo> talentProcessVoList = new ArrayList<>();
        try (Jedis jedis = commonRedisService.getJedisParserInstance()) {
            Pipeline pipeline = jedis.pipelined();
            for (String taskId : taskIdList) {
                // 检查任务是否都存在
                pipeline.exists(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA);
            }
            List<Object> resultObjectList = pipeline.syncAndReturnAll();
            List<String> notFountTaskIdList = new ArrayList<>();
            IntStream.range(0, taskIdList.size()).forEach(index -> {
                Object object = resultObjectList.get(index);
                if (BooleanUtil.isFalse((Boolean) object)) {
                    //找不到的 taskId, 需要补全数据, 前端使用
                    notFountTaskIdList.add(taskIdList.get(index));
                } else {
                    //获取数据 填充任务详情, 分为 metadata 和 data
                    ExcelTalentProcessVo excelTalentProcessVo = getExcelTalentProcessVo(taskIdList.get(index), jedis, false);
                    talentProcessVoList.add(excelTalentProcessVo);
                }
            });
            // init task
            fillNotFoundTaskDetail(notFountTaskIdList, talentProcessVoList);
        } catch (Exception e) {
            log.error("find talent progress is error, msg = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_COMMON_INTERNALERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        return talentProcessVoList;
    }

    private ExcelTalentProcessVo getExcelTalentProcessVo(String taskId, Jedis jedis, Boolean includeSuccessfulList) {
        Map<String, String> metadataMap = jedis.hgetAll(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA);
        List<String> dataList = jedis.lrange(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_DATA, 0, -1);
        ExcelTalentProcessVo excelTalentProcessVo = BeanUtil.mapToBean(metadataMap, ExcelTalentProcessVo.class, true);
        excelTalentProcessVo.setUuid(taskId);
        if (CollUtil.isNotEmpty(dataList)) {
            List<TalentFailReasonVo> failReasonVoList = new ArrayList<>();
            for (String data : dataList) {
                TalentFailReasonVo failReasonVo = JSONUtil.toBean(data, TalentFailReasonVo.class);
                failReasonVoList.add(failReasonVo);
            }
            //去重, 只有很少的机会出现, 停机等情况
            List<TalentFailReasonVo> uniqueTalentList = new ArrayList<>(failReasonVoList.stream()
                    .collect(Collectors.toMap(TalentFailReasonVo::getIndex, vo -> vo, (existing, replacement) -> existing)).values());
            excelTalentProcessVo.setFailList(uniqueTalentList);
        }
        if (Boolean.TRUE.equals(includeSuccessfulList)) {
            Set<String> successSet = jedis.smembers(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_SUCCESS_DATA);
            excelTalentProcessVo.setSuccessList(new ArrayList<>(successSet));
        }
        return excelTalentProcessVo;
    }

    private void fillNotFoundTaskDetail(List<String> initTaskIdList, List<ExcelTalentProcessVo> talentProcessVoList) {
        if (CollUtil.isEmpty(initTaskIdList)) {
            return;
        }
        for (String taskId : initTaskIdList) {
            String[] taskIdArray = taskId.split("-");
            String uuid = taskIdArray[0];
            CloudFileObjectMetadata cloudFileObject = storeService.getFileDetailWithoutFileFromS3(uuid, UploadTypeEnum.ADDRESS_LIST.getKey()).getBody();
            if (cloudFileObject == null) {
                log.error("excel not fount by taskId = {}", uuid);
                continue;
            }
            ExcelTalentProcessVo excelTalentProcessVo = new ExcelTalentProcessVo();
            excelTalentProcessVo.setUuid(taskId);
            excelTalentProcessVo.setStatus(com.altomni.apn.common.domain.enumeration.talent.TalentExcelStatusEnum.NOT_FOUND.name());
            excelTalentProcessVo.setFileName(cloudFileObject.getFileName());
            talentProcessVoList.add(excelTalentProcessVo);

        }
    }

    @Override
    public void updateTaskStatus(UpdateExcelTaskDto excelTaskDto) {
        try (Jedis jedis = commonRedisService.getJedisParserInstance()){
            List<String> taskIdList = excelTaskDto.getTaskIdList();
            // 取消任务
            if (com.altomni.apn.common.domain.enumeration.talent.TalentExcelStatusEnum.TERMINATE == excelTaskDto.getType()) {
                for (String taskId : taskIdList) {
                    // 检查任务是否已经结束, 小概率事件
                    if (BooleanUtil.isFalse(jedis.exists(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA))) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_UPDATETASKSTATUS_TASKFINISH.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
                    }
                    String[] taskIdArray = taskId.split("-");
                    // apn 设置任务终止标识
                    if (jedis.exists(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA)) {
                        jedis.hset(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA, RedisConstants.METADATA_KEY_STATUS, com.altomni.apn.common.domain.enumeration.talent.TalentExcelStatusEnum.TERMINATE.name());
                    }
                    // parser 设置任务终止标识
                    if (jedis.exists(RedisConstants.DATA_KEY_EXCEL_PARSER + taskIdArray[0] + RedisConstants.DATA_KEY_METADATA)) {
                        jedis.hset(RedisConstants.DATA_KEY_EXCEL_PARSER + taskIdArray[0] + RedisConstants.DATA_KEY_METADATA, "terminate", "1");
                    }
                }
            }
            //重新触发
            if (com.altomni.apn.common.domain.enumeration.talent.TalentExcelStatusEnum.NOT_STARTED == excelTaskDto.getType()) {
                for (String taskId : taskIdList) {
                    startOrRestartTask(taskId, true, jedis);
                }
            }
            //第一次开始, 文件上传成功后开始任务, 如果在上传链接直接开始任务会出现 parser还没有开始解析,s3上面还没有文件的情况
            if (com.altomni.apn.common.domain.enumeration.talent.TalentExcelStatusEnum.ADD_TASK == excelTaskDto.getType()) {
                for (String taskId : taskIdList) {
                    startOrRestartTask(taskId, false, jedis);
                }
            }
        }
    }

    private void startOrRestartTask(String taskId, boolean isRestart, Jedis jedis) {
        String[] taskIdArray = taskId.split("-");
        String hotListId = null;
        if (Objects.equals(taskIdArray.length, 3)) {
            hotListId = taskIdArray[2];
        }
        // 重新开始任务触发sqs
        createTalentByExcelChannel.initParserRedisAndSendSqlByS3(taskIdArray[0], Boolean.TRUE.equals(isRestart), true, hotListId);
        //添加任务
        jedis.lpush(applicationProperties.getCreateTalentByExcelQueue(), taskId);
    }

    /**
     * 获取 excel 的S3 上传链接
     * @param talentExcelUploadUrlDto
     * @return
     */
    @Override
    public StoreGetUploadUrlVO getUploadUrlForCreateTalentByExcel(TalentExcelUploadUrlDto talentExcelUploadUrlDto) {
        StoreGetUploadUrlVO resultVo = new StoreGetUploadUrlVO();
        try (Jedis jedis = commonRedisService.getJedisParserInstance()) {
            // 检查是否有相同的md5已经在执行任务
            String status = jedis.hget(applicationProperties.getCreateTalentByExcelProgress() + talentExcelUploadUrlDto.getTaskId() + RedisConstants.DATA_KEY_METADATA, RedisConstants.METADATA_KEY_STATUS);
            if (StrUtil.isNotBlank(status)) {
                if (!Objects.equals(status, com.altomni.apn.common.domain.enumeration.talent.TalentExcelStatusEnum.TERMINATE.name())) {
                    // 还在排队或者执行的重新设置过期时间
                    jedis.expire(applicationProperties.getCreateTalentByExcelProgress() + talentExcelUploadUrlDto.getTaskId() + RedisConstants.DATA_KEY_DATA, RedisConstants.REDIS_EXPIRE_TIME);
                    jedis.expire(applicationProperties.getCreateTalentByExcelProgress() + talentExcelUploadUrlDto.getTaskId() + RedisConstants.DATA_KEY_METADATA, RedisConstants.REDIS_EXPIRE_TIME);
                    jedis.expire(applicationProperties.getCreateTalentByExcelProgress() + talentExcelUploadUrlDto.getTaskId() + RedisConstants.DATA_KEY_SUCCESS_DATA, RedisConstants.REDIS_EXPIRE_TIME);
                } else {
                    // 已经被取消的, 需要重新触发sqs
                    createTalentByExcelChannel.initParserRedisAndSendSqlByS3(talentExcelUploadUrlDto.getUuid(), true, true, talentExcelUploadUrlDto.getHotListId());
                    //添加任务
                    jedis.lpush(applicationProperties.getCreateTalentByExcelQueue(), talentExcelUploadUrlDto.getTaskId());
                }
                resultVo.setUuid(talentExcelUploadUrlDto.getTaskId());
                resultVo.setStatus(ParseStatus.EDIT.name());
                return resultVo;
            }
            // 判断 parser redis 中是否有任务，而且是未完成的状态
            String parserStatus = jedis.get(RedisConstants.DATA_KEY_EXCEL_PARSER + talentExcelUploadUrlDto.getUuid() + RedisConstants.DATA_KEY_STATUS);
            if (StrUtil.isNotBlank(parserStatus)) {
                //存在 parser 解析结果, 需要添加对应的任务即可
                createTalentByExcelChannel.initParserRedisAndSendSqlByS3(talentExcelUploadUrlDto.getUuid(), false, true, talentExcelUploadUrlDto.getHotListId());
                jedis.lpush(applicationProperties.getCreateTalentByExcelQueue(), talentExcelUploadUrlDto.getTaskId());
                resultVo.setUuid(talentExcelUploadUrlDto.getTaskId());
                resultVo.setStatus(ParseStatus.EDIT.name());
                return resultVo;
            }
            if (BooleanUtil.isTrue(storeService.exists(talentExcelUploadUrlDto.getUuid(), UploadTypeEnum.ADDRESS_LIST.getKey()).getBody())) {
                return sendSqlByTalentExcelUploadUrlDto(talentExcelUploadUrlDto, jedis, resultVo);
            }
            //init redis for parser
            createTalentByExcelChannel.initParserRedisAndSendSqlByUploadUrlDto(talentExcelUploadUrlDto);
            //这个地方不直接开启任务, 存在S3 上传失败的情况
            //post policy
            UploadUrlDto uploadUrlDto = new UploadUrlDto();
            BeanUtil.copyProperties(talentExcelUploadUrlDto, uploadUrlDto);
            resultVo = storeService.getPresignedCommonUploadUrlFromS3WithPostPolicy(uploadUrlDto).getBody();
            resultVo.setStatus(ParseStatus.NONE.name());
            return resultVo;
        } catch (Exception e) {
            log.error("getUploadUrlForCreateTalentByExcel is error, msg = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_COMMON_INTERNALERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
    }

    private StoreGetUploadUrlVO sendSqlByTalentExcelUploadUrlDto(TalentExcelUploadUrlDto talentExcelUploadUrlDto, Jedis jedis, StoreGetUploadUrlVO resultVo) {
        createTalentByExcelChannel.initParserRedisAndSendSqlByS3(talentExcelUploadUrlDto.getUuid(), true, true, talentExcelUploadUrlDto.getHotListId());
        //添加任务, apn 开始去paser set 的redis 获取数据创建talent
        jedis.lpush(applicationProperties.getCreateTalentByExcelQueue(), talentExcelUploadUrlDto.getTaskId());
        resultVo.setUuid(talentExcelUploadUrlDto.getTaskId());
        resultVo.setStatus(ParseStatus.EDIT.name());
        return resultVo;
    }

    @Override
    public void downloadCreateTalentResultByExcel(ExcelTaskDto excelTaskDto, HttpServletResponse httpServletResponse) {
        if (CollUtil.isEmpty(excelTaskDto.getTaskIdList())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_FINDTALENTPROGRESSBYTASKIDS_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        List<String> taskIdList = excelTaskDto.getTaskIdList();
        // 目前只支持单个的处理,多个的需要使用 zip来处理,或者循环处理
        String taskId = taskIdList.get(0);
        String uuid = taskId.split("-")[0];
        CloudFileObjectMetadata cloudFileObjectMetadata = storeService.getFileFromS3(uuid, UploadTypeEnum.ADDRESS_LIST.getKey()).getBody();
        if (cloudFileObjectMetadata == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_DOWNLOADCREATETALENTRESULTBYEXCEL_CLOUDFILEOBJECTMETADATANULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        try (Jedis jedis = commonRedisService.getJedisParserInstance()) {
            if (BooleanUtil.isTrue(jedis.exists(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA))) {
                ExcelTalentProcessVo excelTalentProcessVo = getExcelTalentProcessVo(taskId, jedis, true);
                String status = excelTalentProcessVo.getStatus();
                if (!Objects.equals(status, com.altomni.apn.common.domain.enumeration.talent.TalentExcelStatusEnum.FINISH.name())) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_DOWNLOADCREATETALENTRESULTBYEXCEL_NOTFINISH.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
                }
                byte[] bytes = cloudFileObjectMetadata.getContent();
                String contentType = cloudFileObjectMetadata.getContentType();
                setResponseHeaderDownload(httpServletResponse, cloudFileObjectMetadata.getFileName());
                if (CSV_CONTENT_TYPE_LIST.contains(contentType)) {
                    handleCsv(bytes, httpServletResponse, excelTalentProcessVo);
                } else {
                    handerXlsxOrXls(bytes, httpServletResponse, excelTalentProcessVo);
                }
            } else {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_DOWNLOADCREATETALENTRESULTBYEXCEL_DATAEXPIRED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
            }
        } catch (CustomParameterizedException customParameterizedException) {
            throw customParameterizedException;
        } catch (Exception e) {
            log.error("下载根据excel创建talent失败, msg = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_COMMON_INTERNALERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
    }

    private void handerXlsxOrXls(byte[] bytes, HttpServletResponse httpServletResponse, ExcelTalentProcessVo excelTalentProcessVo) {
        try (InputStream inputStream = new ByteArrayInputStream(bytes);
             Workbook workbook = WorkbookFactory.create(inputStream);
             OutputStream outputStream = httpServletResponse.getOutputStream()) {
            Sheet sheet = workbook.getSheetAt(0);
            int startRowIndex = Integer.parseInt(StrUtil.isBlank(excelTalentProcessVo.getStartRowIndex())?"0":excelTalentProcessVo.getStartRowIndex());
            List<String> successList = excelTalentProcessVo.getSuccessList();
            Map<String, String> successMap = successList.stream().collect(Collectors.toMap(success -> success.split("-")[0], success -> success.split("-")[1], (a1, a2) -> a1));
            List<TalentFailReasonVo> failList = excelTalentProcessVo.getFailList();
            Map<String, String> failMap = failList.stream().collect(Collectors.toMap(vo -> String.valueOf(vo.getIndex()), TalentFailReasonVo::getReason, (a1, a2) -> a1));
            // addindex 表示 head 一共有多长从什么地方开始添加数据, excel 的数据列可能有列补全的情况。
            int addIndex = 0;
            int current = 0;
            for (int i = 0; i < sheet.getLastRowNum(); i++) {
                Row cell = sheet.getRow(i);
                if (cell == null) {
                    continue;
                }
                addIndex = Math.max(cell.getLastCellNum(), addIndex);
            }
            for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                if (i < startRowIndex) {
                    //开头就有空格,需要清理掉
                    continue;
                }
                Row cell = sheet.getRow(i);
                if (cell == null) {
                    cell = sheet.createRow(i);
                }
                if (Objects.equals(cell.getRowNum(), startRowIndex)) {
                    Row headerRow = cell;
                    Cell statusHeaderCell = headerRow.createCell(addIndex);
                    statusHeaderCell.setCellValue("状态");
                    Cell successLinkHeaderCell = headerRow.createCell(addIndex + 1);
                    successLinkHeaderCell.setCellValue("成功链接");
                    Cell failureReasonHeaderCell = headerRow.createCell(addIndex + 2);
                    failureReasonHeaderCell.setCellValue("失败原因");
                } else {
                    current++;
                    String index = String.valueOf(current);
                    if (BooleanUtil.isTrue(successMap.containsKey(index))) {
                        Cell statusCell = cell.createCell(addIndex);
                        statusCell.setCellValue("成功");
                        Cell successLinkCell = cell.createCell(addIndex + 1);
                        successLinkCell.setCellValue(applicationProperties.getBaseUrl() + "/candidates/detail/" + successMap.get(index));
                        Cell failureReasonCell = cell.createCell(addIndex + 2);
                        failureReasonCell.setCellValue("");
                    } else {
                        Cell statusCell = cell.createCell(addIndex);
                        statusCell.setCellValue("失败");
                        Cell successLinkCell = cell.createCell(addIndex + 1);
                        successLinkCell.setCellValue("");
                        Cell failureReasonCell = cell.createCell(addIndex + 2);
                        failureReasonCell.setCellValue(failMap.get(index));
                    }
                }
            }
            workbook.write(outputStream);
        } catch (IOException e) {
            log.info("download xls/xlsx create talent result is fail, msg = {}", ExceptionUtils.getStackTrace(e));
        }
    }

    private void handleCsv(byte[] bytes, HttpServletResponse httpServletResponse, ExcelTalentProcessVo excelTalentProcessVo) {
        String encoding = detectCsvEncoding(bytes);
        if (StrUtil.isBlank(encoding)) {
            encoding = "utf-8";
        }
        try (InputStream inputStream = new ByteArrayInputStream(bytes);
             CSVReader reader = new CSVReader(new InputStreamReader(inputStream, encoding));
             CSVWriter writer = new CSVWriter(new OutputStreamWriter(httpServletResponse.getOutputStream(), encoding))) {
            List<String> successList = excelTalentProcessVo.getSuccessList();
            Map<String, String> successMap = successList.stream().collect(Collectors.toMap(success -> success.split("-")[0], success -> success.split("-")[1], (a1, a2) -> a1));
            List<TalentFailReasonVo> failList = excelTalentProcessVo.getFailList();
            Map<String, String> failMap = failList.stream().collect(Collectors.toMap(vo -> String.valueOf(vo.getIndex()), TalentFailReasonVo::getReason, (a1, a2) -> a1));
            List<String[]> lines = reader.readAll();
            // 处理CSV数据
            List<String[]> mewLines = reader.readAll();
            int current = 0;
            int startRowIndex = Integer.parseInt(StrUtil.isBlank(excelTalentProcessVo.getStartRowIndex())?"0":excelTalentProcessVo.getStartRowIndex());
            for (int i = 0; i < lines.size(); i++) {
                if (i < startRowIndex) {
                    //开头就有空格,需要清理掉
                    continue;
                }
                String[] line = lines.get(i);
                String[] newLine;
                if (Objects.equals(i, startRowIndex)) {
                    newLine = ArrayUtil.append(line, "状态", "成功链接", "失败原因");
                } else {
                    current++;
                    String index = String.valueOf(current);
                    if (BooleanUtil.isTrue(successMap.containsKey(index))) {
                        newLine = ArrayUtil.append(line, "成功", applicationProperties.getBaseUrl() + "/candidates/detail/" + successMap.get(index), "");
                    } else {
                        newLine = ArrayUtil.append(line, "失败", "", failMap.get(index));
                    }
                }
                mewLines.add(newLine);
            }
            writer.writeAll(mewLines);
            writer.flush();
        } catch (Exception e) {
            log.info("download csv create talent result is fail, msg = {}", ExceptionUtils.getStackTrace(e));
        }
    }

    private String detectCsvEncoding(byte[] bytes) {
        try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
            UniversalDetector detector = new UniversalDetector(null);
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) > 0 && !detector.isDone()) {
                detector.handleData(buffer, 0, bytesRead);
            }
            detector.dataEnd();
            String detectedCharset = detector.getDetectedCharset();
            detector.reset();
            return detectedCharset;
        } catch (Exception e) {
            e.printStackTrace();
            return StandardCharsets.UTF_8.name(); // Return a default if detection fails
        }
    }

    void setResponseHeaderDownload(HttpServletResponse response, String fileName) {
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        response.setHeader("charset", "utf-8");
        response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
    }


    @Override
    public TalentSimilarityDto convertTalentInfoInputToSimilarityDTO(TalentInfoInput input){
        removeEnumsNull(input);
        TalentDTOV3 talentDTO = talentInfoMapper.toTalentDTO(input);
        setTalentDefaultValue(talentDTO);
        TalentSimilarityDto talentSimilarityDto = new TalentSimilarityDto();
        BeanUtil.copyProperties(talentDTO, talentSimilarityDto);
        return talentSimilarityDto;
    }

    @Override
    public void upsertExperience(Long talentId, TalentExperienceDTO talentExperienceDTO) {
        Optional<TalentAdditionalInfo> optionalTalentAdditionalInfo = talentRepository.getTalentAdditionalInfo(talentId);
        if (optionalTalentAdditionalInfo.isPresent()){
            TalentAdditionalInfo talentAdditionalInfo = optionalTalentAdditionalInfo.get();
            String extendedInfo = talentAdditionalInfo.getExtendedInfo();
            if (StringUtils.isBlank(extendedInfo)){
                extendedInfo= "{}";
            }
            JSONObject extendedInfoObj = JSONUtil.parseObj(extendedInfo, true);
            List<TalentExperienceDTO> existingExperiences = extendedInfoObj.containsKey("experiences") ?
                    extendedInfoObj.getJSONArray("experiences").toList(TalentExperienceDTO.class) : new ArrayList<>();
            List<TalentExperienceDTO> sortExperience = this.upsertAndSortedExperiences(existingExperiences, talentExperienceDTO);
            extendedInfoObj.put("experiences", sortExperience);
            talentAdditionalInfo.setExtendedInfo(JSONUtil.toJsonStr(extendedInfoObj));
            talentAdditionalInfoRepository.save(talentAdditionalInfo);
            talentRepository.updateTalentLastEditedTime(talentId);
        }else {
            JSONObject extendedInfoObj = new JSONObject().put("experiences", List.of(talentExperienceDTO));
            TalentAdditionalInfo talentAdditionalInfo = talentAdditionalInfoRepository.save(new TalentAdditionalInfo(JSONUtil.toJsonStr(extendedInfoObj)));
            talentRepository.updateTalenAdditionalInfoIdById(talentId, talentAdditionalInfo.getId());
        }
    }

    private List<TalentExperienceDTO> getExperiencesFromExtendedInfo(String extendedInfo){
        if (StringUtils.isBlank(extendedInfo)){
            extendedInfo= "{}";
        }
        JSONObject extendedInfoObj = JSONUtil.parseObj(extendedInfo);
        return extendedInfoObj.containsKey("experiences") ?
                extendedInfoObj.getJSONArray("experiences").toList(TalentExperienceDTO.class) : new ArrayList<>();
    }

    private List<TalentExperienceDTO> upsertAndSortedExperiences(List<TalentExperienceDTO> existingExperiences, TalentExperienceDTO newExperience){
        List<TalentExperienceDTO> mergedResult = new ArrayList<>(List.of(newExperience));
        for (TalentExperienceDTO talentExperience : existingExperiences) {
            if (Objects.isNull(talentExperience)){
                continue;
            }
            if (this.isAvailableToAppendExperience(talentExperience, newExperience)){
                mergedResult.add(talentExperience);
            }
        }
        return mergedResult.stream().sorted(TalentExperienceDTO.COMPARATOR).collect(Collectors.toList());
    }

    private boolean isAvailableToAppendExperience(TalentExperienceDTO existingExperience, TalentExperienceDTO newExperience){
        // 如果是同一个application，则用新的experience替换
        if (Objects.nonNull(existingExperience.getTalentRecruitmentProcessId())
                && existingExperience.getTalentRecruitmentProcessId().equals(newExperience.getTalentRecruitmentProcessId())){
            return false;
        }
        // 原有的experience可能存储着还未到达start date的工作经历，这些前端不会传过来，为了防止丢失，需要加进来
        if (Objects.nonNull(existingExperience.getStartDate()) && existingExperience.getStartDate().isAfter(LocalDate.now())){
            return true;
        }
        // 如果存在同公司同职位，并且入职年月也相同的话，则替换原来的工作经历
        return !Objects.equals(existingExperience.getTitle(), newExperience.getTitle())
                || !Objects.equals(existingExperience.getCompanyName(), newExperience.getCompanyName())
                || !this.isSaveYearAndMonth(existingExperience.getStartDate(), newExperience.getStartDate());
    }

    private boolean isSaveYearAndMonth(LocalDate date1, LocalDate date2){
        if (Objects.isNull(date1) && Objects.isNull(date2)){
            return true;
        }
        return Objects.nonNull(date1)
                && Objects.nonNull(date2)
                && date1.getYear() == date2.getYear()
                && date1.getMonth() == date2.getMonth();
    }

    @Override
    public void deleteExperienceByTalentRecruitmentProcessId(Long talentId, Long talentRecruitmentProcessId) {
        Optional<TalentAdditionalInfo> optionalTalentAdditionalInfo = talentRepository.getTalentAdditionalInfo(talentId);
        if (optionalTalentAdditionalInfo.isEmpty()){
            return;
        }
        TalentAdditionalInfo talentAdditionalInfo = optionalTalentAdditionalInfo.get();
        String extendedInfo = talentAdditionalInfo.getExtendedInfo();
        if (StringUtils.isBlank(extendedInfo)){
            return;
        }
        JSONObject extendedInfoObj = JSONUtil.parseObj(extendedInfo);
        if (!extendedInfoObj.containsKey("experiences")){
            return;
        }
        List<TalentExperienceDTO> newExperiences = new ArrayList<>();
        cn.hutool.json.JSONArray experienceJsonArray = extendedInfoObj.getJSONArray("experiences");
        for (Object ex : experienceJsonArray) {
            TalentExperienceDTO talentExperience = JSONUtil.toBean(ex.toString(), TalentExperienceDTO.class);
            if (Objects.isNull(talentExperience)){
                continue;
            }
            // 如果是同一个application，则用新的experience替换
            if (Objects.nonNull(talentExperience.getTalentRecruitmentProcessId())
                    && talentExperience.getTalentRecruitmentProcessId().equals(talentRecruitmentProcessId)){
                continue;
            }
            newExperiences.add(talentExperience);
        }
        if (newExperiences.size() != experienceJsonArray.size()){
            List<TalentExperienceDTO> sortExperience = newExperiences.stream().sorted(TalentExperienceDTO.COMPARATOR).collect(Collectors.toList());
            extendedInfoObj.put("experiences", sortExperience);
            talentAdditionalInfo.setExtendedInfo(JSONUtil.toJsonStr(extendedInfoObj));
            talentAdditionalInfoRepository.save(talentAdditionalInfo);
            talentRepository.updateTalentLastEditedTime(talentId);
        }
    }
    @Override
    public Set<TalentBriefVO> getAllBriefTalentsByTalentIds(Set<Long> talentIds) {
        return talentRepository.getAllBriefTalentsByIds(talentIds);
    }

}
