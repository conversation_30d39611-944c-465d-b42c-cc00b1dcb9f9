package com.altomni.apn.talent.service.tracking.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.enumeration.tracking.*;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinGroup;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinGroupMember;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinMessageTemplate;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinPending;
import com.altomni.apn.talent.repository.tracking.TalentTrackingLinkedinGroupMemberRepository;
import com.altomni.apn.talent.repository.tracking.TalentTrackingLinkedinGroupRepository;
import com.altomni.apn.talent.repository.tracking.TalentTrackingLinkedinMessageTemplateRepository;
import com.altomni.apn.talent.repository.tracking.TalentTrackingLinkedinPendingRepository;
import com.altomni.apn.talent.service.dto.tracking.*;
import com.altomni.apn.talent.service.tracking.TalentTrackingService;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingGroupVO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingTemplateVO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.ws.rs.ForbiddenException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TalentTrackingServiceImpl implements TalentTrackingService {

    @Resource
    private TalentTrackingLinkedinPendingRepository talentTrackingLinkedinPendingRepository;

    @Resource
    private TalentTrackingLinkedinMessageTemplateRepository talentTrackingLinkedinMessageTemplateRepository;

    @Resource
    private TalentTrackingLinkedinGroupRepository talentTrackingLinkedinGroupRepository;

    @Resource
    private TalentTrackingLinkedinGroupMemberRepository talentTrackingLinkedinGroupMemberRepository;

    //没有软删的人脉status
    private static final List<TrackingStatus> ACTIVE_TRACKING_STATUS = Arrays.asList(TrackingStatus.NOT_SENT, TrackingStatus.SENT);


    @Override
    public TalentTrackingVO saveTalentTracking(TalentTrackingDTO talentTrackingDTO) {
        TalentTrackingLinkedinPending talentTrackingLinkedinPending = talentTrackingLinkedinPendingRepository.findFirstByTenantIdAndCategoryAndStatusInAndOperatorLinkedinIdAndTalentLinkedinId(SecurityUtils.getTenantId(), talentTrackingDTO.getCategory(), ACTIVE_TRACKING_STATUS, talentTrackingDTO.getOperatorLinkedinId(), talentTrackingDTO.getTalentLinkedinId());
        if (ObjectUtil.isNotEmpty(talentTrackingLinkedinPending)) {
            throw new CustomParameterizedException("talent is tracking.");
        }
        // 查询有没有在三周内撤回过此用户的记录, 如果有的话需要回填一下撤回的时间，便于前端控制不允许再次添加好友
        Optional<TalentTrackingLinkedinPending> lastInactiveTrackingOpt = talentTrackingLinkedinPendingRepository.findAllByTenantIdAndStatusInAndOperatorLinkedinIdAndTalentLinkedinIdIn(SecurityUtils.getTenantId(),
                        List.of(TrackingStatus.IN_ACTIVE), talentTrackingDTO.getOperatorLinkedinId(), List.of(talentTrackingDTO.getTalentLinkedinId()))
                .stream().filter(track -> track.getLastInteractionTime() != null)
                .max(Comparator.comparing(TalentTrackingLinkedinPending::getLastInteractionTime))
                .filter(track -> track.getLastInteractionTime().plus(21, ChronoUnit.DAYS).isAfter(Instant.now()));

        talentTrackingLinkedinPending = TalentTrackingLinkedinPending.fromTalentTrackingDTO(talentTrackingDTO);
        talentTrackingLinkedinPending.setStatus(TrackingStatus.NOT_SENT);
        talentTrackingLinkedinPending.setTenantId(SecurityUtils.getTenantId());
        if (lastInactiveTrackingOpt.isPresent()) {
            talentTrackingLinkedinPending.setLastInteractionTime(lastInactiveTrackingOpt.get().getLastInteractionTime());
        }
        talentTrackingLinkedinPending = talentTrackingLinkedinPendingRepository.save(talentTrackingLinkedinPending);
        return TalentTrackingLinkedinPending.toTalentTrackingVO(talentTrackingLinkedinPending);
    }

    @Override
    public Page<TalentTrackingLinkedinPending> searchTalentTracking(TalentTrackingSearchDTO talentTrackingSearchDTO, Pageable pageable) {
        return talentTrackingLinkedinPendingRepository.findAll(queryAllTalentTrackingLinkedinPending(SecurityUtils.getTenantId(), talentTrackingSearchDTO.getCategory(), talentTrackingSearchDTO.getStatus(), talentTrackingSearchDTO.getOperatorLinkedinId()), pageable);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inactiveTalentTracking(TalentTrackingManageDTO talentTrackingManageDTO) {
        List<TalentTrackingLinkedinPending> talentTrackingLinkedinPendingList = queryTalentTrackingLinkedinPending(talentTrackingManageDTO.getIds(), talentTrackingManageDTO.getOperatorLinkedinId());
        talentTrackingLinkedinPendingList.forEach(item -> item.setStatus(TrackingStatus.IN_ACTIVE));
        talentTrackingLinkedinPendingRepository.saveAll(talentTrackingLinkedinPendingList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TalentTrackingVO> sentTalentTracking(TalentTrackingManageDTO talentTrackingManageDTO) {
        List<TalentTrackingLinkedinPending> talentTrackingLinkedinPendingList = queryTalentTrackingLinkedinPending(talentTrackingManageDTO.getIds(), talentTrackingManageDTO.getOperatorLinkedinId());
        List<String> talentLinkedinIds = talentTrackingLinkedinPendingList.stream().map(TalentTrackingLinkedinPending::getTalentLinkedinId).toList();
        // 根据候选人领英 id 再次查询，因为同一个候选人可能同时在链接加人列表和待加列表中，同一个候选人都需要设置成已发送状态
        List<TalentTrackingLinkedinPending> realPendingList = talentTrackingLinkedinPendingRepository.findAllByTenantIdAndStatusInAndOperatorLinkedinIdAndTalentLinkedinIdIn(SecurityUtils.getTenantId(),
                ACTIVE_TRACKING_STATUS, talentTrackingManageDTO.getOperatorLinkedinId(), talentLinkedinIds);
        realPendingList.forEach(item -> {
            item.setStatus(TrackingStatus.SENT);
            item.setLastInteractionTime(Instant.now());
        });
        talentTrackingLinkedinPendingRepository.saveAll(realPendingList);
        return realPendingList.stream().map(TalentTrackingLinkedinPending::toTalentTrackingVO).toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TalentTrackingVO> retractSentTalentTracking(TalentTrackingManageDTO talentTrackingManageDTO) {
        List<TalentTrackingLinkedinPending> talentTrackingLinkedinPendingList = queryTalentTrackingLinkedinPendingByTalentLinkedinIds(talentTrackingManageDTO.getTalentLinkedinIds(), talentTrackingManageDTO.getOperatorLinkedinId());
        talentTrackingLinkedinPendingList.forEach(item -> {
            item.setStatus(TrackingStatus.NOT_SENT);
            item.setLastInteractionTime(Instant.now());
        });
        talentTrackingLinkedinPendingRepository.saveAll(talentTrackingLinkedinPendingList);
        return talentTrackingLinkedinPendingList.stream().map(TalentTrackingLinkedinPending::toTalentTrackingVO).toList();
    }

    @Override
    public Page<TalentTrackingLinkedinMessageTemplate> searchTalentTrackingTemplate(TalentTrackingTemplateSearchDTO talentTrackingTemplateSearchD, Pageable pageable) {
        return talentTrackingLinkedinMessageTemplateRepository.findAll(queryAllTalentTrackingLinkedinMessageTemplate(SecurityUtils.getTenantId(), talentTrackingTemplateSearchD.getCategory(), talentTrackingTemplateSearchD.getStatus(), SecurityUtils.getUserId()), pageable);
    }

    @Override
    public TalentTrackingTemplateVO saveTalentTrackingTemplate(TalentTrackingTemplateDTO talentTrackingTemplateDTO) {
        TalentTrackingLinkedinMessageTemplate talentTrackingLinkedinMessageTemplate = TalentTrackingLinkedinMessageTemplate.fromTalentTrackingTemplateDTO(talentTrackingTemplateDTO);
        talentTrackingLinkedinMessageTemplate.setStatus(TrackingTemplateStatus.ACTIVE);
        talentTrackingLinkedinMessageTemplate.setTenantId(SecurityUtils.getTenantId());
        talentTrackingLinkedinMessageTemplate = talentTrackingLinkedinMessageTemplateRepository.save(talentTrackingLinkedinMessageTemplate);
        return TalentTrackingLinkedinMessageTemplate.toTalentTrackingTemplateVO(talentTrackingLinkedinMessageTemplate);
    }

    @Override
    public TalentTrackingTemplateVO updateTalentTrackingTemplate(Long id, TalentTrackingTemplateDTO talentTrackingTemplateDTO) {
        TalentTrackingLinkedinMessageTemplate talentTrackingLinkedinMessageTemplate = talentTrackingLinkedinMessageTemplateRepository.findById(id).orElseThrow(() -> new NotFoundException("template does not exist."));
        if (!talentTrackingLinkedinMessageTemplate.getTenantId().equals(SecurityUtils.getTenantId()) || !talentTrackingLinkedinMessageTemplate.getPermissionUserId().equals(SecurityUtils.getUserId())) {
            throw new ForbiddenException("template does not exist.");
        }
        ServiceUtils.myCopyProperties(talentTrackingTemplateDTO, talentTrackingLinkedinMessageTemplate);
        talentTrackingLinkedinMessageTemplate = talentTrackingLinkedinMessageTemplateRepository.save(talentTrackingLinkedinMessageTemplate);
        return TalentTrackingLinkedinMessageTemplate.toTalentTrackingTemplateVO(talentTrackingLinkedinMessageTemplate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inactiveTalentTrackingTemplate(TalentTrackingManageDTO talentTrackingManageDTO) {
        if (CollUtil.isEmpty(talentTrackingManageDTO.getIds())) {
            return;
        }
        List<TalentTrackingLinkedinMessageTemplate> talentTrackingLinkedinMessageTemplateList = talentTrackingLinkedinMessageTemplateRepository.findAllByIdInAndTenantIdAndStatusAndPermissionUserId(talentTrackingManageDTO.getIds(), SecurityUtils.getTenantId(), TrackingTemplateStatus.ACTIVE, SecurityUtils.getUserId());
        talentTrackingLinkedinMessageTemplateList.forEach(item -> item.setStatus(TrackingTemplateStatus.IN_ACTIVE));
        talentTrackingLinkedinMessageTemplateRepository.saveAll(talentTrackingLinkedinMessageTemplateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TalentTrackingGroupVO saveGroup(TalentTrackingGroupDTO talentTrackingGroupDTO) {
        checkDuplicateGroup(talentTrackingGroupDTO.getName(), talentTrackingGroupDTO.getOperatorLinkedinId(), null);
        TalentTrackingLinkedinGroup talentTrackingLinkedinGroup = TalentTrackingLinkedinGroup.fromTalentTrackingGroupDTO(talentTrackingGroupDTO);
        talentTrackingLinkedinGroup.setStatus(TrackingGroupStatus.ACTIVE);
        talentTrackingLinkedinGroup.setTenantId(SecurityUtils.getTenantId());
        talentTrackingLinkedinGroup = talentTrackingLinkedinGroupRepository.save(talentTrackingLinkedinGroup);
        int memberCount = saveGroupMember(talentTrackingGroupDTO.getMembers(), SecurityUtils.getTenantId(), talentTrackingLinkedinGroup.getId());

        TalentTrackingGroupVO talentTrackingGroupVO = TalentTrackingLinkedinGroup.toTalentTrackingGroupVO(talentTrackingLinkedinGroup);
        talentTrackingGroupVO.setMemberCount(memberCount);
        return talentTrackingGroupVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TalentTrackingGroupVO updateGroup(Long id, TalentTrackingGroupDTO talentTrackingGroupDTO) {
        TalentTrackingLinkedinGroup talentTrackingLinkedinGroup = talentTrackingLinkedinGroupRepository.findById(id).orElseThrow(() -> new NotFoundException("This member group does not exist."));
        if (!talentTrackingLinkedinGroup.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new ForbiddenException("This member group does not exist.");
        }
        checkDuplicateGroup(talentTrackingGroupDTO.getName(), talentTrackingLinkedinGroup.getOperatorLinkedinId(), id);
        talentTrackingLinkedinGroup.setName(talentTrackingGroupDTO.getName());
        talentTrackingLinkedinGroupRepository.save(talentTrackingLinkedinGroup);
        return toTalentTrackingGroupVO(talentTrackingLinkedinGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inactiveGroup(Long id) {
        TalentTrackingLinkedinGroup talentTrackingLinkedinGroup = talentTrackingLinkedinGroupRepository.findById(id).orElseThrow(() -> new NotFoundException("This member group does not exist."));
        if (!talentTrackingLinkedinGroup.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new ForbiddenException("This member group does not exist.");
        }

        talentTrackingLinkedinGroup.setStatus(TrackingGroupStatus.IN_ACTIVE);
        talentTrackingLinkedinGroupRepository.save(talentTrackingLinkedinGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveGroupMember(TalentTrackingGroupManageDTO talentTrackingGroupManageDTO) {
        List<Long> groupIds = talentTrackingGroupManageDTO.getGroupIds();
        List<TalentTrackingGroupMemberDTO> members = talentTrackingGroupManageDTO.getMembers();
        Long tenantId = SecurityUtils.getTenantId();

        List<TalentTrackingLinkedinGroupMember> existedMemberList = talentTrackingLinkedinGroupMemberRepository.findAllByGroupIdInAndTalentLinkedinIdIn(groupIds, members.stream().map(TalentTrackingGroupMemberDTO::getTalentLinkedinId).toList());
        Map<Long, Set<String>> existedMemberMap = existedMemberList.stream()
                .collect(Collectors.groupingBy(
                        TalentTrackingLinkedinGroupMember::getGroupId,
                        Collectors.mapping(TalentTrackingLinkedinGroupMember::getTalentLinkedinId, Collectors.toSet())
                ));

        List<TalentTrackingLinkedinGroupMember> addMemberList = new ArrayList<>();
        groupIds.forEach(groupId -> {
            Set<String> itemExistedMembers = existedMemberMap.getOrDefault(groupId, new HashSet<>());
            addMemberList.addAll(members.stream().filter(item -> !itemExistedMembers.contains(item.getTalentLinkedinId())).map(o -> TalentTrackingLinkedinGroupMember.fromTalentTrackingGroupMemberDTO(o, tenantId, groupId)).toList());
        });
        talentTrackingLinkedinGroupMemberRepository.saveAll(addMemberList);
    }

    @Override
    public Page<TalentTrackingLinkedinGroup> searchGroup(String operatorLinkedinId, Pageable pageable) {
        return talentTrackingLinkedinGroupRepository.findAllByTenantIdAndOperatorLinkedinIdAndStatus(SecurityUtils.getTenantId(), operatorLinkedinId, TrackingGroupStatus.ACTIVE, pageable);
    }

    @Override
    public List<TalentTrackingGroupVO> toTalentTrackingGroupVO(List<TalentTrackingLinkedinGroup> talentTrackingLinkedinGroupList) {
        List<TalentTrackingGroupVO> talentTrackingGroupVOList = talentTrackingLinkedinGroupList.stream().map(TalentTrackingLinkedinGroup::toTalentTrackingGroupVO).toList();
        List<TalentTrackingLinkedinGroupMember> talentTrackingLinkedinGroupMemberList = talentTrackingLinkedinGroupMemberRepository.findAllByGroupIdIn(talentTrackingLinkedinGroupList.stream().map(TalentTrackingLinkedinGroup::getId).distinct().toList());
        List<TalentTrackingVO> talentTrackingVOList = talentTrackingLinkedinGroupMemberList.stream().map(TalentTrackingLinkedinGroupMember::toTalentTrackingVO).toList();
        Map<Long, List<TalentTrackingVO>> talentTrackingVOMap = talentTrackingVOList.stream().collect(Collectors.groupingBy(TalentTrackingVO::getGroupId));
        talentTrackingGroupVOList.forEach(item -> {
            List<TalentTrackingVO> itemList = talentTrackingVOMap.getOrDefault(item.getId(), new ArrayList<>());
            item.setMembers(itemList);
            item.setMemberCount(itemList.size());
        });
        return talentTrackingGroupVOList;
    }

    @Override
    public List<TalentTrackingVO> queryGroupMember(Long id) {
        TalentTrackingLinkedinGroup talentTrackingLinkedinGroup = talentTrackingLinkedinGroupRepository.findById(id).orElseThrow(() -> new NotFoundException("This member group does not exist."));
        if (!talentTrackingLinkedinGroup.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new ForbiddenException("This member group does not exist.");
        }
        List<TalentTrackingLinkedinGroupMember> trackingLinkedinGroupMemberList = talentTrackingLinkedinGroupMemberRepository.findAllByGroupId(id);
        return trackingLinkedinGroupMemberList.stream().map(TalentTrackingLinkedinGroupMember::toTalentTrackingVO).toList();
    }

    @Override
    public List<TalentTrackingGroupVO> queryGroupByOperatorLinkedinId(String operatorLinkedinId) {
        List<TalentTrackingLinkedinGroup> talentTrackingLinkedinGroupList = talentTrackingLinkedinGroupRepository.findAllByTenantIdAndOperatorLinkedinIdAndStatusOrderByCreatedDateDesc(SecurityUtils.getTenantId(), operatorLinkedinId, TrackingGroupStatus.ACTIVE);
        return talentTrackingLinkedinGroupList.stream().map(TalentTrackingLinkedinGroup::toTalentTrackingGroupVO).toList();
    }

    private TalentTrackingGroupVO toTalentTrackingGroupVO(TalentTrackingLinkedinGroup talentTrackingLinkedinGroup) {
        TalentTrackingGroupVO talentTrackingGroupVO = TalentTrackingLinkedinGroup.toTalentTrackingGroupVO(talentTrackingLinkedinGroup);
        int memberCount = talentTrackingLinkedinGroupMemberRepository.countDistinctByGroupId(talentTrackingGroupVO.getId());
        talentTrackingGroupVO.setMemberCount(memberCount);
        return talentTrackingGroupVO;
    }

    private int saveGroupMember(List<TalentTrackingGroupMemberDTO> talentTrackingGroupMemberDTOList, Long tenantId, Long groupId) {
        if (CollUtil.isEmpty(talentTrackingGroupMemberDTOList)) {
            return 0;
        }

        List<TalentTrackingLinkedinGroupMember> talentTrackingLinkedinGroupMemberList = talentTrackingGroupMemberDTOList.stream().map(o -> TalentTrackingLinkedinGroupMember.fromTalentTrackingGroupMemberDTO(o, tenantId, groupId)).toList();
        talentTrackingLinkedinGroupMemberRepository.saveAll(talentTrackingLinkedinGroupMemberList);
        return talentTrackingLinkedinGroupMemberList.size();
    }

    //领英人员分组查重
    private void checkDuplicateGroup(String name, String operatorId, Long id) {
        // 查询状态为 active 的成员分组，检查该用户是否有重名的成员分组
        List<TalentTrackingLinkedinGroup> talentTrackingLinkedinGroupList = talentTrackingLinkedinGroupRepository
                .findAllByTenantIdAndOperatorLinkedinIdAndStatusAndName(SecurityUtils.getTenantId(), operatorId, TrackingGroupStatus.ACTIVE, name);

        if (CollUtil.isNotEmpty(talentTrackingLinkedinGroupList) && talentTrackingLinkedinGroupList.stream()
                .noneMatch(announcement -> Objects.equals(announcement.getId(), id))) {
            throw new CustomParameterizedException("This member group already exists.");
        }
    }

    private List<TalentTrackingLinkedinPending> queryTalentTrackingLinkedinPending(List<Long> ids, String operatorLinkedinId) {
        if (CollUtil.isEmpty(ids) || ObjectUtil.isEmpty(operatorLinkedinId)) {
            return new ArrayList<>();
        }
        return talentTrackingLinkedinPendingRepository.findAllByIdInAndTenantIdAndStatusInAndOperatorLinkedinId(ids, SecurityUtils.getTenantId(), ACTIVE_TRACKING_STATUS, operatorLinkedinId);
    }

    private List<TalentTrackingLinkedinPending> queryTalentTrackingLinkedinPendingByTalentLinkedinIds(List<String> talentLinkedinIds, String operatorLinkedinId) {
        if (CollUtil.isEmpty(talentLinkedinIds) || ObjectUtil.isEmpty(operatorLinkedinId)) {
            return new ArrayList<>();
        }
        return talentTrackingLinkedinPendingRepository.findAllByTenantIdAndStatusInAndOperatorLinkedinIdAndTalentLinkedinIdIn(SecurityUtils.getTenantId(), ACTIVE_TRACKING_STATUS, operatorLinkedinId, talentLinkedinIds);
    }

    private Specification<TalentTrackingLinkedinPending> queryAllTalentTrackingLinkedinPending(Long tenantId, TrackingCategory category, TrackingStatus status, String operatorLinkedinId) {
        Specification<TalentTrackingLinkedinPending> specification = new Specification<TalentTrackingLinkedinPending>() {
            @Override
            public Predicate toPredicate(Root<TalentTrackingLinkedinPending> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                Predicate predicate = criteriaBuilder.conjunction();
                predicate.getExpressions().add(criteriaBuilder.equal(root.get("tenantId"), tenantId));
                if (ObjectUtil.isNotEmpty(category)) {
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("category"), category.toDbValue()));
                }
                if (ObjectUtil.isNotEmpty(status)) {
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("status"), status.toDbValue()));
                } else {
                    //默认查未发送的待加人脉数据
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("status"), TrackingStatus.NOT_SENT.toDbValue()));
                }
                if (ObjectUtil.isNotEmpty(operatorLinkedinId)) {
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("operatorLinkedinId"), operatorLinkedinId));
                }
                return predicate;
            }
        };
        return specification;
    }

    private Specification<TalentTrackingLinkedinMessageTemplate> queryAllTalentTrackingLinkedinMessageTemplate(Long tenantId, TrackingTemplateCategory category, TrackingTemplateStatus status, Long operatorId) {
        Specification<TalentTrackingLinkedinMessageTemplate> specification = new Specification<TalentTrackingLinkedinMessageTemplate>() {
            @Override
            public Predicate toPredicate(Root<TalentTrackingLinkedinMessageTemplate> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                Predicate predicate = criteriaBuilder.conjunction();
                predicate.getExpressions().add(criteriaBuilder.equal(root.get("tenantId"), tenantId));
                if (ObjectUtil.isNotEmpty(category)) {
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("category"), category.toDbValue()));
                }
                if (ObjectUtil.isNotEmpty(status)) {
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("status"), status.toDbValue()));
                } else {
                    //默认查活跃的
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("status"), TrackingTemplateStatus.ACTIVE.toDbValue()));
                }
                if (ObjectUtil.isNotEmpty(operatorId)) {
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("permissionUserId"), operatorId));
                }
                return predicate;
            }
        };
        return specification;
    }
}
