package com.altomni.apn.common.dto.application.pipeline;

import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MyPipelineSearchParam {

    private String talentName;

    private String talentSkills;

    private String talentEmails;

    private String talentPhones;

    private String jobTitle;

    private String jobLocations;

    private String companyName;

    private List<Long> recruiter;

    private List<Long> accountManager;

    private List<Long> cooperateAccountManager;

    private List<Long> recruiterTeam;

    private List<Long> accountManagerTeam;

    private List<Long> cooperateAccountManagerTeam;

    private Long hiringManager;

    private Long hrCoordinate;

    private Long msp;

    private Instant fromDate;

    private Instant toDate;

    private MyCandidateStatusFilter nodeType;

    private List<UserRole> userRoles;

    private List<Long> recruitmentProcessIds;

    private List<String> generalText;

    // invoiceFlag  false表示未入职
    private Boolean invoiceFlag;

    private Instant submitToJobStartDate;

    private Instant submitToJobEndDate;

    private Boolean processStop;

    private Set<Long> userIds;

    private Set<ResumeSourceType> sources;

    private Set<Long> agencyIds;

    public String userRoleDBValues() {
        String s = "";
        for (UserRole userRole : this.userRoles) {
            s += "," + userRole.toDbValue();
        }
        return "(" + s.substring(1) + ")";
    }

}
