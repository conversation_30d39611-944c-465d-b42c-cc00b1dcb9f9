package com.altomni.apn.common.config.constants;

import cn.hutool.core.collection.CollUtil;

import java.util.Arrays;
import java.util.List;

/**
 * Application constants.
 * <AUTHOR>
 */
public final class ElasticSearchConstants {

    public static final String HITS = "hits";

    public static final String ID = "_id";

    public static final String TOTAL = "total";

    public static final String TOTAL_OWNED_DATA = "total-owned";

    public static final String TOTAL_NOT_OWNED_DATA = "total-not-owned";

    public static final String RESULT_TOTAL = "result-total";

    public static final String COUNT_TOTAL = "count-total";


    public static final String VALUE = "value";

    public static final String EN_SORT_KEY = ".en_sort_order";

    public static final String CN_SORT_KEY = ".cn_sort_order";

    public static final List<String> EN_OR_CH_SORT_KEY_FOR_JOB = CollUtil.newArrayList("jobFunctions", "requiredLanguages", "preferredLanguages");

    public static final List<String> EN_OR_CH_SORT_KEY_FOR_TALENT = CollUtil.newArrayList("industries", "jobFunctions", "languages");

    public static final List<String> JOB_SEARCH_SOURCE = Arrays.asList("title", "companyName", "companyId", "code", "type", "status", "postingTime", "openTime", "tenantWebsitePostingTime",
            "assignedUsers", "locations.originDisplay", "locations.originDisplayPinYin", "jobFunctions.id", "minimumDegreeLevel", "requiredSkills.skillName",
            "preferredSkills.skillName", "requiredLanguages.name", "preferredLanguages.name", "currency", "experienceYearRange", "department", "published",
            "priority", "startDate", "clientContacts.name", "allowRemote","salesLeadId", "recruitmentProcessId", "createdDate", "lastModifiedDate","sharedAgencies",
            "responsibility0","responsibility1","responsibility2", "responsibility3","responsibility4","responsibility5",
            "responsibility6", "responsibility7", "responsibility8","responsibility9","responsibility10","responsibility11",
            "responsibility12", "responsibility13", "responsibility14","responsibility15","responsibility16","responsibility17",
            "responsibility18","responsibility19","responsibility20","openings");

    public static final String JOB_SEARCH_SOURCE_AFFILIATIONS = "affiliations";

    public static final String JOB_SEARCH_SOURCE_SALES_LEAD_ID = "salesLeadId";

    public static final List<String> RELATE_JOB_FOLDER_SEARCH_SOURCE = Arrays.asList("title", "companyName","status", "priority", "foldersOfPreSubmitTalents", "numberOfNotes");

    public static final List<String> RECOMMEND_TALENT_KEY = Arrays.asList("id", "firstName", "lastName",
            "fullName", "currentLocation", "motivation", "languages", "skills", "topEducation", "nonTopEducations", "currentExperiences", "pastExperiences", "applications", "foldersOfPreSubmitTalents",
            "createdDate","lastModifiedDate","totalPhones", "totalEmails", "totalWechats", "totalWhatsApps", "totalLinkedIns", "hasResume", "affiliations");

    public static final List<String> COMPANY_CLIENT_SEARCH_SOURCE = Arrays.asList("active", "companyName", "MSPCompanies", "MSPAssociatedCompanies", "parentCorporate", "childCompanies", "createdDate", "folders", "importance", "industries.id", "industries.en_sort_order", "industries.cn_sort_order", "lastESFillerModifiedTime", "logo", "locations", "serviceTypes", "responsibility5", "responsibility1", "responsibility6", "responsibility11", "responsibility2","coAMCountries", "responsibility12", "numberOfContacts", "numberOfJobs", "hasUnsignedSalesLead", "contracts", "tags", "phonesFormatted", "tenantTags", "displayNameInEn", "businessName", "poachingRestriction", "websites");

    public static final List<String> COMPANY_PROSPECT_SEARCH_SOURCE = Arrays.asList("active", "companyName", "MSPCompanies", "MSPAssociatedCompanies", "parentCorporate", "childCompanies", "createdDate", "folders", "industries.id", "industries.en_sort_order", "industries.cn_sort_order", "lastESFillerModifiedTime", "logo", "locations", "salesLeads", "locations", "responsibility5", "numberOfContacts", "numberOfJobs", "tags", "phonesFormatted", "tenantTags", "displayNameInEn", "businessName", "websites");

    public static final List<String> DROP_DOWN_KEYS = Arrays.asList("jobFunctions","industries","languages","requiredLanguages","preferredLanguages","degree", "minimumDegreeLevel", "workAuthorization");

    public static final String DROP_DOWN_JOBFUNCTION = "jobFunctions";

    public static final String DROP_DOWN_INDUSTRY = "industries";

    public static final String JD_RESPONSIBILITY_TEXT = "responsibilityText";

    public static final String JD_SUMMARY_TEXT = "summaryText";

    public static final String JD_REQUIREMENT_TEXT = "requirementText";

    public static final String JOB_PUBLICDESC = "publicDesc";

    public static final String YEARS_SOURCE_PREFIX = "experienceYearsIn";

    public static final String ESFILLER_KEY_NICK_NAME = "nickName";

    public static final String ESFILLER_KEY_LOCATIONS = "locations";

    public static final String ESFILLER_KEY_COMPANY_NAME_EN = "displayNameInEn";

    public static final String ESFILLER_KEY_CURRENT_LOCATIONS = "currentLocation";



    public ElasticSearchConstants() {

    }
}
