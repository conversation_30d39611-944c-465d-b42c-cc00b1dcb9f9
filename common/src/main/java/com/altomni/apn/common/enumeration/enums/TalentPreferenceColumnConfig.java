package com.altomni.apn.common.enumeration.enums;

import java.util.List;
import java.util.Optional;

public enum TalentPreferenceColumnConfig {
    NAME("fullName", List.of("fullName")),
    COMPANY("currentCompanyName", List.of("currentExperiences.companyName", "currentExperiences.companyNamePinYin")),
    TITLE("currentExperiencesTitle", List.of("currentExperiences.title", "currentExperiences.titlePinYin")),
    PHONE("phones", List.of("phonesDisplay")),
    EMAIL("emails", List.of("emails")),
    LOCATION("currentLocation", List.of("currentLocation.originDisplay")),
    MOTIVATION("motivation", List.of("motivation")),
    //enum_user_responsibility获取
    CREATED_BY("createdBy", List.of("responsibility5")),
    CREATED_DATE("createdDate", List.of("createdDate")),
    GENDER("gender", List.of("gender")),
    ETHNICITY("ethnicity", List.of("ethnicity")),
    PREFERRED_PRONOUN("preferredPronoun", List.of("preferredPronoun")),
    DISABILITY("disability", List.of("disability")),
    VETERAN("veteran", List.of("veteran")),
    MEMBER_OF_LGBTQ("memberOfLGBTQ", List.of("memberOfLGBTQ")),
    AGE("age", List.of("birthDate")),
    INDUSTRIES("industries", List.of("industries.id")),
    JOB_FUNCTIONS("jobFunctions", List.of("jobFunctions.id")),
    SKILLS("skills", List.of("skills.skillName")),
    SALARY("currentSalary", List.of("payType","salaryRange","currency", "currencyUSDExchangeRate", "annualSalaryInUSD")),
    LANGUAGE("languages", List.of("languages")),
    PREFERRED_SALARY("preferredSalary", List.of("preferredSalaryRange", "preferredPayType", "preferredCurrency", "preferredCurrencyUSDExchangeRate", "preferredAnnualSalaryInUSD", "preferences")),
    PREFERRED_LOCATION("preferredLocations", List.of("preferredLocations.originDisplay", "preferences")),
    SCHOOL("highestDegreeCollege", List.of("topEducation.collegeName", "topEducation.collegeInfo")),
    DEGREE("highestDegreeLevel", List.of("topEducation.degreeLevel")),
    MAJOR("highestDegreeMajor", List.of("topEducation.majorName")),
    //enum_user_responsibility获取
    AM("accountManager", List.of("applications.responsibility1")),
    //enum_user_responsibility获取
    CO_AM("cooperateAccountManager", List.of("applications.responsibility7","coAMCountries")),
    //enum_user_responsibility获取
    DM("deliveryManager", List.of("applications.responsibility2")),
    //enum_user_responsibility获取
    RECRUITER("recruiter", List.of("applications.responsibility3")),
    //enum_user_responsibility获取
    PARTICIPANT("participant", List.of("applications.responsibility6")),
    //enum_user_responsibility获取
    SALES_LEAD_OWNER("salesLeadOwner", List.of("applications.responsibility8")),
    //enum_user_responsibility获取
    BD_OWNER("bdOwner", List.of("applications.responsibility9")),
    WORK_AUTHORIZATION("workAuthorization", List.of("workAuthorization")),
    //enum_user_responsibility获取
    OWNER("owner", List.of("responsibility6")),

    //enum_user_responsibility获取
    SHARE_USER("sharedBy", List.of("responsibility7")),
    EXPERIENCE_YEARS("experienceYears", List.of("experienceYears")),
    CANDIDATE_TYPE("isActiveClientContact", List.of("currentExperiences.activeCompanyId")),
    TALENT_ID("talentId", List.of("_id")),
    DEFAULT_RETURN("defaultReturn", List.of("createdDate","lastModifiedDate","totalPhones", "totalEmails", "totalWechats", "totalWhatsApps", "totalLinkedIns", "hasResume"
            , "currentExperiences.activeCompanyId", "currentExperiences.activeCRMAccountId", "currentExperiences.companyId", "currentExperiences.recogCompanyId")),
    COMMON_POOL_RETURN("commonPollReturn", List.of("formattedLinkedIn","industries","jobFunctions")),
    RELATE_JOB_FOLDER_RETURN("relateJobFolderReturn", List.of("foldersOfPreSubmitTalents", "applications")),
    NICK_NAME("nickName", List.of("nickName")),
    NUMBER_OF_NOTES("numberOfNotes", List.of("numberOfNotes")),
    LATEST_NOTE("latestNote", List.of("latestNote")),
    REVIEW_STATUS("reviewStatus", List.of("reviewInfos")),
    SOURCE("source", List.of("source", "sourceAgency")),
    CONFIDENTIAL_INFO_RETURN("confidentialInfoReturn", List.of("confidentialInfo", "responsibility8")),

    CONTACT_DATA_PERMISSION_RETURN("contactPermission", List.of("affiliations"));

    ;
    private final String configName;
    private final List<String> esName;

    TalentPreferenceColumnConfig(String configName, List<String> esName) {
        this.configName = configName;
        this.esName = esName;
    }

    public String getConfigName() {
        return configName;
    }

    public List<String> getEsName() {
        return esName;
    }



    public static Optional<TalentPreferenceColumnConfig> parse(String configName) {
        for(TalentPreferenceColumnConfig columnConfig : TalentPreferenceColumnConfig.values()) {
            if(columnConfig.getConfigName().contentEquals(configName)) {
                return Optional.of(columnConfig);
            }
        }
        return Optional.empty();
    }
}
