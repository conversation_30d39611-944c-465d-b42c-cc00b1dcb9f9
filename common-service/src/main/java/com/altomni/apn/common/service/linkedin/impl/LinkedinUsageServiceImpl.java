package com.altomni.apn.common.service.linkedin.impl;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.linkedin.LinkedinUsageDTO;
import com.altomni.apn.common.dto.linkedin.LinkedinUsageSearchDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.service.linkedin.LinkedinUsageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Objects;

@Slf4j
@Service
public class LinkedinUsageServiceImpl implements LinkedinUsageService {

    @Resource
    private HttpService httpService;

    @Value("${application.linkedin-usage.summary-url}")
    private String linkedinUsageSummarySearchUrl;

    @Override
    public LinkedinUsageDTO searchLinkedinUsageReport(LinkedinUsageSearchDTO linkedinUsageSearchDTO) {
        try {
            String bodyJson = JSONUtil.toJsonStr(linkedinUsageSearchDTO);
            HttpResponse response = httpService.post(linkedinUsageSummarySearchUrl, bodyJson);
            if (Objects.nonNull(response) && response.getCode() == 200) {
                LinkedinUsageDTO linkedinUsageDTO = JSONUtil.toBean(response.getBody(), LinkedinUsageDTO.class);
                return linkedinUsageDTO;
            } else {
                log.error("[LinkedinUsageService: searchLinkedinUsageReport] Error while searching linkedin-usage report, with searchDTO: {}, and response code: {}, response body: {}", linkedinUsageSearchDTO, response.getCode(), response.getBody());
                throw new CustomParameterizedException("search linkedin usage summary error");
            }
        } catch (IOException e) {
            log.error("[LinkedinUsageService: searchLinkedinUsageReport] IOException when search linkedin usage summary with searchDTO: {}", linkedinUsageSearchDTO, e);
            throw new CustomParameterizedException("search linkedin usage summary IOException");
        }
    }

}
