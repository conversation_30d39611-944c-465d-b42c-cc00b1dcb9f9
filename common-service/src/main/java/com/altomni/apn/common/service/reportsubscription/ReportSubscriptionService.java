package com.altomni.apn.common.service.reportsubscription;

import com.altomni.apn.common.dto.reportsubscription.ReportSubscriptionDTO;
import com.altomni.apn.common.enumeration.reportSubscriptions.ActiveType;
import com.altomni.apn.common.enumeration.reportSubscriptions.ReportType;
import com.altomni.apn.common.subscription.DeleteSubscriptionBatchDTO;
import com.altomni.apn.common.vo.reportsubscription.ReportSubscriptionSimplifyVO;
import com.altomni.apn.common.vo.reportsubscription.ReportSubscriptionVO;

import java.util.List;

public interface ReportSubscriptionService {
    void createReportSubscription(ReportSubscriptionDTO reportSubscriptionDTO);

    void createReportSubscriptionForAutoGenerate(ReportSubscriptionDTO reportSubscriptionDTO);

    void updateReportSubscription(ReportSubscriptionDTO reportSubscriptionDTO);

    void deleteReportSubscriptionById(Long id);

    void deleteReportSubscriptionBatch(DeleteSubscriptionBatchDTO deleteSubscriptionBatchDTO);

    void updateReportSubscriptionActiveStatus(Long id, ActiveType active);

    List<ReportSubscriptionSimplifyVO> getReportSubscriptionList(ReportType reportType);

    ReportSubscriptionVO getReportSubscriptionDetailById(Long id);

    ReportType unsubscribeReportById(Integer id);
}
