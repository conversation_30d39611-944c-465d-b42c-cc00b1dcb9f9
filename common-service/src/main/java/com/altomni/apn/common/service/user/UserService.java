package com.altomni.apn.common.service.user;

import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.customconfig.TenantConfigDTO;
import com.altomni.apn.common.dto.parser.TenantWatermarkDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.vo.user.UserTimeZoneVO;
import com.altomni.apn.user.domain.permission.PermissionTeam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * Service Interface for managing {@link TalentDTOV3}.
 */
@Component
@FeignClient(value = "user-service")
public interface UserService {


    @GetMapping("/user/api/v3/users/all-brief-with-permission/{module}")
    ResponseEntity<Object> getAllBriefUsersWithPermissionByType(@PathVariable("module") Module module);

    @PostMapping("/user/api/v3/users/get-all-inactive-user")
    ResponseEntity<List<Long>> getAllInactiveByidIn(@RequestBody Set<Long> ids);

    @PostMapping("/user/api/v3/permissions/teams/users/active-team-user-ids")
    ResponseEntity<Set<Long>> getAllActiveTeamUserIdsByPermissionTeamIdIn(@RequestBody Set<Long> teamIds);

    @GetMapping("/user/api/v3/tenants/watermark/{tenantId}")
    ResponseEntity<TenantWatermarkDTO> getTenantWatermarkConfig(@PathVariable("tenantId") Long tenantId);

    @GetMapping("/user/api/v3/users/{id}")
    ResponseEntity<User> findUserById(@PathVariable("id") Long id);

    @GetMapping("/user/api/v3/users/sync-lark")
    ResponseEntity<Integer> getSyncLark();

    @GetMapping("/user/api/v3/users/all-brief")
    ResponseEntity<List<UserBriefDTO>> getAllBriefUsers();

    @GetMapping("/user/api/v3/users/all-tenant/emails")
    ResponseEntity<List<String>> getAllTenantUserEmails();

    @GetMapping("/user/api/v3/setting/{tenantId}")
    ResponseEntity<TenantConfigDTO> getSettingConfig(@PathVariable("tenantId") Long tenantId,@RequestParam(value = "configCode") TenantConfigCode configCode);

    @GetMapping("/user/api/v3/setting")
    ResponseEntity<TenantConfigDTO> getSettingConfig(@RequestParam(value = "configCode") TenantConfigCode configCode);

    @GetMapping("/user/api/v3/setting/{tenantId}")
    ResponseEntity<TenantConfigDTO> getSettingConfig(@RequestParam(value = "configCode") TenantConfigCode configCode, @PathVariable("tenantId") Long tenantId);

    @PostMapping("/user/api/v3/users/all-brief-by-ids")
    ResponseEntity<List<UserBriefDTO>> getBriefUsersByIds(@RequestBody List<Long> ids);

    @PostMapping("/user/api/v3/users/get-timezone-by-ids")
    ResponseEntity<List<UserTimeZoneVO>> getTimezoneListByUserIdList(@RequestBody List<Long> ids);

    @GetMapping("/user/api/v3/talents/config/talent-form")
    ResponseEntity<String> getTalentFormConfig();

    @PostMapping("/user/api/v3/users/update-push-time")
    ResponseEntity<Void> updatePushTime();

    @PostMapping("/user/api/v3/permissions/teams/all-child-teams")
    ResponseEntity<List<PermissionTeam>> getAllChildTeams(@RequestBody Set<Long> teamIds);

    @PostMapping("/user/api/v3/permissions/teams/all-child-teams/active-user-ids")
    ResponseEntity<Set<Long>> getAllActiveUserIdsIncludingChildTeamUsersByTeamIds(@RequestBody Set<Long> teamIds);

}
