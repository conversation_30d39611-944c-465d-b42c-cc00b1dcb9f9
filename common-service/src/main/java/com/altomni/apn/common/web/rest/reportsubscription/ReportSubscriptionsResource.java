package com.altomni.apn.common.web.rest.reportsubscription;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.dto.reportsubscription.ReportSubscriptionDTO;
import com.altomni.apn.common.enumeration.reportSubscriptions.ActiveType;
import com.altomni.apn.common.enumeration.reportSubscriptions.ReportType;
import com.altomni.apn.common.service.reportsubscription.ReportSubscriptionService;
import com.altomni.apn.common.subscription.DeleteSubscriptionBatchDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.reportsubscription.ReportSubscriptionSimplifyVO;
import com.altomni.apn.common.vo.reportsubscription.ReportSubscriptionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class ReportSubscriptionsResource {

    @Resource
    private ReportSubscriptionService reportSubscriptionService;

    @PostMapping("/kpi/subscription")
    public ResponseEntity<Void> createReportSubscription(@RequestBody ReportSubscriptionDTO reportSubscriptionDTO) {
        log.info("[apn @{}] create report subscription , param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(reportSubscriptionDTO));
        reportSubscriptionService.createReportSubscription(reportSubscriptionDTO);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/kpi/subscription/batch")
    public ResponseEntity<Void> createReportSubscriptionBatch(@RequestBody List<ReportSubscriptionDTO> reportSubscriptionDTOList) {
        for(ReportSubscriptionDTO reportSubscriptionDTO : reportSubscriptionDTOList){
            reportSubscriptionService.createReportSubscriptionForAutoGenerate(reportSubscriptionDTO);
        }
        return ResponseEntity.ok().build();
    }

    @PutMapping("/kpi/subscription")
    public ResponseEntity<Void> updateReportSubscription(@RequestBody ReportSubscriptionDTO reportSubscriptionDTO) {
        log.info("[apn @{}] update report subscription , param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(reportSubscriptionDTO));
        reportSubscriptionService.updateReportSubscription(reportSubscriptionDTO);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/kpi/subscription/{id}")
    public ResponseEntity<Void> deleteReportSubscriptionById(@PathVariable("id") Long id) {
        log.info("[apn @{}] delete report subscription , param = {}", SecurityUtils.getUserId(), id);
        reportSubscriptionService.deleteReportSubscriptionById(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/kpi/subscription/delete/batch")
    public ResponseEntity<Void> deleteReportSubscriptionBatch(@RequestBody DeleteSubscriptionBatchDTO deleteSubscriptionBatchDTO) {
        reportSubscriptionService.deleteReportSubscriptionBatch(deleteSubscriptionBatchDTO);
        return ResponseEntity.ok().build();
    }


    @PutMapping("/kpi/subscription/{id}/{active}")
    public ResponseEntity<Void> updateReportSubscriptionActiveStatus(@PathVariable("id") Long id, @PathVariable("active") ActiveType active) {
        log.info("[apn @{}] update report subscription , id = {}, isActive = {}", SecurityUtils.getUserId(), id, active);
        reportSubscriptionService.updateReportSubscriptionActiveStatus(id, active);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/kpi/subscription/list/{reportType}")
    public ResponseEntity<List<ReportSubscriptionSimplifyVO>> getReportSubscriptionListByReportType(@PathVariable("reportType") ReportType reportType) {
        log.info("[apn @{}] get report subscription list, reportType = {}", SecurityUtils.getUserId(), reportType);
        List<ReportSubscriptionSimplifyVO> subscriptionSimplifyVOList = reportSubscriptionService.getReportSubscriptionList(reportType);
        return ResponseEntity.ok(subscriptionSimplifyVOList);
    }

    @GetMapping("/kpi/subscription/detail/{id}")
    public ResponseEntity<ReportSubscriptionVO> getReportSubscriptionDetailById(@PathVariable("id") Long id) {
        log.info("[apn @{}] get report subscription list, reportType = {}", SecurityUtils.getUserId(), id);
        ReportSubscriptionVO reportSubscriptionVO = reportSubscriptionService.getReportSubscriptionDetailById(id);
        return ResponseEntity.ok(reportSubscriptionVO);
    }

    @PostMapping("/kpi/unsubscribe/{id}")
    public ResponseEntity<ReportType> unsubscribeReport(@PathVariable("id") Integer id) {
        log.info("[apn @{}] unsubscribe report subscription , param = {}", SecurityUtils.getUserId(), id);
        ReportType reportType = reportSubscriptionService.unsubscribeReportById(id);
        return ResponseEntity.ok(reportType);
    }

}
