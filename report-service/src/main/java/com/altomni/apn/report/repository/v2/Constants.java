package com.altomni.apn.report.repository.v2;

import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.SQLDialect;
import org.jooq.Table;
import org.jooq.conf.RenderKeywordCase;
import org.jooq.conf.RenderQuotedNames;
import org.jooq.conf.Settings;

import java.util.List;

import static org.jooq.impl.DSL.*;

public class Constants {

    public static final DSLContext dsl = using(SQLDialect.MYSQL, new Settings()
            // 去掉 group concat max len 设置
            .withRenderGroupConcatMaxLenSessionVariable(false)
            // 使用大写关键字
            .withRenderKeywordCase(RenderKeywordCase.UPPER)
            // 格式化 SQL
            .withRenderFormatted(true)
            // 不使用双引号
            .withRenderQuotedNames(RenderQuotedNames.NEVER));

    public static final Table<?> VIEW_APPLICATION_CURRENT_API = table("view_application_current_kpi").as("application_kpi");
    public static final Table<?> VIEW_APPLICATION_FUNNEL_API = table("view_application_funnel_kpi").as("application_kpi");
    public static final Table<?> VIEW_NOTES_KPI = table("mv_note_kpi_v2").as("note_kpi");
    public static final Table<?> VIEW_CREATED__KPI = table("mv_created_kpi_v2").as("created_kpi");

    public static final Field<Long> ID = field("id", Long.class);
    public static final String DATE_DIM_ALIAS = "group_by_date";
    public static final Field<Long> USER_ID = field("user_id", Long.class);
    public static final Field<Long> PREFIX_PUT_USER_ID = field("put.user_id", Long.class);
    public static final Field<Boolean> USER_ACTIVATED = field("user_activated", Boolean.class);
    public static final Field<String> USER_NAME = field("user_name", String.class);
    public static final Field<Long> JOB_PTEAM_ID = field("job_pteam_id", Long.class);
    public static final Field<Long> JOB_ID = field("job_id", Long.class);
    public static final Field<Long> PREFIX_APP_JOB_ID = field("application_kpi.job_id", Long.class);
    public static final Field<String> JOB_NAME = field("j.title", String.class);
    public static final Field<String> JOB_NAME_AS = field("j.title as jobTitle", String.class);
    public static final Field<Long> COMPANY_ID = field("company_id", Long.class);
    public static final Field<Long> PREFIX_APP_COMPANY_ID = field("application_kpi.company_id", Long.class);
    public static final Field<String> COMPANY_NAME = field("full_business_name", String.class);
    public static final Field<String> COMPANY_NAME_AS = field("full_business_name as companyName", String.class);
    public static final Field<Long> TEAM_ID = field("team_id", Long.class);
    public static final Field<Long> PREFIX_PUT_TEAM_ID = field("put.team_id", Long.class);
    public static final Field<String> TEAM_NAME = field("team_name", String.class);
    public static final Field<Long> TEAM_PARENT_ID = field("team_parent_id", Long.class);
    public static final Field<Integer> TEAM_LEVEL = field("team_level", Integer.class);
    public static final Field<Boolean> TEAM_IS_LEAF = field("team_is_leaf", Boolean.class);
    public static final Field<Long> TENANT_ID = field("tenant_id", Long.class);
    public static final Field<Long> PREFIX_APP_TENANT_ID = field("application_kpi.tenant_id", Long.class);
    public static final Field<String> EVENT_DATE = field("event_date", String.class);
    public static final Field<String> ADD_DATE = field("add_date", String.class);

    public static final Table<?> USER = table("user").as("u");
    public static final Table<?> JOB = table("job").as("j");
    public static final Table<?> COMPANY = table("company").as("c");
    public static final Table<?> COMPANY_USER_RELATION = table("company_user_relation").as("ul");


    /**
     * 当前状态 KPI 指标列表
     */
    public static List<Field<?>> createCurrentKpiMetrics() {
        return List.of(
                // 投递相关指标
                bitmap_union_count(field("submit_to_job_current_countNum", Integer.class)).as("submitToJobCurrentCountNum"),
                bitmap_union_count(field("submit_to_job_currentAiRecommendNum", Integer.class)).as("submitToJobCurrentAiRecommendNum"),
                bitmap_union_count(field("submit_to_job_currentPrecisionAiRecommendNum", Integer.class)).as("submitToJobCurrentPrecisionAiRecommendNum"),
                bitmap_union_count(field("submit_to_job_currentStayedOver", Integer.class)).as("submitToJobCurrentStayedOver"),

                // 提交给客户相关指标
                bitmap_union_count(field("submit_to_client_current_countNum", Integer.class)).as("submitToClientCurrentCountNum"),
                bitmap_union_count(field("submit_to_client_currentAiRecommendNum", Integer.class)).as("submitToClientCurrentAiRecommendNum"),
                bitmap_union_count(field("submit_to_client_currentPrecisionAiRecommendNum", Integer.class)).as("submitToClientCurrentPrecisionAiRecommendNum"),
                bitmap_union_count(field("submit_to_client_currentStayedOver", Integer.class)).as("submitToClientCurrentStayedOver"),

                // 面试相关基础指标
                bitmap_union_count(field("current_interview1", Integer.class)).as("currentInterview1"),
                bitmap_union_count(field("current_interview2", Integer.class)).as("currentInterview2"),
                bitmap_union_count(field("current_two_or_more_interviews", Integer.class)).as("currentTwoOrMoreInterviews"),
                bitmap_union_count(field("current_interview_final", Integer.class)).as("currentInterviewFinal"),
                bitmap_union_count(field("current_interview_total", Integer.class)).as("currentInterviewTotal"),
                bitmap_union_count(field("unique_interview_talents", Integer.class)).as("uniqueInterviewTalents"),
                bitmap_union_count(field("current_interview_total_process", Integer.class)).as("currentInterviewTotalProcess"),

                // 面试 AI 推荐指标
                bitmap_union_count(field("currentInterviewTotalAiRecommendNum", Integer.class)).as("currentInterviewTotalAiRecommendNum"),
                bitmap_union_count(field("currentInterviewTotalProcessAiRecommendNum", Integer.class)).as("currentInterviewTotalProcessAiRecommendNum"),
                bitmap_union_count(field("currentInterview1AiRecommendNum", Integer.class)).as("currentInterview1AiRecommendNum"),
                bitmap_union_count(field("currentInterview2AiRecommendNum", Integer.class)).as("currentInterview2AiRecommendNum"),
                bitmap_union_count(field("currentTwoOrMoreInterviewsAiRecommendNum", Integer.class)).as("currentTwoOrMoreInterviewsAiRecommendNum"),
                bitmap_union_count(field("currentInterviewFinalAiRecommendNum", Integer.class)).as("currentInterviewFinalAiRecommendNum"),

                // 面试精准推荐指标
                bitmap_union_count(field("currentInterviewTotalPrecisionAiRecommendNum", Integer.class)).as("currentInterviewTotalPrecisionAiRecommendNum"),
                bitmap_union_count(field("currentInterviewNumProcessPrecisionAIRecommend", Integer.class)).as("currentInterviewNumProcessPrecisionAIRecommend"),
                bitmap_union_count(field("currentInterview1PrecisionAiRecommendNum", Integer.class)).as("currentInterview1PrecisionAiRecommendNum"),
                bitmap_union_count(field("currentInterview2PrecisionAiRecommendNum", Integer.class)).as("currentInterview2PrecisionAiRecommendNum"),
                bitmap_union_count(field("currentTwoOrMoreInterviewsPrecisionAiRecommendNum", Integer.class)).as("currentTwoOrMoreInterviewsPrecisionAiRecommendNum"),
                bitmap_union_count(field("currentInterviewFinalPrecisionAiRecommendNum", Integer.class)).as("currentInterviewFinalPrecisionAiRecommendNum"),

                // Offer 相关指标
                bitmap_union_count(field("offer_current_countNum", Integer.class)).as("offerCurrentCountNum"),
                bitmap_union_count(field("offer_currentAiRecommendNum", Integer.class)).as("offerCurrentAiRecommendNum"),
                bitmap_union_count(field("offer_currentPrecisionAiRecommendNum", Integer.class)).as("offerCurrentPrecisionAiRecommendNum"),

                // Offer 接受相关指标
                bitmap_union_count(field("offer_accept_current_countNum", Integer.class)).as("offerAcceptCurrentCountNum"),
                bitmap_union_count(field("offer_accept_currentAiRecommendNum", Integer.class)).as("offerAcceptCurrentAiRecommendNum"),
                bitmap_union_count(field("offer_accept_currentPrecisionAiRecommendNum", Integer.class)).as("offerAcceptCurrentPrecisionAiRecommendNum"),

                // 入职相关指标
                bitmap_union_count(field("onboard_current_countNum", Integer.class)).as("onboardCurrentCountNum"),
                bitmap_union_count(field("onboard_currentAiRecommendNum", Integer.class)).as("onboardCurrentAiRecommendNum"),
                bitmap_union_count(field("onboard_currentPrecisionAiRecommendNum", Integer.class)).as("onboardCurrentPrecisionAiRecommendNum"),

                // 淘汰相关指标
                bitmap_union_count(field("eliminate_current_countNum", Integer.class)).as("eliminateCurrentCountNum"),
                bitmap_union_count(field("eliminate_currentAiRecommendNum", Integer.class)).as("eliminateCurrentAiRecommendNum"),
                bitmap_union_count(field("eliminate_currentPrecisionAiRecommendNum", Integer.class)).as("eliminateCurrentPrecisionAiRecommendNum")
        );
    }

    /**
     * 漏斗状态 KPI 指标列表
     */
    public static List<Field<?>> createKpiFunnelMetrics() {
        return List.of(
                // 投递相关指标
                bitmap_union_count(field("submit_to_job_countNum", Integer.class)).as("submitToJobCountNum"),
                bitmap_union_count(field("submit_to_job_aiRecommendCountNum", Integer.class)).as("submitToJobAiRecommendCountNum"),
                bitmap_union_count(field("submit_to_job_precisionAiRecommendNum", Integer.class)).as("submitToJobPrecisionAiRecommendNum"),
                bitmap_union_count(field("submit_to_job_stayedOver", Integer.class)).as("submitToJobStayedOver"),

                // 提交给客户相关指标
                bitmap_union_count(field("submit_to_client_countNum", Integer.class)).as("submitToClientCountNum"),
                bitmap_union_count(field("submit_to_client_aiRecommendCountNum", Integer.class)).as("submitToClientAiRecommendCountNum"),
                bitmap_union_count(field("submit_to_client_precisionAiRecommendNum", Integer.class)).as("submitToClientPrecisionAiRecommendNum"),
                bitmap_union_count(field("submit_to_client_stayedOver", Integer.class)).as("submitToClientStayedOver"),

                // 面试相关基础指标
                bitmap_union_count(field("interview1", Integer.class)).as("interview1"),
                bitmap_union_count(field("interview2", Integer.class)).as("interview2"),
                bitmap_union_count(field("two_or_more_interviews", Integer.class)).as("twoOrMoreInterviews"),
                bitmap_union_count(field("interview_final", Integer.class)).as("interviewFinal"),
                bitmap_union_count(field("interview_total", Integer.class)).as("interviewTotal"),
                bitmap_union_count(field("unique_interview_talents", Integer.class)).as("uniqueInterviewTalents"),
                bitmap_union_count(field("interview_total_process", Integer.class)).as("interviewTotalProcess"),

                // 面试 AI 推荐指标
                bitmap_union_count(field("interviewTotalAiRecommendNum", Integer.class)).as("interviewTotalAiRecommendNum"),
                bitmap_union_count(field("interviewTotalProcessAiRecommendNum", Integer.class)).as("interviewTotalProcessAiRecommendNum"),
                bitmap_union_count(field("interview1AiRecommendNum", Integer.class)).as("interview1AiRecommendNum"),
                bitmap_union_count(field("interview2AiRecommendNum", Integer.class)).as("interview2AiRecommendNum"),
                bitmap_union_count(field("twoOrMoreInterviewsAiRecommendNum", Integer.class)).as("twoOrMoreInterviewsAiRecommendNum"),
                bitmap_union_count(field("interviewFinalAiRecommendNum", Integer.class)).as("interviewFinalAiRecommendNum"),

                // 面试精准推荐指标
                bitmap_union_count(field("interviewTotalPrecisionAiRecommendNum", Integer.class)).as("interviewTotalPrecisionAiRecommendNum"),
                bitmap_union_count(field("interviewNumProcessPrecisionAIRecommend", Integer.class)).as("interviewNumProcessPrecisionAIRecommend"),
                bitmap_union_count(field("interview1PrecisionAiRecommendNum", Integer.class)).as("interview1PrecisionAiRecommendNum"),
                bitmap_union_count(field("interview2PrecisionAiRecommendNum", Integer.class)).as("interview2PrecisionAiRecommendNum"),
                bitmap_union_count(field("twoOrMoreInterviewsPrecisionAiRecommendNum", Integer.class)).as("twoOrMoreInterviewsPrecisionAiRecommendNum"),
                bitmap_union_count(field("interviewFinalPrecisionAiRecommendNum", Integer.class)).as("interviewFinalPrecisionAiRecommendNum"),

                // Offer 相关指标
                bitmap_union_count(field("offer_countNum", Integer.class)).as("offerCountNum"),
                bitmap_union_count(field("offer_aiRecommendCountNum", Integer.class)).as("offerAiRecommendCountNum"),
                bitmap_union_count(field("offer_precisionAiRecommendNum", Integer.class)).as("offerPrecisionAiRecommendNum"),

                // Offer 接受相关指标
                bitmap_union_count(field("offer_accept_countNum", Integer.class)).as("offerAcceptCountNum"),
                bitmap_union_count(field("offer_accept_aiRecommendCountNum", Integer.class)).as("offerAcceptAiRecommendCountNum"),
                bitmap_union_count(field("offer_accept_precisionAiRecommendNum", Integer.class)).as("offerAcceptPrecisionAiRecommendNum"),

                // 入职相关指标
                bitmap_union_count(field("onboard_countNum", Integer.class)).as("onboardCountNum"),
                bitmap_union_count(field("onboard_aiRecommendCountNum", Integer.class)).as("onboardAiRecommendCountNum"),
                bitmap_union_count(field("onboard_precisionAiRecommendNum", Integer.class)).as("onboardPrecisionAiRecommendNum"),

                // 淘汰相关指标
                bitmap_union_count(field("eliminate_countNum", Integer.class)).as("eliminateCountNum"),
                bitmap_union_count(field("eliminate_aiRecommendCountNum", Integer.class)).as("eliminateAiRecommendCountNum"),
                bitmap_union_count(field("eliminate_precisionAiRecommendNum", Integer.class)).as("eliminatePrecisionAiRecommendNum")
        );
    }


    private static Field<Integer> bitmap_union_count(Field<Integer> field) {
        return field("BITMAP_UNION_COUNT(%s)".formatted(field.getName()), Integer.class);
    }

    private static Field<String> bitmap_union_to_string(Field<?> field) {
        return field("BITMAP_TO_STRING(BITMAP_UNION(%s))".formatted(field.getName()), String.class);
    }


}
