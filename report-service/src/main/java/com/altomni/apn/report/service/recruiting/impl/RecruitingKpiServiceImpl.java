package com.altomni.apn.report.service.recruiting.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.*;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiViewType;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.FutureExceptionUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.recruiting.*;
import com.altomni.apn.report.domain.enumeration.DataSourceType;
import com.altomni.apn.report.domain.enumeration.ReportTableType;
import com.altomni.apn.report.domain.vo.BDReportKpiUserCompanyInfoVO;
import com.altomni.apn.report.domain.vo.CrmUserIdAndTeamIdVO;
import com.altomni.apn.report.repository.RecruitingKpiCompanyRepository;
import com.altomni.apn.report.repository.RecruitingKpiUserRepository;
import com.altomni.apn.report.repository.ReportRepository;
import com.altomni.apn.report.service.recruiting.RecruitingKpiService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service("recruitingKpiService")
public class RecruitingKpiServiceImpl extends RecruitingKpiBaseServiceImpl implements RecruitingKpiService {

    @Resource
    private RecruitingKpiUserRepository recruitingKpiUserRepository;

    @Resource
    private RecruitingKpiCompanyRepository recruitingKpiCompanyRepository;

    @Resource
    private ReportRepository reportRepository;

    @Resource
    private HttpService httpService;

    @Value("${application.crmUrl}")
    private String crmUrl;

    @Override
    public List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUser(RecruitingKpiReportSearchDto searchDto) {
        log.info("[apn @{}] search recruiting kpi report by user, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        StopWatch stopWatch = new StopWatch("recruiting kpi report by user");

        stopWatch.start("[1] search permission data task");
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        stopWatch.stop();
        stopWatch.start("[2] search job、talent、application、note、company info、crm company info data task");
        Function<RecruitingKpiCommonCountVO, String> function = getMapKey(searchDto.getGroupByFieldList());
        //job
        CompletableFuture<ConcurrentMap<String, RecruitingKpiCommonCountVO>> jobFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepository.searchJobKpiData(searchDto, teamDTO)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), getExecutor());
        //talent
        CompletableFuture<ConcurrentMap<String, RecruitingKpiCommonCountVO>> talentFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepository.searchTalentKpiData(searchDto, teamDTO)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), getExecutor());
        //submit to job
        CompletableFuture<ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO>> submitToJobFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepository.searchNodeTypeKpiData(searchDto, teamDTO, ReportTableType.SUBMIT_TO_JOB)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), getExecutor());
        //submit to client
        CompletableFuture<ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO>> submitToClientFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepository.searchNodeTypeKpiData(searchDto, teamDTO, ReportTableType.SUBMIT_TO_CLIENT)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), getExecutor());
        //interview
        CompletableFuture<ConcurrentMap<String, RecruitingKpiInterviewCountVO>> interviewFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepository.searchInterviewKpiData(searchDto, teamDTO)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), getExecutor());
        //reserve interview
        CompletableFuture<ConcurrentMap<String, RecruitingKpiInterviewCountVO>> reserveInterviewFuture = CompletableFuture.supplyAsync(() -> {
            if (searchDto.getDateType() == RecruitingKpiDateType.ADD) {
                return null;
            }
            //当查询的时间类型为 add 的是,不需要在另外查询预约面试的数据,预约面试的数据就直接是 面试的数据相等
            return recruitingKpiUserRepository.searchReserveInterviewKpiData(searchDto, teamDTO).parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, getExecutor());
        //offer
        CompletableFuture<ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO>> offerFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepository.searchNodeTypeKpiData(searchDto, teamDTO, ReportTableType.OFFER)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), getExecutor());
        //offerAccepted
        CompletableFuture<ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO>> offerAcceptFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepository.searchNodeTypeKpiData(searchDto, teamDTO, ReportTableType.OFFER_ACCEPT)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), getExecutor());
        //onboard
        CompletableFuture<ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO>> onboardFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepository.searchNodeTypeKpiData(searchDto, teamDTO, ReportTableType.ON_BOARD)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), getExecutor());
        //eliminate
        CompletableFuture<ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO>> eliminateFuture = CompletableFuture.supplyAsync(() -> {
            List<RecruitingKpiApplicationCommonCountVO> voList = new ArrayList<>();
            if (!searchDto.isXxlJobFlag()) {
                voList = recruitingKpiUserRepository.searchNodeTypeKpiData(searchDto, teamDTO, ReportTableType.ELIMINATED);
            }
            return voList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, getExecutor());
        //talent note
        CompletableFuture<ConcurrentMap<String, RecruitingKpiTalentNoteCountVO>> talentNoteFuture = CompletableFuture.supplyAsync(() -> {
            List<RecruitingKpiTalentNoteCountVO> voList = new ArrayList<>();
            if (!searchDto.isXxlJobFlag()) {
                voList = recruitingKpiUserRepository.searchTalentNoteKpiData(searchDto, teamDTO);
            }
            return voList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, getExecutor());
        //applicationNote
        CompletableFuture<List<RecruitingKpiApplicationNoteCountVO>> applicationNoteFuture = CompletableFuture.supplyAsync(() -> {
            List<RecruitingKpiApplicationNoteCountVO> voList = new ArrayList<>();
            if (!searchDto.isXxlJobFlag()) {
                voList = recruitingKpiUserRepository.searchApplicationNoteKpiData(searchDto, teamDTO);
            }
            return voList;
            }, getExecutor());
        //apn pro note
        CompletableFuture<ConcurrentMap<String, RecruitingKpiCommonCountVO>> apnProNoteFuture = CompletableFuture.supplyAsync(() -> {
            List<RecruitingKpiCommonCountVO> voList = new ArrayList<>();
            if (!searchDto.isXxlJobFlag()) {
                voList = recruitingKpiUserRepository.searchApnProNoteKpiData(searchDto, teamDTO);
            }
            return voList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, getExecutor());

        SecurityContext context = SecurityContextHolder.getContext();
        // 升级为正式客户指标
        CompletableFuture<ConcurrentMap<String, List<KpiReportCompanyUpgradeToClientVO>>> companyListMapFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return getCompanyInfoByUser(searchDto, teamDTO);
        }, getExecutor());

        //search crm接口, 创建公司数量指标
        CompletableFuture<ConcurrentMap<String, List<KpiReportCompanyUpgradeToClientVO>>> crmCompanyListMapFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return getCrmCompanyInfoByUser(searchDto, teamDTO);
        }, getExecutor());

        CompletableFuture.allOf(jobFuture, talentFuture, submitToJobFuture, submitToClientFuture, interviewFuture, reserveInterviewFuture,
                offerFuture, offerAcceptFuture, onboardFuture, eliminateFuture, talentNoteFuture, applicationNoteFuture, apnProNoteFuture,
                companyListMapFuture, crmCompanyListMapFuture
        ).exceptionally(FutureExceptionUtil::handleFutureException);


        CopyOnWriteArrayList<RecruitingKpiByUserVO> voList = new CopyOnWriteArrayList<>();
        ConcurrentMap<String, RecruitingKpiCommonCountVO> jobMap = jobFuture.join();
        ConcurrentMap<String, RecruitingKpiCommonCountVO> talentMap = talentFuture.join();
        ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> submitToJobMap = submitToJobFuture.join();
        ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> submitToClientMap = submitToClientFuture.join();
        ConcurrentMap<String, RecruitingKpiInterviewCountVO> interviewMap = interviewFuture.join();
        ConcurrentMap<String, RecruitingKpiInterviewCountVO> reserveInterviewMap = reserveInterviewFuture.join();
        ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> offerMap = offerFuture.join();
        ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> offerAcceptMap = offerAcceptFuture.join();
        ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> onboardMap = onboardFuture.join();
        ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> eliminateMap = eliminateFuture.join();
        ConcurrentMap<String, RecruitingKpiTalentNoteCountVO> talentNoteMap = talentNoteFuture.join();
        List<RecruitingKpiApplicationNoteCountVO> applicationNoteList = applicationNoteFuture.join();
        ConcurrentMap<String, RecruitingKpiCommonCountVO> apnProNoteMap = apnProNoteFuture.join();
        ConcurrentMap<String, List<KpiReportCompanyUpgradeToClientVO>> companyIdInfoMap = companyListMapFuture.join();
        ConcurrentMap<String, List<KpiReportCompanyUpgradeToClientVO>> crmCompanyIdInfoMap = crmCompanyListMapFuture.join();
        ConcurrentMap<String, RecruitingKpiApplicationNoteCountVO> applicationNoteMap = applicationNoteList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        stopWatch.stop();

        Set<String> keySet = Stream.of(jobMap, talentMap, submitToJobMap, submitToClientMap, interviewMap, offerMap, offerAcceptMap, onboardMap, eliminateMap, talentNoteMap, applicationNoteMap, apnProNoteMap).parallel()
                .flatMap(map -> map.keySet().stream())
                .collect(Collectors.toSet());

        stopWatch.start("[4] assemble data task");
        List<StageKpiReportDto> stageSearchFilter = addStageFilter(searchDto);
        keySet.parallelStream().forEach(key -> {
            try {
                RecruitingKpiByUserVO vo = new RecruitingKpiByUserVO();
                setGroupByField(vo, key, searchDto.getGroupByFieldList());
                setVoNum(vo, jobMap, talentMap, submitToJobMap, submitToClientMap, interviewMap, reserveInterviewMap, offerMap, offerAcceptMap, onboardMap, eliminateMap, key, searchDto);
                vo.setCallNoteNum(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getCallNoteNum());
                vo.setPersonNoteNum(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getPersonNoteNum());
                vo.setEmailNoteNum(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getEmailNoteNum());
                vo.setVideoNoteNum(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getVideoNoteNum());
                vo.setOtherNoteNum(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getOtherNoteNum());
                vo.setIciNum(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getIciNum());
                vo.setPipelineNoteNum(applicationNoteMap.getOrDefault(key, new RecruitingKpiApplicationNoteCountVO()).getCountNum());
                vo.setApnProNoteNum(apnProNoteMap.getOrDefault(key, new RecruitingKpiCommonCountVO()).getCountNum());
                vo.setNoteCount(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getNoteCount());
                vo.setUniqueTalentIds(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getUniqueTalentIds());
                if(!companyIdInfoMap.isEmpty()){
                    if (searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.USER)) {
                        List<KpiReportCompanyUpgradeToClientVO> kpiReportCompanyInfoVOList = companyIdInfoMap.get(vo.getUserId().toString());
                        setCreateCompanyInfoByUserBean(kpiReportCompanyInfoVOList,vo,shouldJoinDateDimension(searchDto));
                    } else if (searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.TEAM)) {
                        List<KpiReportCompanyUpgradeToClientVO> kpiReportCompanyInfoVOList = companyIdInfoMap.get(vo.getTeamId().toString());
                        setCreateCompanyInfoByUserBean(kpiReportCompanyInfoVOList,vo,shouldJoinDateDimension(searchDto));
                    } else {
                        List<KpiReportCompanyUpgradeToClientVO> kpiReportCompanyInfoVOList = companyIdInfoMap.get(vo.getGroupByDate());
                        if (null != kpiReportCompanyInfoVOList) {
                            vo.setUpgradeToClient(kpiReportCompanyInfoVOList.get(0).getCompanyCount());
                        }
                    }
                }
                if(!crmCompanyIdInfoMap.isEmpty()){
                    if (searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.USER)) {
                        List<KpiReportCompanyUpgradeToClientVO> crmCompanyInfoVOList = crmCompanyIdInfoMap.get(vo.getUserId().toString());
                        setUpgradeCompanyInfoByUserBean(crmCompanyInfoVOList,vo,shouldJoinDateDimension(searchDto));
                    } else if (searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.TEAM)) {
                        List<KpiReportCompanyUpgradeToClientVO> crmCompanyInfoVOList = crmCompanyIdInfoMap.get(vo.getTeamId().toString());
                        setUpgradeCompanyInfoByUserBean(crmCompanyInfoVOList,vo,shouldJoinDateDimension(searchDto));
                    } else {
                        List<KpiReportCompanyUpgradeToClientVO> kpiReportCompanyInfoVOList = crmCompanyIdInfoMap.get(vo.getGroupByDate());
                        if (null != kpiReportCompanyInfoVOList) {
                            vo.setCreatedCompaniesNum(kpiReportCompanyInfoVOList.get(0).getCompanyCount());
                        }
                    }
                }
                voList.add(vo);
            } catch (Exception e) {
                log.error(" search kpi by user assemble data is error, msg = {}", ExceptionUtil.getAllExceptionMsg(e));
                throw e;
            }
        });

        setCreateCompanyInfo(companyIdInfoMap,searchDto,voList);

        List<RecruitingKpiByUserVO> otherResult = setUpgradeCompanyInfo(crmCompanyIdInfoMap,searchDto,voList);

        if(!otherResult.isEmpty()){
            voList.addAll(otherResult);
        }

        stopWatch.stop();

        List<RecruitingKpiGroupByFieldType> groupByFieldList = searchDto.getGroupByFieldList();
        if (BooleanUtil.isFalse(CollectionUtils.isEmpty(groupByFieldList))){
            this.appendEmptyData(voList, searchDto, teamDTO);
        }
        stopWatch.start("[4] search filter data task");
        CopyOnWriteArrayList<RecruitingKpiByUserVO> resultVoList = new CopyOnWriteArrayList<>();
        voList.forEach(vo->{
            if (checkSearchFilter(vo, stageSearchFilter, searchDto.getApplicationStatusType(),searchDto.getStayedOverList())){
                resultVoList.add(vo);
            }
        });
        stopWatch.stop();
        log.info("[apn @{}] searchRecruitingKpiReportByUser time = {}ms \n {}", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());

        return sortVoListByFields(resultVoList, groupByFieldList);
    }

    @Override
    public RecruitingKpiByUserVO searchRecruitingKpiTotalByUser(RecruitingKpiReportSearchDto searchDto) {
        // not implement
        return null;
    }

    @Override
    public List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUserForE5(RecruitingKpiReportSearchDto searchDto) {
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        Function<RecruitingKpiCommonCountVO, String> function = getMapKey(searchDto.getGroupByFieldList());

        //submit to job
        CompletableFuture<ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO>> submitToJobFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepository.searchNodeTypeKpiData(searchDto, teamDTO, ReportTableType.SUBMIT_TO_JOB)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), getExecutor());
        //interview
        CompletableFuture<ConcurrentMap<String, RecruitingKpiInterviewCountVO>> interviewFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepository.searchInterviewKpiData(searchDto, teamDTO)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), getExecutor());
        //onboard
        CompletableFuture<ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO>> onboardFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepository.searchNodeTypeKpiData(searchDto, teamDTO, ReportTableType.ON_BOARD)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), getExecutor());
        //talent note
        CompletableFuture<ConcurrentMap<String, RecruitingKpiTalentNoteCountVO>> talentNoteFuture = CompletableFuture.supplyAsync(() -> {
            List<RecruitingKpiTalentNoteCountVO> voList = new ArrayList<>();
            if (!searchDto.isXxlJobFlag()) {
                voList = recruitingKpiUserRepository.searchTalentNoteKpiData(searchDto, teamDTO);
            }
            return voList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, getExecutor());
        CompletableFuture.allOf(submitToJobFuture, interviewFuture, onboardFuture, talentNoteFuture
        ).exceptionally(FutureExceptionUtil::handleFutureException);
        CopyOnWriteArrayList<RecruitingKpiByUserVO> voList = new CopyOnWriteArrayList<>();
        ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> submitToJobMap = submitToJobFuture.join();
        ConcurrentMap<String, RecruitingKpiInterviewCountVO> interviewMap = interviewFuture.join();
        ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> onboardMap = onboardFuture.join();
        ConcurrentMap<String, RecruitingKpiTalentNoteCountVO> talentNoteMap = talentNoteFuture.join();
        Set<String> keySet = Stream.of(submitToJobMap,interviewMap, onboardMap, talentNoteMap).parallel()
                .flatMap(map -> map.keySet().stream())
                .collect(Collectors.toSet());
        keySet.parallelStream().forEach(key -> {
            try {
                RecruitingKpiByUserVO vo = new RecruitingKpiByUserVO();
                setGroupByField(vo, key, searchDto.getGroupByFieldList());
                setVoNumForE5(vo, submitToJobMap,  interviewMap,  onboardMap,  key, searchDto);
                vo.setCallNoteNum(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getCallNoteNum());
                vo.setPersonNoteNum(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getPersonNoteNum());
                vo.setEmailNoteNum(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getEmailNoteNum());
                vo.setVideoNoteNum(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getVideoNoteNum());
                vo.setOtherNoteNum(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getOtherNoteNum());
                vo.setIciNum(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getIciNum());
                vo.setNoteCount(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getNoteCount());
                vo.setUniqueTalentIds(talentNoteMap.getOrDefault(key, new RecruitingKpiTalentNoteCountVO()).getUniqueTalentIds());
                voList.add(vo);
            } catch (Exception e) {
                log.error(" search kpi by user assemble data is error, msg = {}", ExceptionUtil.getAllExceptionMsg(e));
                throw e;
            }
        });
        return voList;
    }


    private List<RecruitingKpiByUserVO> setUpgradeCompanyInfo(ConcurrentMap<String, List<KpiReportCompanyUpgradeToClientVO>> finalCrmCompanyIdInfoMap, RecruitingKpiReportSearchDto searchDto, CopyOnWriteArrayList<RecruitingKpiByUserVO> voList) {
        List<RecruitingKpiByUserVO> otherResult = new ArrayList<>();
        if (!finalCrmCompanyIdInfoMap.isEmpty()) {
            List<String> dateList = voList.stream().map(RecruitingKpiByUserVO::getGroupByDate).collect(Collectors.toList());
            if (searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.USER)) {
                List<Long> userMap = voList.stream().map(RecruitingKpiByUserVO::getUserId).collect(Collectors.toList());
                setNullUpgradeCompanyInfo(userMap, finalCrmCompanyIdInfoMap, searchDto, voList, dateList, otherResult, true);
            } else if (searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.TEAM)) {
                List<Long> teamMap = voList.stream().map(RecruitingKpiByUserVO::getTeamId).collect(Collectors.toList());
                setNullUpgradeCompanyInfo(teamMap, finalCrmCompanyIdInfoMap, searchDto, voList, dateList, otherResult, false);
            } else {
                for (Map.Entry<String, List<KpiReportCompanyUpgradeToClientVO>> entry : finalCrmCompanyIdInfoMap.entrySet()) {
                    if (!dateList.contains(entry.getValue().get(0).getGroupByDate())) {
                        dateList.add(entry.getValue().get(0).getGroupByDate());
                        RecruitingKpiByUserVO vo = new RecruitingKpiByUserVO();
                        vo.setUpgradeToClient(entry.getValue().get(0).getCompanyCount());
                        vo.setGroupByDate(entry.getValue().get(0).getGroupByDate());
                        otherResult.add(vo);
                    }
                }
            }
        }
        return otherResult;
    }

    private void setNullUpgradeCompanyInfo(List<Long> userMap,
                                           ConcurrentMap<String, List<KpiReportCompanyUpgradeToClientVO>> finalCrmCompanyIdInfoMap,
                                           RecruitingKpiReportSearchDto searchDto,
                                           CopyOnWriteArrayList<RecruitingKpiByUserVO> voList,
                                           List<String> dateList,
                                           List<RecruitingKpiByUserVO> otherResult,
                                           Boolean flag) {
        for (Map.Entry<String, List<KpiReportCompanyUpgradeToClientVO>> entry : finalCrmCompanyIdInfoMap.entrySet()) {
            String key = entry.getKey();
            List<KpiReportCompanyUpgradeToClientVO> value = entry.getValue();

            if (!userMap.contains(Long.valueOf(key))) {
                // 如果 userMap 不包含当前 key，则创建新的 RecruitingKpiByUserVO 并添加到 voList
                RecruitingKpiByUserVO vo = createRecruitingKpiByUserVO(value.get(0));
                voList.add(vo);
            } else {
                // 如果 userMap 包含当前 key，则更新 voList 或 otherResult
                updateVoListAndOtherResult(voList, value, searchDto, dateList, otherResult, flag, key);
            }
        }
    }

    private RecruitingKpiByUserVO createRecruitingKpiByUserVO(KpiReportCompanyUpgradeToClientVO companyInfo) {
        RecruitingKpiByUserVO vo = new RecruitingKpiByUserVO();
        vo.setUserId(companyInfo.getUserId());
        vo.setUserName(companyInfo.getUsername());
        vo.setTeamName(companyInfo.getTeamName());
        vo.setTeamId(companyInfo.getTeamId());
        vo.setCreatedCompaniesNum(companyInfo.getCompanyCount());
        vo.setGroupByDate(companyInfo.getGroupByDate());
        return vo;
    }

    private void updateVoListAndOtherResult(CopyOnWriteArrayList<RecruitingKpiByUserVO> voList,
                                            List<KpiReportCompanyUpgradeToClientVO> crmCompanyInfoVOList,
                                            RecruitingKpiReportSearchDto searchDto,
                                            List<String> dateList,
                                            List<RecruitingKpiByUserVO> otherResult,
                                            Boolean flag,
                                            String key) {
        for (RecruitingKpiByUserVO v : voList) {
            if (shouldJoinDateDimension(searchDto)) {
                // 如果需要按日期维度处理
                processWithDateDimension(v, crmCompanyInfoVOList, dateList, otherResult, flag);
            } else {
                // 如果不需要按日期维度处理
                processWithoutDateDimension(v, crmCompanyInfoVOList, flag, key);
            }
        }
    }

    private void processWithDateDimension(RecruitingKpiByUserVO v,
                                          List<KpiReportCompanyUpgradeToClientVO> crmCompanyInfoVOList,
                                          List<String> dateList,
                                          List<RecruitingKpiByUserVO> otherResult,
                                          Boolean flag) {
        for (KpiReportCompanyUpgradeToClientVO z : crmCompanyInfoVOList) {
            if (flag) {
                // 按用户 ID 处理
                if (v.getUserId().equals(z.getUserId())) {
                    if (v.getGroupByDate().equals(z.getGroupByDate())) {
                        v.setCreatedCompaniesNum(z.getCompanyCount());
                    } else if (!dateList.contains(z.getGroupByDate())) {
                        dateList.add(z.getGroupByDate());
                        otherResult.add(createRecruitingKpiByUserVO(z));
                    }
                }
            } else {
                // 按团队 ID 处理
                if (v.getTeamId().equals(z.getTeamId())) {
                    if (v.getGroupByDate().equals(z.getGroupByDate())) {
                        v.setCreatedCompaniesNum(z.getCompanyCount());
                    } else if (!dateList.contains(z.getGroupByDate())) {
                        dateList.add(z.getGroupByDate());
                        otherResult.add(createRecruitingKpiByUserVO(z));
                    }
                }
            }
        }
    }

    private void processWithoutDateDimension(RecruitingKpiByUserVO v,
                                             List<KpiReportCompanyUpgradeToClientVO> crmCompanyInfoVOList,
                                             Boolean flag,
                                             String key) {
        if (flag) {
            // 按用户 ID 处理
            if (v.getUserId().equals(Long.valueOf(key))) {
                v.setCreatedCompaniesNum(crmCompanyInfoVOList.get(0).getCompanyCount());
            }
        } else {
            // 按团队 ID 处理
            if (v.getTeamId().equals(Long.valueOf(key))) {
                v.setCreatedCompaniesNum(crmCompanyInfoVOList.get(0).getCompanyCount());
            }
        }
    }

    private void setCreateCompanyInfoByUserBean(List<KpiReportCompanyUpgradeToClientVO> kpiReportCompanyInfoVOList, RecruitingKpiByUserVO vo, boolean flag) {
        if (null != kpiReportCompanyInfoVOList) {
            if (flag) {
                for (KpiReportCompanyUpgradeToClientVO z : kpiReportCompanyInfoVOList) {
                    if (vo.getGroupByDate().equals(z.getGroupByDate())) {
                        vo.setUpgradeToClient(z.getCompanyCount());
                    }
                }
            } else {
                vo.setUpgradeToClient(kpiReportCompanyInfoVOList.get(0).getCompanyCount());
            }
        }
    }

    private void setUpgradeCompanyInfoByUserBean(List<KpiReportCompanyUpgradeToClientVO> kpiReportCompanyInfoVOList, RecruitingKpiByUserVO vo, boolean flag) {
        if (null != kpiReportCompanyInfoVOList) {
            if (flag) {
                for (KpiReportCompanyUpgradeToClientVO z : kpiReportCompanyInfoVOList) {
                    if (vo.getGroupByDate().equals(z.getGroupByDate())) {
                        vo.setCreatedCompaniesNum(z.getCompanyCount());
                    }
                }
            } else {
                vo.setCreatedCompaniesNum(kpiReportCompanyInfoVOList.get(0).getCompanyCount());
            }
        }
    }

    private void setCreateCompanyInfo(ConcurrentMap<String, List<KpiReportCompanyUpgradeToClientVO>> finalCompanyIdInfoMap,RecruitingKpiReportSearchDto searchDto,CopyOnWriteArrayList<RecruitingKpiByUserVO> voList) {
        if (!finalCompanyIdInfoMap.isEmpty()) {

            if (searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.USER)) {
                Map<Long,List<RecruitingKpiByUserVO>> dateList = voList.stream().collect(Collectors.groupingBy(x-> x.getUserId()));
                List<Long> userIds = voList.stream().map(RecruitingKpiByUserVO::getUserId).collect(Collectors.toList());
                setNullCreateCompanyInfo(finalCompanyIdInfoMap,searchDto,voList,userIds,dateList);
            } else if (searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.TEAM)) {
                Map<Long,List<RecruitingKpiByUserVO>> dateList = voList.stream().collect(Collectors.groupingBy(x-> x.getTeamId()));
                List<Long> teamIds = voList.stream().map(RecruitingKpiByUserVO::getTeamId).collect(Collectors.toList());
                setNullCreateCompanyInfo(finalCompanyIdInfoMap,searchDto,voList,teamIds,dateList);
            }else {
                List<String> dateList = voList.stream().map(RecruitingKpiByUserVO::getGroupByDate).collect(Collectors.toList());
                for (Map.Entry<String, List<KpiReportCompanyUpgradeToClientVO>> entry : finalCompanyIdInfoMap.entrySet()) {
                    if (!dateList.contains(entry.getValue().get(0).getGroupByDate())) {
                        dateList.add(entry.getValue().get(0).getGroupByDate());
                        RecruitingKpiByUserVO vo = new RecruitingKpiByUserVO();
                        vo.setUpgradeToClient(entry.getValue().get(0).getCompanyCount());
                        vo.setGroupByDate(entry.getValue().get(0).getGroupByDate());
                        voList.add(vo);
                    }
                }
            }
        }
    }

    private void setNullCreateCompanyInfo(ConcurrentMap<String, List<KpiReportCompanyUpgradeToClientVO>> finalCompanyIdInfoMap,
                                          RecruitingKpiReportSearchDto searchDto,
                                          CopyOnWriteArrayList<RecruitingKpiByUserVO> voList,
                                          List<Long> ids,Map<Long,List<RecruitingKpiByUserVO>> dateList) {
        for (Map.Entry<String, List<KpiReportCompanyUpgradeToClientVO>> entry : finalCompanyIdInfoMap.entrySet()) {
            if (!ids.contains(Long.valueOf(entry.getKey()))) {
                RecruitingKpiByUserVO vo = new RecruitingKpiByUserVO();
                vo.setUserId(entry.getValue().get(0).getUserId());
                vo.setUserName(entry.getValue().get(0).getUsername());
                vo.setTeamName(entry.getValue().get(0).getTeamName());
                vo.setTeamId(entry.getValue().get(0).getTeamId());
                vo.setUpgradeToClient(entry.getValue().get(0).getCompanyCount());
                vo.setGroupByDate(entry.getValue().get(0).getGroupByDate());
                voList.add(vo);
            } else {
                List<String> dates = dateList.get(Long.valueOf(entry.getKey()))
                        .stream().map(RecruitingKpiByUserVO::getGroupByDate).collect(Collectors.toList());
                if (shouldJoinDateDimension(searchDto)) {
                    if (!dates.contains(entry.getValue().get(0).getGroupByDate())) {
                        dates.add(entry.getValue().get(0).getGroupByDate());
                        RecruitingKpiByUserVO vo = new RecruitingKpiByUserVO();
                        vo.setUserId(entry.getValue().get(0).getUserId());
                        vo.setUserName(entry.getValue().get(0).getUsername());
                        vo.setTeamName(entry.getValue().get(0).getTeamName());
                        vo.setTeamId(entry.getValue().get(0).getTeamId());
                        vo.setUpgradeToClient(entry.getValue().get(0).getCompanyCount());
                        vo.setGroupByDate(entry.getValue().get(0).getGroupByDate());
                        voList.add(vo);
                    }
                }
            }
        }
    }

    @Override
    public List<RecruitingKpiByCompanyVO> searchRecruitingKpiReportByCompany(RecruitingKpiReportSearchDto searchDto) {
        log.info("[apn @{}] search recruiting kpi report by company, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        StopWatch stopWatch = new StopWatch("recruiting kpi report by company");
        stopWatch.start("[1] search company permission data task");
        //init report permission
        getPermissionDTOAndSetCommonParam(searchDto);
        // if group by company, ignore user active status filter
        stopWatch.stop();
        stopWatch.start("[2] search company data task");
        //talent
        CompletableFuture<List<RecruitingKpiCommonCountVO>> talentFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepository.searchTalentKpiByCompany(searchDto), executorService);
        //submit to job
        CompletableFuture<List<? extends RecruitingKpiCommonCountVO>> submitToJobFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepository.searchApplicationKpiByCompany(searchDto, ReportTableType.SUBMIT_TO_JOB), executorService);
        //submit to client
        CompletableFuture<List<? extends RecruitingKpiCommonCountVO>> submitToClientFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepository.searchApplicationKpiByCompany(searchDto, ReportTableType.SUBMIT_TO_CLIENT), executorService);
        //interview
        CompletableFuture<List<? extends RecruitingKpiCommonCountVO>> interviewFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepository.searchApplicationKpiByCompany(searchDto, ReportTableType.INTERVIEW), executorService);
        //offer
        CompletableFuture<List<? extends RecruitingKpiCommonCountVO>> offerFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepository.searchApplicationKpiByCompany(searchDto, ReportTableType.OFFER), executorService);
        //offer accept
        CompletableFuture<List<? extends RecruitingKpiCommonCountVO>> offerAcceptFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepository.searchApplicationKpiByCompany(searchDto, ReportTableType.OFFER_ACCEPT), executorService);
        //onboard
        CompletableFuture<List<? extends RecruitingKpiCommonCountVO>> onboardFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepository.searchApplicationKpiByCompany(searchDto, ReportTableType.ON_BOARD), executorService);
        //eliminate
        CompletableFuture<List<? extends RecruitingKpiCommonCountVO>> eliminateFuture = CompletableFuture.supplyAsync(() -> {
            if (searchDto.isXxlJobFlag()) {
                return new ArrayList<>();
            }
            return recruitingKpiCompanyRepository.searchApplicationKpiByCompany(searchDto, ReportTableType.ELIMINATED);
        }, executorService);
        //job note
        CompletableFuture<List<RecruitingKpiCommonCountVO>> jobNoteFuture = CompletableFuture.supplyAsync(() -> {
            if (searchDto.isXxlJobFlag() || !searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.JOB)) {
                return new ArrayList<>();
            }
            return recruitingKpiCompanyRepository.searchJobNoteKpiByCompany(searchDto);
        }, executorService);
        CompletableFuture.allOf(talentFuture, submitToJobFuture, submitToClientFuture, interviewFuture, offerFuture, offerAcceptFuture, onboardFuture, eliminateFuture, jobNoteFuture).exceptionally(FutureExceptionUtil::handleFutureException);
        stopWatch.stop();
        stopWatch.start("[3] assemble data task");
        boolean jobDetailFlag = searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.JOB);
        Function<RecruitingKpiCommonCountVO, String> function = this.getCompanyMapKey(searchDto.getGroupByFieldList());
        ConcurrentMap<String, RecruitingKpiCommonCountVO> talentMap = talentFuture.join().parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> submitToJobMap = submitToJobFuture.join().parallelStream().collect(Collectors.toConcurrentMap(function, a -> (RecruitingKpiApplicationCommonCountVO) a));
        ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> submitToClientMap = submitToClientFuture.join().parallelStream().collect(Collectors.toConcurrentMap(function, a -> (RecruitingKpiApplicationCommonCountVO) a));
        ConcurrentMap<String, RecruitingKpiInterviewCountVO> interviewMap = interviewFuture.join().parallelStream().collect(Collectors.toConcurrentMap(function, a -> (RecruitingKpiInterviewCountVO) a));
        ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> offerMap = offerFuture.join().parallelStream().collect(Collectors.toConcurrentMap(function, a -> (RecruitingKpiApplicationCommonCountVO) a));
        ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> offerAcceptMap = offerAcceptFuture.join().parallelStream().collect(Collectors.toConcurrentMap(function, a -> (RecruitingKpiApplicationCommonCountVO) a));
        ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> onboardMap = onboardFuture.join().parallelStream().collect(Collectors.toConcurrentMap(function, a -> (RecruitingKpiApplicationCommonCountVO) a));
        ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> eliminateMap = eliminateFuture.join().parallelStream().collect(Collectors.toConcurrentMap(function, a -> (RecruitingKpiApplicationCommonCountVO) a));
        ConcurrentMap<String, RecruitingKpiCommonCountVO> jobNoteMap = jobNoteFuture.join().parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));

        // 返回结果对象
        CopyOnWriteArrayList<RecruitingKpiByCompanyVO> voList = new CopyOnWriteArrayList<>();

        // 获取查询结果keySet
        Set<String> keySet = Stream.of(talentMap, submitToJobMap, submitToClientMap, interviewMap, offerMap, offerAcceptMap, onboardMap, eliminateMap, jobNoteMap).parallel()
                .flatMap(map -> map.keySet().stream())
                .collect(Collectors.toSet());

        // 公司id，查询本周和上周推荐至客户和公司进度备注数
        List<RecruitingKpiByCompanyVO> companyVoList ;

        List<Long> companyIdList ;

        List<? extends RecruitingKpiCommonCountVO> jobList;

        ConcurrentMap<String, RecruitingKpiCommonCountVO> jobMap;

        //job视图 查询job总数 要根据CompanyID查询

        if (null !=searchDto.getViewType() && RecruitingKpiViewType.JOB.equals(searchDto.getViewType())) {
            Pair<List<RecruitingKpiByCompanyVO>,List<Long>> companyPair = saveCompanyId(keySet, searchDto);
            companyVoList = companyPair.getKey();
            companyIdList = companyPair.getValue();
            CompletableFuture<List<? extends RecruitingKpiCommonCountVO>> jobFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepository.searchJobKpiByCompany(searchDto), executorService);
            jobList = jobFuture.join();
            jobMap = jobList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        } else {
            //job
            CompletableFuture<List<? extends RecruitingKpiCommonCountVO>> jobFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepository.searchJobKpiByCompany(searchDto), executorService);
            jobList = jobFuture.join();
            ConcurrentMap<String, RecruitingKpiJobDetailCommonCountVO> jobDetailMap = !jobDetailFlag ? new ConcurrentHashMap<>() : jobList.stream().collect(Collectors.toConcurrentMap(function, a -> (RecruitingKpiJobDetailCommonCountVO) a));
            jobMap = jobList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
            if (BooleanUtil.isTrue(jobDetailFlag)) {
                keySet.addAll(jobDetailMap.keySet());
            } else {
                keySet.addAll(jobMap.keySet());
            }
            Pair<List<RecruitingKpiByCompanyVO>,List<Long>> companyPair = saveCompanyId(keySet, searchDto);
            companyVoList = companyPair.getKey();
            companyIdList = companyPair.getValue();
        }

        CompletableFuture<ConcurrentMap<Long, List<KpiReportCompanySubmitToClientTwoWeekVO>>> twoWeekMapFuture = this.getSubmitToClientWithWeek(searchDto);
        ConcurrentMap<Long, List<KpiReportCompanySubmitToClientTwoWeekVO>> twoWeekMap = twoWeekMapFuture.join();

        CompletableFuture<ConcurrentMap<Long, List<KpiReportCompanyInfoVO>>> companyListMapFuture = getCompanyInfo(companyIdList, searchDto);
        ConcurrentMap<Long, List<KpiReportCompanyInfoVO>> companyIdInfoMap = companyListMapFuture.join();

        CompletableFuture<ConcurrentMap<Long, Long>> progressNoteMapFuture = getBdReportProgressNoteStats(companyIdList, searchDto);
        ConcurrentMap<Long, Long> companyIdCountMap = progressNoteMapFuture.join();

        if (BooleanUtil.isTrue(jobDetailFlag)) {
            ConcurrentMap<Long, RecruitingKpiJobDetailCommonCountVO> jobDetailMapByJobId = jobList.stream().collect(Collectors.toConcurrentMap(RecruitingKpiCommonCountVO::getJobId, a -> (RecruitingKpiJobDetailCommonCountVO) a));
            // job detail 的情况处理
            companyVoList.parallelStream().forEach(vo -> {
                try {
                    // 分组肯定有jobId
                    RecruitingKpiJobDetailCommonCountVO jobDetailVO = jobDetailMapByJobId.get(vo.getJobId());
                    setVoNum(vo, null, talentMap, submitToJobMap, submitToClientMap, interviewMap, null, offerMap, offerAcceptMap, onboardMap, eliminateMap, vo.getKey(), searchDto);
                    if (ObjectUtil.isNotNull(jobDetailVO)){
                        vo.setContacts(jobDetailVO.getContacts());
                        vo.setAssignedUser(jobDetailVO.getAssignedUser());
                        vo.setJobStatus(jobDetailVO.getJobStatus());
                        vo.setJobStartDate(jobDetailVO.getJobStartDate());
                        vo.setJobEndDate(jobDetailVO.getJobEndDate());
                        vo.setContractDuration(jobDetailVO.getContractDuration());
                        vo.setJobCurrency(jobDetailVO.getJobCurrency());
                        vo.setMinimumPayRate(jobDetailVO.getMinimumPayRate());
                        vo.setMaximumPayRate(jobDetailVO.getMaximumPayRate());
                        vo.setOpenings(jobDetailVO.getCountNum());
                        vo.setJobIds(jobDetailVO.getJobId() + "");
                        vo.setJobCooperationStatus(jobDetailVO.getJobCooperationStatus());
                        vo.setPrivateJob(jobDetailVO.isPrivateJob());
                    }
                    vo.setJobNoteNum(jobNoteMap.getOrDefault(vo.getKey(), new RecruitingKpiCommonCountVO()).getCountNum());
                    vo.setBdReportProgressNoteCount(companyIdCountMap.get(vo.getCompanyId()));
                    setCompanyInfo(vo, companyIdInfoMap, twoWeekMap, vo.getJobId());
                    voList.add(vo);
                } catch (Exception e) {
                    log.error(" search kpi assemble data is error, msg = {}", ExceptionUtil.getAllExceptionMsg(e));
                    throw e;
                }
            });
        } else {
            companyVoList.parallelStream().forEach(vo -> {
                try {
                    // 分组肯定有jobId
                    setVoNum(vo, jobMap, talentMap, submitToJobMap, submitToClientMap, interviewMap, null, offerMap, offerAcceptMap, onboardMap, eliminateMap, vo.getKey(), searchDto);
                    vo.setJobNoteNum(jobNoteMap.getOrDefault(vo.getKey(), new RecruitingKpiTalentNoteCountVO()).getCountNum());
                    if (!searchDto.isXxlJobFlag()) {
                        vo.setBdReportProgressNoteCount(companyIdCountMap.get(vo.getCompanyId()));
                        setCompanyInfo(vo, companyIdInfoMap, twoWeekMap, null);
                    }
                    voList.add(vo);
                } catch (Exception e) {
                    log.error(" search kpi assemble data is error, msg = {}", ExceptionUtil.getAllExceptionMsg(e));
                    throw e;
                }
            });
        }

        stopWatch.stop();
        log.info("[apn @{}] searchRecruitingKpiReportByCompany time = {}ms \n {}", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());

        return sortVoListByFields(voList, searchDto.getGroupByFieldList());
    }

    @Override
    public void exportRecruitingKpiReportByUserExcel(RecruitingKpiReportSearchDto searchDto, HttpServletResponse response) {
        // not implement
        throw new UnsupportedOperationException("not implement");
    }

    @Override
    public RecruitingKpiByCompanyVO searchRecruitingKpiTotalByCompany(RecruitingKpiReportSearchDto searchDto) {
        // not implement
        return null;
    }

    @Override
    public void exportRecruitingKpiReportByCompanyExcel(RecruitingKpiReportSearchDto searchDto, HttpServletResponse response) {
        throw new UnsupportedOperationException("not implement");
    }

    protected boolean shouldJoinDateDimension(RecruitingKpiReportSearchDto searchDto) {
        return Stream.of(RecruitingKpiGroupByFieldType.DAY, RecruitingKpiGroupByFieldType.WEEK, RecruitingKpiGroupByFieldType.MONTH, RecruitingKpiGroupByFieldType.QUARTER, RecruitingKpiGroupByFieldType.YEAR)
                .anyMatch(searchDto.getGroupByFieldList()::contains);
    }

    private CompletableFuture<ConcurrentMap<Long, List<KpiReportCompanySubmitToClientTwoWeekVO>>> getSubmitToClientWithWeek(RecruitingKpiReportSearchDto searchDto) {
        return CompletableFuture.supplyAsync(() -> {
            if (searchDto.isXxlJobFlag()) {
                return new ConcurrentHashMap<>();
            }
            return recruitingKpiCompanyRepository.findSubmitToClientWithWeek(searchDto, DataSourceType.MYSQL,false);
        });
    }

    private CompletableFuture<ConcurrentMap<Long, List<KpiReportCompanyInfoVO>>> getCompanyInfo(List<Long> companyIdList,  RecruitingKpiReportSearchDto searchDto) {
        return CompletableFuture.supplyAsync(() -> {
            if (BooleanUtil.isTrue(searchDto.isXxlJobFlag())) {
                return new ConcurrentHashMap<>();
            }
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            List<KpiReportCompanyInfoVO> companyInfoVOList = recruitingKpiCompanyRepository.findCompanyInfoMapBy(companyIdList);
            stopWatch.stop();
            log.info("[apn ] by company kpi getCompanyInfo time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
            return companyInfoVOList.stream().collect(Collectors.groupingByConcurrent(KpiReportCompanyInfoVO::getCompanyId));
        });
    }

    private ConcurrentMap<String, List<KpiReportCompanyUpgradeToClientVO>> getCompanyInfoByUser(RecruitingKpiReportSearchDto searchDto,TeamDataPermissionRespDTO teamDTO) {
        if (BooleanUtil.isTrue(searchDto.isXxlJobFlag()) || BooleanUtil.isTrue(searchDto.isE5ReportFlag())) {
            return new ConcurrentHashMap<>();
        }
        StopWatch stopWatch = new StopWatch("search recruiting kpi report by user, search company info by user");
        if (searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.USER)) {
            stopWatch.start("[1] search company info map by user");
            List<KpiReportCompanyUpgradeToClientVO> companyInfoVOList = recruitingKpiCompanyRepository.findCompanyInfoMapByUserId(searchDto, teamDTO);
            stopWatch.stop();
            log.info("[apn ] by user kpi getCompanyInfoByUser time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
            return companyInfoVOList.stream().collect(Collectors.groupingByConcurrent(t -> t.getUserId().toString()));
        } else if (searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.TEAM)) {
            stopWatch.start("[2] search company info map by team");
            List<KpiReportCompanyUpgradeToClientVO> companyInfoVOList = recruitingKpiCompanyRepository.findCompanyInfoMapByTeamId(searchDto, teamDTO);
            stopWatch.stop();
            log.info("[apn ] by team kpi getCompanyInfoByUser time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
            return companyInfoVOList.stream().collect(Collectors.groupingByConcurrent(t -> t.getTeamId().toString()));
        }

        stopWatch.start("search company info map by day");
        List<KpiReportCompanyUpgradeToClientVO> companyInfoVOList = recruitingKpiCompanyRepository.findCompanyInfoMapByDay(searchDto, teamDTO);
        stopWatch.stop();
        log.info("[apn ] by day kpi getCompanyInfoByUser time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return companyInfoVOList.stream().collect(Collectors.groupingByConcurrent(t -> t.getGroupByDate()));
    }

    private ConcurrentMap<String, List<KpiReportCompanyUpgradeToClientVO>> getCrmCompanyInfoByUser(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        if (BooleanUtil.isTrue(searchDto.isXxlJobFlag()) || BooleanUtil.isTrue(searchDto.isE5ReportFlag())) {
            return new ConcurrentHashMap<>();
        }
        StopWatch stopWatch = new StopWatch("search recruiting kpi report by user, search crm company info by user");

        stopWatch.start("[1] search auth user id list");
        // 获取授权用户 ID 列表
        List<Long> authUserIdList = getAuthUserIdList(searchDto, teamDTO);
        stopWatch.stop();

        stopWatch.start("[2] search crm user id list");
        // 获取用户 ID 列表
        List<Long> userIdList = getUserIdList(searchDto, authUserIdList);
        stopWatch.stop();

        //根据用户或团队查询 未查询到数据
        if (userIdList.contains(-1L)) {
            return new ConcurrentHashMap<>();
        }

        // 检查用户 ID 列表是否为空
        if (userIdList.isEmpty() && !teamDTO.getAll()) {
            return new ConcurrentHashMap<>();
        }

        stopWatch.start("[3] request crm company info by user");
        // 构建请求参数
        JSONObject jsonObject = buildRequestJson(searchDto, userIdList);

        // 发送 HTTP 请求
        HttpResponse response = sendHttpRequest(jsonObject);
        if (response.getCode() != 200) {
            return new ConcurrentHashMap<>();
        }
        stopWatch.stop();

        stopWatch.start("[4] process response data");
        // 处理响应数据
        List<KpiReportCompanyUpgradeToClientVO> clientVOS = processResponse(searchDto, response);
        stopWatch.stop();

        log.info("[apn @{}] search crm company info by user time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        // 根据分组字段返回结果
        return groupClientVosByField(searchDto, clientVOS);
    }

    private List<Long> getAuthUserIdList(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        List<Long> authUserIdList = new ArrayList<>();
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            authUserIdList = getCrmUserIdListByUserIds(Arrays.asList(searchDto.getSearchUserId()), searchDto.getUser());
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            List<Long> teamIdList = teamDTO.getNestedTeamIds().stream().toList();
            authUserIdList = getCrmUserIdListByTeamIds(teamIdList, searchDto.getUser());
        }
        return authUserIdList;
    }

    private List<Long> getUserIdList(RecruitingKpiReportSearchDto searchDto, List<Long> authUserIdList) {
        List<Long> userIdList = new ArrayList<>();
        if (CollUtil.isNotEmpty(searchDto.getUserIdList())) {
            List<Long> crmUserIds = getCrmUserIdListByUserIds(searchDto.getUserIdList(), searchDto.getUser());
            userIdList.addAll(filterCrmUserIds(crmUserIds, authUserIdList));
            if(userIdList.isEmpty()){
                return Arrays.asList(-1L);
            }
        }
        if (CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
            List<Long> crmUserIds = getCrmUserIdListByTeamIds(searchDto.getTeamIdList(), searchDto.getUser());
            userIdList.addAll(filterCrmUserIds(crmUserIds, authUserIdList));
            if(userIdList.isEmpty()){
                return Arrays.asList(-1L);
            }
        }

        if (userIdList.isEmpty() && CollUtil.isEmpty(searchDto.getUserIdList()) && CollUtil.isEmpty(searchDto.getTeamIdList())) {
            userIdList.addAll(authUserIdList);
        }
        return userIdList;
    }

    private List<Long> getCrmUserIdListByUserIds(List<Long> userIds, SearchUserDto user) {
        List<CrmUserIdAndTeamIdVO> voList = recruitingKpiCompanyRepository.findCrmUserIdByUserId(userIds, user);
        return CollUtil.isNotEmpty(voList) ? voList.stream().map(CrmUserIdAndTeamIdVO::getCrmUserId).collect(Collectors.toList()) : Collections.emptyList();
    }

    private List<Long> getCrmUserIdListByTeamIds(List<Long> teamIds, SearchUserDto user) {
        List<CrmUserIdAndTeamIdVO> voList = recruitingKpiCompanyRepository.findCrmUserIdByTeamId(teamIds, user);
        return CollUtil.isNotEmpty(voList) ? voList.stream().map(CrmUserIdAndTeamIdVO::getCrmUserId).collect(Collectors.toList()) : Collections.emptyList();
    }

    private List<Long> filterCrmUserIds(List<Long> crmUserIds, List<Long> authUserIdList) {
        if (authUserIdList.isEmpty()) {
            return crmUserIds;
        }
        return crmUserIds.stream().filter(authUserIdList::contains).collect(Collectors.toList());
    }

    private JSONObject buildRequestJson(RecruitingKpiReportSearchDto searchDto, List<Long> userIdList) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userIdList", userIdList);
        jsonObject.put("fromDate", searchDto.getStartDate());
        jsonObject.put("toDate", searchDto.getEndDate());
        jsonObject.put("timezone", searchDto.getTimezone());
        jsonObject.put("tenantId", SecurityUtils.getTenantId());
        jsonObject.put("containsUser", shouldContainUser(searchDto) ? "1" : "");
        if (shouldJoinDateDimension(searchDto)) {
            jsonObject.put("dateFileType", buildDateFileType(searchDto));
        }
        return jsonObject;
    }

    private boolean shouldContainUser(RecruitingKpiReportSearchDto searchDto) {
        return Stream.of(RecruitingKpiGroupByFieldType.USER, RecruitingKpiGroupByFieldType.TEAM)
                .anyMatch(searchDto.getGroupByFieldList()::contains);
    }

    private String buildDateFileType(RecruitingKpiReportSearchDto searchDto) {
        return searchDto.getGroupByFieldList().stream()
                .map(this::mapGroupByFieldToDateFileType)
                .collect(Collectors.joining(" "));
    }

    private String mapGroupByFieldToDateFileType(RecruitingKpiGroupByFieldType fieldType) {
        switch (fieldType) {
            case DAY: return "d.date group_by_date";
            case WEEK: return "d.start_of_week group_by_date";
            case MONTH: return "DATE_FORMAT(d.start_of_month, '%Y-%m') group_by_date";
            case QUARTER: return "d.quarter_of_year group_by_date";
            case YEAR: return "d.year group_by_date";
            default: return "";
        }
    }

    private HttpResponse sendHttpRequest(JSONObject jsonObject) {
        //String url = "http://localhost:9008/report/api/v1/bd-report/kpi-user/company-info/count";
        String url = crmUrl + "/report/api/v1/bd-report/kpi-user/company-info/count";
        log.info("[apn getKpiUserCompanyInfoList {}] url = {}", SecurityUtils.getUserId(), url);
        Headers headers = Headers.of(Map.of(HttpHeaders.AUTHORIZATION, "Bearer " + SecurityUtils.getCurrentUserToken()));
        try {
            return httpService.post(url, headers, jsonObject.toString());
        } catch (IOException e) {
            log.error("[apn getKpiUserCompanyInfoList {}] search is error, message = {}", SecurityUtils.getUserId(), ExceptionUtil.getAllExceptionMsg(e));
            throw new RuntimeException(e);
        }
    }

    private List<KpiReportCompanyUpgradeToClientVO> processResponse(RecruitingKpiReportSearchDto searchDto, HttpResponse response) {
        List<BDReportKpiUserCompanyInfoVO> result = JSONUtil.toList(JSONUtil.parseArray(response.getBody()), BDReportKpiUserCompanyInfoVO.class);
        List<Long> crmUserIds = result.stream().map(BDReportKpiUserCompanyInfoVO::getUserId).collect(Collectors.toList());

        if (searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.USER)) {
            return processUserGroup(result, crmUserIds, searchDto.getUser(),searchDto);
        } else if (searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.TEAM)) {
            return processTeamGroup(result, crmUserIds, searchDto.getUser(),searchDto);
        } else {
            return processDefaultGroup(result);
        }
    }

    private List<KpiReportCompanyUpgradeToClientVO> processUserGroup(List<BDReportKpiUserCompanyInfoVO> result, List<Long> crmUserIds, SearchUserDto user,RecruitingKpiReportSearchDto searchDto) {
        List<KpiReportCompanyUpgradeToClientVO> clientVOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(crmUserIds)) {
            List<CrmUserIdAndTeamIdVO> teamIdList = recruitingKpiCompanyRepository.findTeamIdAndUserIdByCrmUserId(crmUserIds, user);
            List<CrmUserIdAndTeamIdVO> userList = recruitingKpiCompanyRepository.findUserIdByCrmUserId(crmUserIds, user);
            userList.forEach(v->{
               List<BDReportKpiUserCompanyInfoVO> userData = result.stream()
                        .filter(map -> map.getUserId().equals(v.getCrmUserId())).collect(Collectors.toList());

                if (shouldJoinDateDimension(searchDto)) {

                    Map<String, Long> dateCompanyCount = userData.stream()
                            .collect(Collectors.groupingBy(
                                    BDReportKpiUserCompanyInfoVO::getGroupByDate,
                                    Collectors.mapping(BDReportKpiUserCompanyInfoVO::getCompanyId, Collectors.toSet()) // 去重
                            ))
                            .entrySet().stream()
                            .collect(Collectors.toMap(Map.Entry::getKey, entry -> (long) entry.getValue().size()));
                    dateCompanyCount.forEach((k,c)->{
                        KpiReportCompanyUpgradeToClientVO vo = buildClientVO(v, userData.get(0));
                        vo.setCompanyCount(c);
                        vo.setGroupByDate(k);
                        teamIdList.stream()
                                .filter(z -> z.getCrmUserId().equals(v.getCrmUserId()))
                                .findFirst()
                                .ifPresent(z -> {
                                    vo.setTeamId(z.getTeamId());
                                    vo.setTeamName(z.getTeamName());
                                });
                        clientVOS.add(vo);
                    });

                } else {

                    KpiReportCompanyUpgradeToClientVO vo = buildClientVO(v, userData.get(0));
                    Map<Long, Long> userCompanyCount = userData.stream()
                            .collect(Collectors.groupingBy(
                                    BDReportKpiUserCompanyInfoVO::getUserId,
                                    Collectors.mapping(BDReportKpiUserCompanyInfoVO::getCompanyId, Collectors.toSet()) // 去重
                            ))
                            .entrySet().stream()
                            .collect(Collectors.toMap(Map.Entry::getKey, entry -> (long) entry.getValue().size()));
                    vo.setCompanyCount(userCompanyCount.get(userData.get(0).getUserId()));
                    teamIdList.stream()
                            .filter(z -> z.getCrmUserId().equals(v.getCrmUserId()))
                            .findFirst()
                            .ifPresent(z -> {
                                vo.setTeamId(z.getTeamId());
                                vo.setTeamName(z.getTeamName());
                            });
                    clientVOS.add(vo);
                }
            });
        }
        return clientVOS;
    }

    private KpiReportCompanyUpgradeToClientVO buildClientVO(CrmUserIdAndTeamIdVO v, BDReportKpiUserCompanyInfoVO map) {
        KpiReportCompanyUpgradeToClientVO vo = new KpiReportCompanyUpgradeToClientVO();
        vo.setUserId(v.getUserId());
        vo.setUsername(v.getUsername());
        vo.setId(v.getId());
        vo.setCompanyCount(map.getCompanyCount());
        vo.setGroupByDate(map.getGroupByDate());
        return vo;
    }

    private List<KpiReportCompanyUpgradeToClientVO> processTeamGroup(List<BDReportKpiUserCompanyInfoVO> result, List<Long> crmUserIds, SearchUserDto user,RecruitingKpiReportSearchDto searchDto) {
        List<KpiReportCompanyUpgradeToClientVO> clientVOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(crmUserIds)) {
            List<CrmUserIdAndTeamIdVO> teamIdList = recruitingKpiCompanyRepository.findTeamIdAndUserIdByCrmUserId(crmUserIds, user);
            Map<Long, List<CrmUserIdAndTeamIdVO>> crmUserMap = teamIdList.stream().collect(Collectors.groupingBy(CrmUserIdAndTeamIdVO::getTeamId));
            crmUserMap.forEach((teamId, teamUsers) -> {

                List<BDReportKpiUserCompanyInfoVO> teamUserAll = new ArrayList<>();
                teamUsers.forEach(a -> {
                    teamUserAll.addAll(result.stream()
                            .filter(map -> map.getUserId().equals(a.getCrmUserId())).collect(Collectors.toList()));
                });

                if (shouldJoinDateDimension(searchDto)) {

                    Map<String, Long> dateCompanyCount = teamUserAll.stream()
                            .collect(Collectors.groupingBy(
                                    BDReportKpiUserCompanyInfoVO::getGroupByDate,
                                    Collectors.mapping(BDReportKpiUserCompanyInfoVO::getCompanyId, Collectors.toSet()) // 去重
                            ))
                            .entrySet().stream()
                            .collect(Collectors.toMap(Map.Entry::getKey, entry -> (long) entry.getValue().size()));

                    dateCompanyCount.forEach((k,v)->{
                        KpiReportCompanyUpgradeToClientVO vo = new KpiReportCompanyUpgradeToClientVO();
                        vo.setCompanyCount(v);
                        vo.setGroupByDate(k);
                        vo.setTeamId(teamId);
                        vo.setTeamName(teamUsers.get(0).getTeamName());
                        vo.setId(teamUsers.get(0).getId());
                        clientVOS.add(vo);
                    });

                } else {
                    KpiReportCompanyUpgradeToClientVO vo = new KpiReportCompanyUpgradeToClientVO();
                    List<Long> companyIds = teamUserAll.stream().map(BDReportKpiUserCompanyInfoVO::getCompanyId).distinct().collect(Collectors.toList());
                    if (!companyIds.isEmpty()) {
                        vo.setCompanyCount((long) companyIds.size());
                    } else {
                        vo.setCompanyCount(0L);
                    }
                    vo.setTeamId(teamId);
                    vo.setTeamName(teamUsers.get(0).getTeamName());
                    vo.setId(teamUsers.get(0).getId());
                    clientVOS.add(vo);
                }
            });
        }
        return clientVOS;
    }

    private List<KpiReportCompanyUpgradeToClientVO> processDefaultGroup(List<BDReportKpiUserCompanyInfoVO> result) {
        return result.stream()
                .map(map -> {
                    KpiReportCompanyUpgradeToClientVO vo = new KpiReportCompanyUpgradeToClientVO();
                    vo.setCompanyCount(map.getCompanyCount());
                    vo.setGroupByDate(map.getGroupByDate());
                    return vo;
                })
                .collect(Collectors.toList());
    }

    private ConcurrentMap<String, List<KpiReportCompanyUpgradeToClientVO>> groupClientVosByField(RecruitingKpiReportSearchDto searchDto, List<KpiReportCompanyUpgradeToClientVO> clientVOS) {
        if (searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.USER)) {
            return clientVOS.stream().collect(Collectors.groupingByConcurrent(t -> t.getUserId().toString()));
        } else if (searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.TEAM)) {
            return clientVOS.stream().collect(Collectors.groupingByConcurrent(t -> t.getTeamId().toString()));
        }
        return clientVOS.stream().collect(Collectors.groupingByConcurrent(t -> t.getGroupByDate()));
    }
}
