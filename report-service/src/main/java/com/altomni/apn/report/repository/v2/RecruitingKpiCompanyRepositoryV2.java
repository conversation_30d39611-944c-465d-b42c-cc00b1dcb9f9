package com.altomni.apn.report.repository.v2;

import cn.hutool.core.util.BooleanUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiApplicationStatusType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.vo.recruiting.KpiReportCompanyInfoVO;
import com.altomni.apn.common.vo.recruiting.KpiReportCompanySubmitToClientTwoWeekVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiCommonCountVO;
import com.altomni.apn.common.vo.recruiting.v2.RecruitingKpiApplicationCountVO;
import com.altomni.apn.common.vo.recruiting.v2.RecruitingKpiNoteCountVO;
import com.altomni.apn.report.domain.enumeration.DataSourceType;
import com.altomni.apn.report.repository.RecruitingKpiCompanyRepository;
import lombok.extern.slf4j.Slf4j;
import org.jooq.*;
import org.jooq.Record;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.report.repository.v2.Constants.*;
import static com.altomni.apn.report.repository.v2.RecruitingKpiConditionBuilder.*;
import static org.jooq.impl.DSL.*;

@Slf4j
@Service
public class RecruitingKpiCompanyRepositoryV2 {

    private final QueryExecutor queryExecutor;

    private final RecruitingKpiCompanyRepository recruitingKpiCompanyRepository;

    public RecruitingKpiCompanyRepositoryV2(QueryExecutor queryExecutor,RecruitingKpiCompanyRepository recruitingKpiCompanyRepository) {
        this.queryExecutor = queryExecutor;
        this.recruitingKpiCompanyRepository = recruitingKpiCompanyRepository;
    }

    /**
     * 流程相关 kpi 指标
     */
    public List<RecruitingKpiApplicationCountVO> searchApplicationKpiData(RecruitingKpiReportSearchDto searchDto) {
        RecruitingKpiDateType dateType = searchDto.getDateType();
        Field<String> dateField = RecruitingKpiDateType.ADD.equals(dateType) ? ADD_DATE : EVENT_DATE;
        Supplier<Condition> dataPermissionCondition = () -> buildApplicationCondition(searchDto);
        List<Field<?>> metrics;
        Table<?> queryTable;
        if (searchDto.getApplicationStatusType().equals(RecruitingKpiApplicationStatusType.CURRENT)) {
            metrics = APPLICATION_CURRENT_METRICS;
            queryTable = VIEW_APPLICATION_CURRENT_API;
        } else {
            metrics = APPLICATION_FUNNEL_METRICS;
            queryTable = VIEW_APPLICATION_FUNNEL_API;
        }
        SelectHavingStep<Record> query = buildKpiQuery(searchDto, dateField, queryTable, metrics, dataPermissionCondition);
        return queryExecutor.executeKpiQuery(query, RecruitingKpiApplicationCountVO.class, "searchApplicationKpiData");
    }

    /**
     * 流程相关 kpi 指标 只查询维度没有指标
     */
    public List<RecruitingKpiApplicationCountVO> searchApplicationKpiCompanyData(RecruitingKpiReportSearchDto searchDto) {
        RecruitingKpiDateType dateType = searchDto.getDateType();
        Field<String> dateField = RecruitingKpiDateType.ADD.equals(dateType) ? ADD_DATE : EVENT_DATE;
        Supplier<Condition> dataPermissionCondition = () -> buildApplicationCondition(searchDto);
        SelectHavingStep<Record> query = buildKpiQuery(searchDto, dateField, VIEW_APPLICATION_FUNNEL_API, new ArrayList<>(), dataPermissionCondition);
        return queryExecutor.executeKpiQuery(query, RecruitingKpiApplicationCountVO.class, "searchApplicationKpiCompanyData");
    }

    /**
     * job相关指标
     */
    public List<? extends RecruitingKpiCommonCountVO> searchJobKpiByCompany(RecruitingKpiReportSearchDto searchDto,boolean jobDetailFlag) {
        if (BooleanUtil.isFalse(jobDetailFlag)) {
            return recruitingKpiCompanyRepository.searchJobKpi(searchDto, DataSourceType.STARROCKS);
        } else {
            return recruitingKpiCompanyRepository.searchJobDetailKpi(searchDto,DataSourceType.STARROCKS);
        }
    }

    /**
     * 候选人相关指标
     * @param searchDto
     * @param jobDetailFlag
     * @return
     */
    public List<RecruitingKpiCommonCountVO> searchTalentKpiByCompany(RecruitingKpiReportSearchDto searchDto,boolean jobDetailFlag) {
        return recruitingKpiCompanyRepository.searchTalentKpi(searchDto, jobDetailFlag, DataSourceType.STARROCKS);
    }

    /**
     * jobNote相关指标
     * @param searchDto
     * @param jobDetailFlag
     * @return
     */
    public List<RecruitingKpiCommonCountVO> searchJobNoteKpi(RecruitingKpiReportSearchDto searchDto,boolean jobDetailFlag) {
        return recruitingKpiCompanyRepository.searchJobNoteKpi(searchDto, jobDetailFlag, DataSourceType.STARROCKS);
    }

    /**
     * 查询submit to client 本周和上周
     * @param searchDto
     * @return
     */
    public CompletableFuture<ConcurrentMap<Long, List<KpiReportCompanySubmitToClientTwoWeekVO>>> getSubmitToClientWithWeek(RecruitingKpiReportSearchDto searchDto,boolean isCount) {
        return CompletableFuture.supplyAsync(() -> {
            if (searchDto.isXxlJobFlag()) {
                return new ConcurrentHashMap<>();
            }
            return recruitingKpiCompanyRepository.findSubmitToClientWithWeek(searchDto, DataSourceType.STARROCKS,isCount);
        });
    }

    /**
     * 补全公司信息
     * @param companyIdList
     * @param searchDto
     * @return
     */
    public CompletableFuture<ConcurrentMap<Long, List<KpiReportCompanyInfoVO>>> getCompanyInfo(List<Long> companyIdList, RecruitingKpiReportSearchDto searchDto) {
        return CompletableFuture.supplyAsync(() -> {
            if (BooleanUtil.isTrue(searchDto.isXxlJobFlag())) {
                return new ConcurrentHashMap<>();
            }
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            List<KpiReportCompanyInfoVO> companyInfoVOList = recruitingKpiCompanyRepository.findCompanyInfoMapByStarrocks(companyIdList);
            stopWatch.stop();
            log.info("[apn ] by company kpi getCompanyInfo time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
            return companyInfoVOList.stream().collect(Collectors.groupingByConcurrent(KpiReportCompanyInfoVO::getCompanyId));
        });
    }



    /**
     * 通用的 KPI 查询构建方法
     */
    private SelectHavingStep<Record> buildKpiQuery(RecruitingKpiReportSearchDto searchDto,
                                                   Field<String> dateField,
                                                   Table<?> fromTable,
                                                   List<Field<?>> metrics,
                                                   Supplier<Condition> dataPermissionCondition) {
        String timezone = searchDto.getTimezone();
        //group by
        List<Field<?>> dimensions = buildDimensions(searchDto, dateField, timezone);

        //查询维度
        List<Field<?>> searchDimensions = buildSearchDimensions(searchDto, dateField, timezone);

        // 构建基础条件
        Condition condition = buildBaseCondition(searchDto, dateField);
        // 构建数据权限条件
        Condition dataPermission = dataPermissionCondition.get();
        // 最终查询条件
        Condition finalCondition = condition.and(dataPermission);

        // 构建查询
        SelectJoinStep<Record> query = dsl.select(Stream.concat(searchDimensions.stream(), metrics.stream()).toList())
                .from(fromTable).innerJoin(table("company").as("c"))
                .on(field("c.id").eq(field("application_kpi.company_id")))
                .innerJoin(table("job").as("j"))
                .on(field("j.id").eq(field("application_kpi.job_id")))
                .innerJoin(table("company_user_relation").as("ul"))
                .on(field("ul.company_id ").eq(field("c.id")))
                .innerJoin(table("permission_user_team").as("put"))
                .on(field("put.user_id ").eq(field("ul.user_id")).and(field("put.is_primary").eq("1")))
                .innerJoin(table("permission_team").as("pt"))
                .on(field("pt.id ").eq(field("put.team_id")))
                .innerJoin(table("user").as("u"))
                .on(field("u.id ").eq(field("ul.user_id")));

        return query.where(finalCondition).groupBy(dimensions);
    }

    /**
     * 构建基础查询条件
     */
    private Condition buildBaseCondition(RecruitingKpiReportSearchDto searchDto, Field<String> dateField) {
        Condition condition = PREFIX_APP_TENANT_ID.eq(searchDto.getSearchTenantId())
                .and(dateField.between(getUtcByTimeZone(searchDto.getStartDate() + " 00:00:00", searchDto.getTimezone()),
                        getUtcByTimeZone(searchDto.getEndDate() + " 23:59:59", searchDto.getTimezone())));
        return condition.and(userSearchConditionByCompany(searchDto));
    }

    public String getUtcByTimeZone(String time, String timezone) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(time, formatter);
        return dateTime.atZone(ZoneId.of(timezone)).withZoneSameInstant(ZoneOffset.UTC).format(formatter);
    }

    private Condition buildApplicationCondition(RecruitingKpiReportSearchDto searchDto) {
        Condition condition = trueCondition();
        Condition permissionCondition = reportByCompanyDataPermissionCondition(searchDto,searchDto.getPermissionRespDTO());
        Optional<Condition> jobCondition = jobSearchCondition(searchDto);
        Optional<Condition> companyCondition = companySearchConditionByCompanyAndUserRole(searchDto);
        if (jobCondition.isPresent()) {
            condition = condition.and(jobCondition.get());
        }
        if (companyCondition.isPresent()) {
            condition = condition.and(companyCondition.get());
        }

        return condition.and(permissionCondition);
    }

    /**
     * 构建维度字段列表
     */
    private List<Field<?>> buildSearchDimensions(RecruitingKpiReportSearchDto searchDto, Field<String> dateField, String timezone) {
        return searchDto.getGroupByFieldList().stream()
                .flatMap(groupByField -> switch (groupByField) {
                    case COMPANY -> Stream.of(PREFIX_APP_COMPANY_ID,COMPANY_NAME_AS);
                    case JOB -> Stream.of(PREFIX_APP_JOB_ID,JOB_NAME_AS);
                    case TENANT -> Stream.of(PREFIX_APP_TENANT_ID);
                    case USER -> Stream.of(field("u.id as userId", Long.class), field("concat(u.first_name, ' ', u.last_name) as userName", String.class));
                    case TEAM -> Stream.of(field("put.team_id as teamId", Long.class), field("pt.name as teamName", String.class));
                    case DAY -> Stream.of(buildDateField("day", dateField, timezone));
                    case WEEK -> Stream.of(buildDateField("week", dateField, timezone));
                    case MONTH -> Stream.of(buildDateField("month", dateField, timezone));
                    case YEAR -> Stream.of(buildDateField("year", dateField, timezone));
                    case QUARTER -> Stream.of(buildDateField("quarter", dateField, timezone));
                }).toList();
    }

    /**
     * 构建维度字段列表
     */
    private List<Field<?>> buildDimensions(RecruitingKpiReportSearchDto searchDto, Field<String> dateField, String timezone) {
        return searchDto.getGroupByFieldList().stream()
                .flatMap(groupByField -> switch (groupByField) {
                    case COMPANY -> Stream.of(PREFIX_APP_COMPANY_ID,COMPANY_NAME);
                    case JOB -> Stream.of(PREFIX_APP_JOB_ID,JOB_NAME);
                    case TENANT -> Stream.of(PREFIX_APP_TENANT_ID);
                    case USER -> Stream.of(field("u.id", Long.class), field("concat(u.first_name, ' ', u.last_name)", String.class));
                    case TEAM -> Stream.of(field("put.team_id", Long.class), field("pt.name", String.class));
                    case DAY -> Stream.of(buildDateField("day", dateField, timezone));
                    case WEEK -> Stream.of(buildDateField("week", dateField, timezone));
                    case MONTH -> Stream.of(buildDateField("month", dateField, timezone));
                    case YEAR -> Stream.of(buildDateField("year", dateField, timezone));
                    case QUARTER -> Stream.of(buildDateField("quarter", dateField, timezone));
                }).toList();
    }

    /**
     * 构建日期维度字段
     */
    private Field<?> buildDateField(String period, Field<String> dateField, String timezone) {
        String convertedDate = convertTz(dateField.getName(), timezone);
        return switch (period) {
            case "day" -> field("DATE_FORMAT(DATE_TRUNC('day', %s), '%%Y-%%m-%%d')".formatted(convertedDate)).as(DATE_DIM_ALIAS);
            case "week" -> field("DATE_FORMAT(DATE_TRUNC('week', %s), '%%Y-%%m-%%d')".formatted(convertedDate)).as(DATE_DIM_ALIAS);
            case "month" ->
                    field("DATE_FORMAT(DATE_TRUNC('month', %s), '%%Y-%%m')".formatted(convertedDate)).as(DATE_DIM_ALIAS);
            case "year" -> field("DATE_FORMAT(DATE_TRUNC('year', %s), '%%Y')".formatted(convertedDate)).as(DATE_DIM_ALIAS);
            case "quarter" ->
                    field("CONCAT(YEAR(%s),'-', QUARTER(%s))".formatted(convertedDate, convertedDate)).as(DATE_DIM_ALIAS);
            default -> throw new IllegalArgumentException("Unsupported period: " + period);
        };
    }

    private static String convertTz(String dataColumn, String timezone) {
        return "CONVERT_TZ(%s, 'UTC', '%s')".formatted(dataColumn, timezone);
    }

    private static Field<Integer> bitmap_union_count(Field<Integer> field) {
        return field("BITMAP_UNION_COUNT(%s)".formatted(field.getName()), Integer.class);
    }

    private static Field<String> bitmap_union_to_string(Field<?> field) {
        return field("BITMAP_TO_STRING(BITMAP_UNION(%s))".formatted(field.getName()), String.class);
    }

    // 指标定义移到常量部分
    private static final List<Field<?>> NOTE_METRICS = List.of(
            bitmap_union_count(field("callNoteNum", Integer.class)).as("callNoteNum"),
            bitmap_union_count(field("personNoteNum", Integer.class)).as("personNoteNum"),
            bitmap_union_count(field("otherNoteNum", Integer.class)).as("otherNoteNum"),
            bitmap_union_count(field("emailNoteNum", Integer.class)).as("emailNoteNum"),
            bitmap_union_count(field("videoNoteNum", Integer.class)).as("videoNoteNum"),
            bitmap_union_count(field("iciNum", Integer.class)).as("iciNum"),
            bitmap_union_count(field("noteCount", Integer.class)).as("noteCount"),
            bitmap_union_to_string(field("unique_talent_ids")).as("uniqueTalentIds"),
            bitmap_union_count(field("application_note_count_num", Integer.class)).as("applicationNoteCountNum"),
            bitmap_union_count(field("talent_tracking_note_count_num", Integer.class)).as("talentTrackingNoteCountNum"),
            bitmap_union_to_string(field("talent_tracking_note_ids")).as("talentTrackingNoteIds")
    );

    private static final List<Field<?>> APPLICATION_FUNNEL_METRICS = createKpiFunnelMetrics();

    private static final List<Field<?>> APPLICATION_CURRENT_METRICS = createCurrentKpiMetrics();



}
