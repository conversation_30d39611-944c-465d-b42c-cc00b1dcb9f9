package com.altomni.apn.report.repository.v2;

import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiApplicationStatusType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiCommonCountVO;
import com.altomni.apn.common.vo.recruiting.v2.KpiReportCreatedVO;
import com.altomni.apn.common.vo.recruiting.v2.RecruitingKpiApplicationCountVO;
import com.altomni.apn.common.vo.recruiting.v2.RecruitingKpiNoteCountVO;
import com.altomni.apn.common.vo.recruiting.v2.ReserveInterviewVO;
import lombok.extern.slf4j.Slf4j;
import org.jooq.*;
import org.jooq.Record;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Stream;

import static com.altomni.apn.report.repository.v2.Constants.*;
import static com.altomni.apn.report.repository.v2.RecruitingKpiConditionBuilder.*;
import static org.jooq.impl.DSL.*;

@Slf4j
@Service
public class RecruitingKpiUserRepositoryV2 {

    private final QueryExecutor queryExecutor;

    public RecruitingKpiUserRepositoryV2(QueryExecutor queryExecutor) {
        this.queryExecutor = queryExecutor;
    }

    /**
     * 流程相关 kpi 指标
     */
    public List<RecruitingKpiApplicationCountVO> searchApplicationKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        RecruitingKpiDateType dateType = searchDto.getDateType();
        Field<String> dateField = RecruitingKpiDateType.ADD.equals(dateType) ? ADD_DATE : EVENT_DATE;
        Supplier<Condition> dataPermissionCondition = () -> buildApplicationCondition(searchDto, teamDTO);
        List<Field<?>> metrics;
        Table<?> queryTable;
        if (searchDto.getApplicationStatusType().equals(RecruitingKpiApplicationStatusType.CURRENT)) {
            metrics = APPLICATION_CURRENT_METRICS;
            queryTable = VIEW_APPLICATION_CURRENT_API;
        } else {
            metrics = APPLICATION_FUNNEL_METRICS;
            queryTable = VIEW_APPLICATION_FUNNEL_API;
        }
        SelectHavingStep<Record> query = buildKpiQuery(searchDto, dateField, queryTable, metrics, dataPermissionCondition);
        return queryExecutor.executeKpiQuery(query, RecruitingKpiApplicationCountVO.class, "searchApplicationKpiData");
    }


    /**
     * 预约面试相关 kpi 指标, 预约面试指标不受事件时间\操作时间影响，统一都是按照操作时间
     */
    public List<ReserveInterviewVO> searchReserveInterviewKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        Supplier<Condition> dataPermissionCondition = () -> buildApplicationCondition(searchDto, teamDTO);
        List<Field<?>> metrics;
        Table<?> queryTable;
        if (searchDto.getApplicationStatusType().equals(RecruitingKpiApplicationStatusType.CURRENT)) {
            metrics = RESERVE_INTERVIEW_CURRENT_METRICS;
            queryTable = VIEW_APPLICATION_CURRENT_API;
        } else {
            metrics = RESERVE_INTERVIEW_FUNNEL_METRICS;
            queryTable = VIEW_APPLICATION_FUNNEL_API;
        }
        SelectHavingStep<Record> query = buildKpiQuery(searchDto, ADD_DATE, queryTable, metrics, dataPermissionCondition);
        return queryExecutor.executeKpiQuery(query, ReserveInterviewVO.class, "searchReserveInterviewKpiData");
    }


    /**
     * 备注相关 kpi 指标
     */
    public List<RecruitingKpiNoteCountVO> searchNoteKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        Supplier<Condition> dataPermissionCondition = () -> reportDataPermissionCondition(searchDto, teamDTO);
        List<Field<?>> noteMetrics = new ArrayList<>(NOTE_METRICS);
        if (!searchDto.isE5ReportFlag()) {
            Set<String> metricsForE5 = Set.of("uniqueTalentIds", "talentTrackingNoteIds");
            noteMetrics.removeIf(field -> metricsForE5.contains(field.getName()));
        }
        SelectHavingStep<Record> query = buildKpiQuery(searchDto, ADD_DATE, VIEW_NOTES_KPI, noteMetrics, dataPermissionCondition);
        return queryExecutor.executeKpiQuery(query, RecruitingKpiNoteCountVO.class, "searchNoteKpiData");
    }

    /**
     * 创建公司、职位、候选人相关 kpi 指标
     */
    public List<KpiReportCreatedVO> searchCreatedKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        List<Field<?>> metrics = List.of(
                bitmap_union_count(field("created_company_count", Integer.class)).as("createdCompanyCount"),
                bitmap_union_count(field("upgrade_company_count", Integer.class)).as("upgradeCompanyCount"),
                bitmap_union_count(field("created_talent_count", Integer.class)).as("createdTalentCount"));

        Field<String> dateField = RecruitingKpiDateType.ADD.equals(searchDto.getDateType()) ? ADD_DATE : EVENT_DATE;
        Supplier<Condition> dataPermissionCondition = () -> reportDataPermissionCondition(searchDto, teamDTO);
        SelectHavingStep<Record> query = buildKpiQuery(searchDto, dateField, VIEW_CREATED__KPI, metrics, dataPermissionCondition);
        return queryExecutor.executeKpiQuery(query, KpiReportCreatedVO.class, "searchCreatedDataMapKpiData");
    }


    /**
     * 查询 job openings 相关指标（使用子查询去重）
     */
    public List<RecruitingKpiCommonCountVO> searchJobOpeningsKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        Field<String> dateField = RecruitingKpiDateType.ADD.equals(searchDto.getDateType()) ? ADD_DATE : EVENT_DATE;
        String timezone = searchDto.getTimezone();
        List<Field<?>> dimensions = buildDimensions(searchDto, dateField, timezone);

        // 构建基础条件
        Condition condition = buildBaseCondition(searchDto, dateField);
        Condition dataPermission = reportDataPermissionCondition(searchDto, teamDTO);
        Condition finalCondition = condition.and(dataPermission);

        // 构建子查询
        List<Field<?>> subQueryFields = new ArrayList<>();
        searchDto.getGroupByFieldList().stream()
                .flatMap(groupByField -> switch (groupByField) {
                    case TENANT -> Stream.of(TENANT_ID);
                    case USER -> Stream.of(USER_ID, USER_NAME);
                    case TEAM -> Stream.of(TEAM_ID, TEAM_NAME, TEAM_PARENT_ID, TEAM_LEVEL, TEAM_IS_LEAF);
                    default -> Stream.empty();
                }).forEach(subQueryFields::add);
        subQueryFields.add(dateField);
        subQueryFields.add(field("openings", Integer.class));

        SelectConditionStep<Record> subQuery = dsl.select(subQueryFields)
                .from(VIEW_CREATED__KPI)
                .where(finalCondition.and(field("openings", Integer.class).isNotNull()));

        // 主查询：对子查询结果进行聚合
        List<Field<?>> mainQueryFields = new ArrayList<>(dimensions);
        mainQueryFields.add(sum(field("openings", Integer.class)).as("countNum"));

        SelectHavingStep<Record> mainQuery = dsl.select(mainQueryFields)
                .from(subQuery.groupBy(subQueryFields).asTable("temp"))
                .groupBy(dimensions);

        return queryExecutor.executeKpiQuery(mainQuery, RecruitingKpiCommonCountVO.class, "searchJobOpeningsKpiData");
    }

    /**
     * 通用的 KPI 查询构建方法
     */
    private SelectHavingStep<Record> buildKpiQuery(RecruitingKpiReportSearchDto searchDto,
                                                   Field<String> dateField,
                                                   Table<?> fromTable,
                                                   List<Field<?>> metrics,
                                                   Supplier<Condition> dataPermissionCondition) {
        String timezone = searchDto.getTimezone();
        List<Field<?>> dimensions = buildDimensions(searchDto, dateField, timezone);

        // 构建基础条件
        Condition condition = buildBaseCondition(searchDto, dateField);
        // 构建数据权限条件
        Condition dataPermission = dataPermissionCondition.get();
        // 最终查询条件
        Condition finalCondition = condition.and(dataPermission);

        // 构建查询
        SelectJoinStep<Record> query = dsl.select(Stream.concat(dimensions.stream(), metrics.stream()).toList())
                .from(fromTable);

        return query.where(finalCondition).groupBy(dimensions);
    }

    /**
     * 构建基础查询条件
     */
    private Condition buildBaseCondition(RecruitingKpiReportSearchDto searchDto, Field<String> dateField) {
        Condition condition = TENANT_ID.eq(searchDto.getSearchTenantId())
                .and(dateField.between(getUtcByTimeZone(searchDto.getStartDate() + " 00:00:00", searchDto.getTimezone()),
                        getUtcByTimeZone(searchDto.getEndDate() + " 23:59:59", searchDto.getTimezone())));
        return condition.and(userSearchCondition(searchDto));
    }

    private Condition buildApplicationCondition(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        Condition condition = trueCondition();
        Condition permissionCondition = reportDataPermissionCondition(searchDto, teamDTO);
        Optional<Condition> jobCondition = jobSearchCondition(searchDto);
        Optional<Condition> companyCondition = companySearchCondition(searchDto);

        if (jobCondition.isPresent()) {
            condition = condition.and(jobCondition.get());
        }
        if (companyCondition.isPresent()) {
            condition = condition.and(companyCondition.get());
        }

        return condition.and(permissionCondition).and(userRoleCondition(searchDto));
    }

    private Optional<Condition> teamCategoryCondition(RecruitingKpiReportSearchDto searchDto) {
        // 声明为 SelectConditionStep 类型
        SelectConditionStep<Record1<Long>> teamSubQuery = dsl.selectDistinct(field("t.id", Long.class))
                .from(table("permission_team").as("t"))
                .where(field("t.team_category_id").in(List.of(15,20)));

        // 返回符合条件的team_id列表
        return Optional.of(TEAM_ID.in(teamSubQuery));
    }

    /**
     * 构建维度字段列表
     */
    private List<Field<?>> buildDimensions(RecruitingKpiReportSearchDto searchDto, Field<String> dateField, String timezone) {
        return searchDto.getGroupByFieldList().stream()
                .flatMap(groupByField -> switch (groupByField) {
                    case COMPANY, JOB -> Stream.empty();
                    case TENANT -> Stream.of(TENANT_ID);
                    case USER -> Stream.of(USER_ID, USER_NAME);
                    case TEAM -> Stream.of(TEAM_ID, TEAM_NAME, TEAM_PARENT_ID, TEAM_LEVEL, TEAM_IS_LEAF);
                    case DAY -> Stream.of(buildDateField("day", dateField, timezone));
                    case WEEK -> Stream.of(buildDateField("week", dateField, timezone));
                    case MONTH -> Stream.of(buildDateField("month", dateField, timezone));
                    case YEAR -> Stream.of(buildDateField("year", dateField, timezone));
                    case QUARTER -> Stream.of(buildDateField("quarter", dateField, timezone));
                }).toList();
    }

    /**
     * 构建日期维度字段
     */
    private Field<?> buildDateField(String period, Field<String> dateField, String timezone) {
        String convertedDate = convertTz(dateField.getName(), timezone);
        return switch (period) {
            case "day" -> field("DATE_FORMAT(DATE_TRUNC('day', %s), '%%Y-%%m-%%d')".formatted(convertedDate)).as(DATE_DIM_ALIAS);
            case "week" -> field("DATE_FORMAT(DATE_TRUNC('week', %s), '%%Y-%%m-%%d')".formatted(convertedDate)).as(DATE_DIM_ALIAS);
            case "month" ->
                    field("DATE_FORMAT(DATE_TRUNC('month', %s), '%%Y-%%m')".formatted(convertedDate)).as(DATE_DIM_ALIAS);
            case "year" -> field("DATE_FORMAT(DATE_TRUNC('year', %s), '%%Y')".formatted(convertedDate)).as(DATE_DIM_ALIAS);
            case "quarter" ->
                    field("CONCAT(YEAR(%s),'-', QUARTER(%s))".formatted(convertedDate, convertedDate)).as(DATE_DIM_ALIAS);
            default -> throw new IllegalArgumentException("Unsupported period: " + period);
        };
    }

    private static String convertTz(String dataColumn, String timezone) {
        return "CONVERT_TZ(%s, 'UTC', '%s')".formatted(dataColumn, timezone);
    }

    private static Field<Integer> bitmap_union_count(Field<Integer> field) {
        return field("BITMAP_UNION_COUNT(%s)".formatted(field.getName()), Integer.class);
    }

    private static Field<String> bitmap_union_to_string(Field<?> field) {
        return field("BITMAP_TO_STRING(BITMAP_UNION(%s))".formatted(field.getName()), String.class);
    }

    // 指标定义移到常量部分
    private static final List<Field<?>> NOTE_METRICS = List.of(
            bitmap_union_count(field("callNoteNum", Integer.class)).as("callNoteNum"),
            bitmap_union_count(field("personNoteNum", Integer.class)).as("personNoteNum"),
            bitmap_union_count(field("otherNoteNum", Integer.class)).as("otherNoteNum"),
            bitmap_union_count(field("emailNoteNum", Integer.class)).as("emailNoteNum"),
            bitmap_union_count(field("videoNoteNum", Integer.class)).as("videoNoteNum"),
            bitmap_union_count(field("iciNum", Integer.class)).as("iciNum"),
            bitmap_union_count(field("noteCount", Integer.class)).as("noteCount"),
            bitmap_union_to_string(field("unique_talent_ids")).as("uniqueTalentIds"),
            bitmap_union_count(field("application_note_count_num", Integer.class)).as("applicationNoteCountNum"),
            bitmap_union_count(field("talent_tracking_note_count_num", Integer.class)).as("talentTrackingNoteCountNum"),
            bitmap_union_to_string(field("talent_tracking_note_ids")).as("talentTrackingNoteIds")
    );

    private static final List<Field<?>> APPLICATION_FUNNEL_METRICS = createKpiFunnelMetrics();

    private static final List<Field<?>> APPLICATION_CURRENT_METRICS = createCurrentKpiMetrics();

    private static final List<Field<?>> RESERVE_INTERVIEW_CURRENT_METRICS = List.of(
            // 预约面试相关指标
            bitmap_union_count(field("reserve_current_interview_total", Integer.class)).as("reserveCurrentInterviewTotal"),
            bitmap_union_count(field("reserve_interview_currentAiRecommendNum", Integer.class)).as("reserveInterviewCurrentAiRecommendNum"),
            bitmap_union_count(field("reserve_interview_currentPrecisionAiRecommendNum", Integer.class)).as("reserveInterviewCurrentPrecisionAiRecommendNum")
    );

    private static final List<Field<?>> RESERVE_INTERVIEW_FUNNEL_METRICS = List.of(
            // 预约面试相关指标
            bitmap_union_count(field("reserve_interview_total", Integer.class)).as("reserveInterviewTotal"),
            bitmap_union_count(field("reserve_interview_aiRecommendCountNum", Integer.class)).as("reserveInterviewAiRecommendCountNum"),
            bitmap_union_count(field("reserve_interview_precisionAiRecommendNum", Integer.class)).as("reserveInterviewPrecisionAiRecommendNum")
    );


}
