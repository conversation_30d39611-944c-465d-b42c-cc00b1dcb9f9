package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiApplicationStatusType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.altomni.apn.report.domain.enumeration.ReportApplicationStatus;
import com.altomni.apn.report.domain.enumeration.ReportTableType;
import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.*;
import com.altomni.apn.report.util.MapToEntityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.jooq.*;
import org.jooq.conf.ParamType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;
import org.springframework.util.StopWatch;

import javax.persistence.Query;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.report.repository.v2.Constants.*;
import static org.jooq.impl.DSL.*;

@Slf4j
@Repository
public class RecruitingKpiUserExtendRepository extends RecruitingKpiBaseRepository {

    public List<RecruitingKpiJobDetailVO> searchJobDetailList(RecruitingKpiJobDetailSearchDto jobSearchDto, Pageable pageable, TeamDataPermissionRespDTO team) {
        StopWatch stopWatch = new StopWatch("searchJobDetailList");
        stopWatch.start();
        String sql = """
                SELECT
                	j.id job_id,
                	j.posting_time posting_date,
                	j.open_time open_date,
                	website.last_modified_date tenant_website_posting_date,
                	j.title job_title,
	                j.pteam_id,
                	j.code job_code,
                	j.status job_status,
                	j.openings,
                	rp.id recruitment_process_id,
                	rp.name job_type_name,
                	rp.job_type job_type,
                	c.`full_business_name` company_name,
                	c.id company_id,
                	jai.extended_info ->> '$.department' AS division,
                	GROUP_CONCAT( DISTINCT concat(tcslcc.id,"-",tcslcc.full_name) ) client_contact,
                	GROUP_CONCAT( DISTINCT concat(u.id, "-", u.first_name, " ", u.last_name)) assigned_user,
                	concat(create_user.first_name, ' ', create_user.last_name) created_by,
                	CAST(jai.extended_info ->> '$.salaryRange.gte' AS DECIMAL) AS minimum_pay_rate,
                    CAST(jai.extended_info ->> '$.salaryRange.lte' AS DECIMAL) AS maximum_pay_rate,
                    CAST(jai.extended_info ->> '$.billRange.gte' AS DECIMAL) AS minimum_bill_rate,
                    CAST(jai.extended_info ->> '$.billRange.lte' AS DECIMAL) AS maximum_bill_rate,
                	jai.extended_info ->> '$.payType' AS rate_per,
                	j.start_date,
                	j.end_date,
                	j.contract_duration,
                	IFNULL(jar.submit_to_client, 0) submit_to_client_num,
                    IFNULL(jar.first_interview, 0) first_interview_num,
                    IFNULL(jar.second_interview, 0) second_interview_num,
                    IFNULL(jar.final_interview, 0) final_interview_num,
                    IFNULL(jar.interview, 0) interview_num,
                    IFNULL(jar.offer, 0) offer_num,
                    IFNULL(jar.onboard, 0) onboard_num,
                	GROUP_CONCAT(distinct jl.format_location SEPARATOR ";") job_location,
                	j.flexible_location,
                	jai.extended_info ->> '$.requiredSkills[*].skillName' AS skills,
                	ec.id currency,
                	ec.name ec_name,
                	ec.symbol job_currency,
                	if(j.sales_lead_id is null, null, if(business_progress = 60, "Master Contract", "Trial Case")) job_cooperation_status,
                	count( DISTINCT jn.id ) contract_notes
                FROM
                	job j
                	INNER JOIN job_user_relation ul ON ul.job_id = j.id
                	LEFT JOIN user create_user ON create_user.id = j.puser_id
                	Inner join permission_user_team put on put.user_id = ul.user_id and put.is_primary = 1
                    INNER JOIN permission_team pt on pt.id = put.team_id
                	INNER JOIN company c ON c.id = j.company_id
                	INNER JOIN recruitment_process rp ON rp.id = j.recruitment_process_id
                	{joinTable}
                	LEFT JOIN job_company_contact_relation jccr ON jccr.job_id = j.id
                	LEFT JOIN company_sales_lead_client_contact cslcc ON cslcc.id = jccr.client_contact_id
                	LEFT JOIN talent tcslcc ON tcslcc.id = cslcc.talent_id
                	LEFT JOIN user_job_relation ujr ON ujr.job_id = j.id AND ujr.status = 1 
                	LEFT JOIN user u on u.id = ujr.user_id
                	LEFT JOIN job_additional_info jai ON jai.id = j.additional_info_id
                	LEFT JOIN job_location jl ON jl.job_id = j.id
                	LEFT JOIN job_note jn ON jn.job_id = j.id and jn.visible = 1
                	left join job_application_relation jar on jar.job_id = j.id
                	left join enum_currency ec on ec.id = j.currency
                	left join account_business ab on ab.id = j.sales_lead_id
                	left join job_ipg_relation website on website.apn_job_id = j.id and website.ipg_job_status not in (4, 200) 
                	where 1=1 
                	{whereCondition}
                	group by j.id
                	{orderBy}
                """;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        Map<String, String> map = new HashMap<>(16);
        map.put("whereCondition", getJobDetailWhereConditionSqlAndSetParam(jobSearchDto, whereParamMap, team));
        map.put("joinTable", getJoinTablesForJobDetailList(jobSearchDto));
        map.put("orderBy", jobSearchDto.getOrderBySql());
        sql = StrUtil.format(sql, map);
        Query query = entityManager.createNativeQuery(sql, RecruitingKpiJobDetailVO.class);
        setListWithPage(pageable, whereParamMap, query);
        List<RecruitingKpiJobDetailVO> voList = query.getResultList();
        stopWatch.stop();
        log.info(" searchJobDetailList time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return voList;
    }

    public Long searchJobDetailCount(RecruitingKpiJobDetailSearchDto jobSearchDto, TeamDataPermissionRespDTO team) {
        StopWatch stopWatch = new StopWatch("searchJobDetailCount");
        stopWatch.start();
        String sql = """
                SELECT
                	count(distinct j.id)
                FROM
                	job j
                	INNER JOIN job_user_relation ul ON ul.job_id = j.id
                	Inner join permission_user_team put on put.user_id = ul.user_id and put.is_primary = 1
                    INNER JOIN permission_team pt on pt.id = put.team_id
                	INNER JOIN company c ON c.id = j.company_id
                	INNER JOIN recruitment_process rp ON rp.id = j.recruitment_process_id
                	{joinTable}
                	LEFT JOIN job_company_contact_relation jccr ON jccr.job_id = j.id
                	LEFT JOIN company_sales_lead_client_contact cslcc ON cslcc.id = jccr.client_contact_id
                	LEFT JOIN talent tcslcc ON tcslcc.id = cslcc.talent_id
                	LEFT JOIN user_job_relation ujr ON ujr.job_id = j.id AND ujr.status = 1
                	LEFT JOIN job_additional_info jai ON jai.id = j.additional_info_id
                	LEFT JOIN job_location jl ON jl.job_id = j.id
                	where 1=1
                	{whereCondition}
                """;
        Map<String, String> map = new HashMap<>(16);
        Map<String, Object> whereParamMap = new HashMap<>(16);
        map.put("whereCondition", getJobDetailWhereConditionSqlAndSetParam(jobSearchDto, whereParamMap, team));
        map.put("joinTable", getJoinTablesForJobDetailList(jobSearchDto));
        sql = StrUtil.format(sql, map);
        Query countQ = entityManager.createNativeQuery(sql);
        Optional.of(whereParamMap).ifPresent(m -> m.forEach(countQ::setParameter));
        long count = Long.parseLong(String.valueOf(countQ.getSingleResult()));
        stopWatch.stop();
        log.info(" searchJobDetailCount time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return count;
    }

    public List<RecruitingKpiTalentDetailVO> searchTalentDetailList(RecruitingKpiTalentDetailSearchDto searchDto, Pageable pageable, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchTalentDetailList");
        stopWatch.start();
        String sql;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        Map<String, String> map = new HashMap<>(16);
        sql = """
            SELECT
                t.id AS talent_id,
                t.full_name AS full_name,
                tai.current_company,
                tai.current_position,
                t.motivation_id job_search_status,
                t.puser_id created_by,
                t.created_date,
                t.last_update_user_id last_modified_by,
                t.last_modified_date,
                GROUP_CONCAT(DISTINCT CASE WHEN tn.note_type = 5 THEN 'ICI' END) AS note_type,
                count( DISTINCT tn.id ) talent_notes,
                IF(cslcc.id IS NOT NULL AND cslcc.STATUS = 1, TRUE, FALSE ) is_contact
            FROM
                talent t
                FORCE INDEX (idx_talent_tenant_id_created_date)
                INNER JOIN talent_user_relation ul ON ul.talent_id = t.id
                INNER JOIN permission_user_team put ON put.user_id = ul.user_id and put.is_primary = 1
                INNER JOIN permission_team pt on pt.id = put.team_id
                {joinTables}
                LEFT JOIN talent_note tn ON tn.talent_id = t.id
                LEFT JOIN talent_additional_info tai ON tai.id = t.additional_info_id
                left join company_sales_lead_client_contact cslcc on cslcc.talent_id = t.id
            WHERE
            t.tenant_id = :tenantId {dateSearchSql}
            {whereCondition}
            group by t.id
            {orderBy}
            """;
        map.put("dateSearchSql", " and t.created_date BETWEEN :startDate AND :endDate ");
        whereParamMap.put("tenantId", searchDto.getSearchTenantId());
        whereParamMap.put("startDate", searchDto.getStartDateUtc());
        whereParamMap.put("endDate", searchDto.getEndDateUtc());
        map.put("orderBy", searchDto.getOrderBySql());
        map.put("whereCondition", getTalentDetailWhereConditionSqlAndSetParam(searchDto, whereParamMap, teamDTO));
        map.put("joinTables", getJoinTablesForTalentDetail(searchDto));
        sql = StrUtil.format(sql, map);
        Query query = entityManager.createNativeQuery(sql, RecruitingKpiTalentDetailVO.class);
        setListWithPage(pageable, whereParamMap, query);
        List<RecruitingKpiTalentDetailVO> voList = query.getResultList();
        stopWatch.stop();
        log.info(" searchTalentDetailList time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return voList;
    }


    public Long searchTalentDetailCount(RecruitingKpiTalentDetailSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchTalentDetailCount");
        stopWatch.start();
        String sql;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        Map<String, String> map = new HashMap<>(16);
        sql = """
            SELECT
                count(distinct t.id)
            FROM
                talent t
                FORCE INDEX (idx_talent_tenant_id_created_date)
                INNER JOIN talent_user_relation ul ON ul.talent_id = t.id
                INNER JOIN permission_user_team put ON put.user_id = ul.user_id and put.is_primary = 1
                INNER JOIN permission_team pt on pt.id = put.team_id
                {joinTables}
                LEFT JOIN talent_additional_info tai ON tai.id = t.additional_info_id
            WHERE
            t.tenant_id = :tenantId {dateSearchSql}
            {whereCondition}
            """;
        map.put("dateSearchSql", " and t.created_date BETWEEN :startDate AND :endDate ");
        whereParamMap.put("tenantId", searchDto.getSearchTenantId());
        whereParamMap.put("startDate", searchDto.getStartDateUtc());
        whereParamMap.put("endDate", searchDto.getEndDateUtc());
        map.put("whereCondition", getTalentDetailWhereConditionSqlAndSetParam(searchDto, whereParamMap, teamDTO));
        map.put("joinTables", getJoinTablesForTalentDetail(searchDto));
        sql = StrUtil.format(sql, map);
        Query countQ = entityManager.createNativeQuery(sql);
        Optional.of(whereParamMap).ifPresent(m -> m.forEach(countQ::setParameter));
        long count = Long.parseLong(String.valueOf(countQ.getSingleResult()));
        stopWatch.stop();
        log.info("searchTalentDetailCount time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return count;
    }

    public List<? extends RecruitingKpiApplicationBaseDetailVO> searchApplicationDetailList(RecruitingKpiApplicationDetailSearchDto searchDto, Pageable pageable, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchApplicationDetailList");
        stopWatch.start();
        String sql = """
                {withSql}
                SELECT
                	node.id node_id,
                	t.id talent_id,
                	trp.id talent_recruitment_process_id,
                	t.full_name full_name,
                	count( DISTINCT tn.id ) talent_notes,
                	{selectFields}
                	j.title job_title,
                	j.id job_id,
                	j.code job_code,
                	c.full_business_name company_name,
                	c.id company_id,
                    rp.job_type job_type,
                	j.status job_status,
                	j.flexible_location,
                    j.pteam_id,
                    CASE WHEN trpn.node_status = 1 THEN trpn.node_type WHEN trpn.node_status = 4 THEN -1 END workflow_status,
                	GROUP_CONCAT( distinct jl.format_location SEPARATOR ";") job_location,
                	group_concat( DISTINCT am.user_id ) am,
                	group_concat( DISTINCT re.user_id ) recruiter,
                	group_concat( DISTINCT so.user_id ) sourcer,
                	group_concat( DISTINCT ac.user_id ) ac,
                	group_concat( DISTINCT dm.user_id ) dm,
                	group_concat( DISTINCT owner.user_id ) owner,
                	group_concat( DISTINCT concat(coam.user_id, '-', coam.country)) co_am,
                	group_concat( DISTINCT salesleadowner.user_id ) sales_lead_owner,
                	group_concat( DISTINCT bdowner.user_id ) bd_owner,
                	node.last_update_user_id last_modified_by,
                	node.last_modified_date,
                	if(resign.id is null, false, true) as resigned,
                	if(star.id is null, false, true) as converted_to_fte
                FROM
                	{fromTables} node
                	INNER JOIN talent_recruitment_process trp ON trp.id = node.talent_recruitment_process_id
                	LEFT JOIN talent_recruitment_process_resignation resign ON trp.id = resign.talent_recruitment_process_id
                	left join start star on resign.talent_recruitment_process_id = star.talent_recruitment_process_id and star.start_type=5
                	{processUserRelation}
                	INNER JOIN talent t ON t.id = trp.talent_id
                	INNER JOIN job j ON j.id = trp.job_id
                    INNER JOIN recruitment_process rp on rp.id = j.recruitment_process_id                                                                     
                	INNER JOIN company c ON j.company_id = c.id
                	INNER JOIN permission_user_team put ON put.user_id = ul.user_id and put.is_primary = 1
                	INNER JOIN permission_team pt on pt.id = put.team_id AND pt.team_category_id IN (15,20)
                	{leftJoinTables}
                    {withLeftSql}     
                	LEFT JOIN talent_recruitment_process_kpi_user am ON am.talent_recruitment_process_id = trp.id AND am.user_role = 0
                	LEFT JOIN talent_recruitment_process_kpi_user re ON re.talent_recruitment_process_id = trp.id AND re.user_role = 1
                	LEFT JOIN talent_recruitment_process_kpi_user so ON so.talent_recruitment_process_id = trp.id AND so.user_role = 2
                	LEFT JOIN talent_recruitment_process_kpi_user ac ON ac.talent_recruitment_process_id = trp.id AND ac.user_role = 5
                	LEFT JOIN talent_recruitment_process_kpi_user dm ON dm.talent_recruitment_process_id = trp.id AND dm.user_role = 3
                	LEFT JOIN talent_recruitment_process_kpi_user owner ON owner.talent_recruitment_process_id = trp.id AND owner.user_role = 4
                	LEFT JOIN talent_recruitment_process_kpi_user coam ON coam.talent_recruitment_process_id = trp.id AND coam.user_role = 7
                    LEFT JOIN talent_recruitment_process_kpi_user salesleadowner ON salesleadowner.talent_recruitment_process_id = trp.id AND salesleadowner.user_role = 9
                    LEFT JOIN talent_recruitment_process_kpi_user bdowner ON bdowner.talent_recruitment_process_id = trp.id AND bdowner.user_role = 8
                	LEFT JOIN talent_note tn ON tn.talent_id = t.id
                	LEFT JOIN job_location jl ON jl.job_id = j.id
                WHERE
                	{whereCondition}
                GROUP BY
                	node.id
                	{orderBy}
                """;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        sql = setApplicationSql(sql, searchDto, whereParamMap, teamDTO);
        Class<? extends RecruitingKpiApplicationBaseDetailVO> clazz = searchDto.getApplicationDetailClass();
        Query query = entityManager.createNativeQuery(sql);
        query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        setListWithPage(pageable, whereParamMap, query);
        List<Map<String, Object>> mapList = query.getResultList();
        List<? extends RecruitingKpiApplicationBaseDetailVO> voList = MapToEntityUtil.convertEntity(mapList, clazz);
        stopWatch.stop();
        voList.forEach(x -> {
            if (StringUtils.isNotBlank(x.getCoAm())) {
                List<UserCountryVO> userCountryList = new ArrayList<>();
                Arrays.stream(x.getCoAm().split(",")).forEach(v -> {
                    String[] amCountry = v.split("-");
                    UserCountryVO vo = new UserCountryVO();
                    vo.setUserId(Long.valueOf(amCountry[0]));
                    vo.setCountryId(amCountry[1]);
                    userCountryList.add(vo);
                });
                x.setCoAmList(userCountryList);
            }
        });
        log.info(" searchApplicationDetailList time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return voList.stream().map(d -> d.setResigned(BooleanUtils.isTrue(d.getConvertedToFte()) ? Boolean.FALSE : d.getResigned())).toList();
    }

    public Long searchApplicationDetailCount(RecruitingKpiApplicationDetailSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchApplicationDetailCount");
        stopWatch.start();
        String sql = """
                {withSql}
                SELECT
                	count(distinct node.id)
                FROM
                	{fromTables} node
                	INNER JOIN talent_recruitment_process trp ON trp.id = node.talent_recruitment_process_id
                	LEFT JOIN talent_recruitment_process_resignation resign ON trp.id = resign.talent_recruitment_process_id
                	{processUserRelation}
                	INNER JOIN talent t ON t.id = trp.talent_id
                	INNER JOIN job j ON j.id = trp.job_id
	                INNER JOIN recruitment_process rp on rp.id = j.recruitment_process_id      
                	INNER JOIN company c ON j.company_id = c.id
                	INNER JOIN permission_user_team put ON put.user_id = ul.user_id and put.is_primary = 1
                    INNER JOIN permission_team pt ON pt.id = put.team_id AND pt.team_category_id IN (15,20)
                	{leftJoinTables}
	                {withLeftSql}
                	LEFT JOIN talent_recruitment_process_kpi_user am ON am.talent_recruitment_process_id = trp.id AND am.user_role = 0
                	LEFT JOIN talent_recruitment_process_kpi_user re ON re.talent_recruitment_process_id = trp.id AND re.user_role = 1
                	LEFT JOIN talent_recruitment_process_kpi_user so ON so.talent_recruitment_process_id = trp.id AND so.user_role = 2
                	LEFT JOIN talent_recruitment_process_kpi_user ac ON ac.talent_recruitment_process_id = trp.id AND ac.user_role = 5
                	LEFT JOIN talent_recruitment_process_kpi_user dm ON dm.talent_recruitment_process_id = trp.id AND dm.user_role = 3
                	LEFT JOIN talent_recruitment_process_kpi_user owner ON owner.talent_recruitment_process_id = trp.id AND owner.user_role = 4
                	LEFT JOIN talent_recruitment_process_kpi_user coam ON coam.talent_recruitment_process_id = trp.id AND coam.user_role = 7
                    LEFT JOIN talent_recruitment_process_kpi_user salesleadowner ON salesleadowner.talent_recruitment_process_id = trp.id AND salesleadowner.user_role = 9
                    LEFT JOIN talent_recruitment_process_kpi_user bdowner ON bdowner.talent_recruitment_process_id = trp.id AND bdowner.user_role = 8
                	LEFT JOIN job_location jl ON jl.job_id = j.id
                WHERE
                	{whereCondition}
                """;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        sql = setApplicationSql(sql, searchDto, whereParamMap, teamDTO);
        Query query = entityManager.createNativeQuery(sql);
        setListWithPage(null, whereParamMap, query);
        long count = Long.parseLong(String.valueOf(query.getSingleResult()));
        stopWatch.stop();
        log.info(" searchApplicationDetailCount time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return count;
    }

    public List<RecruitingKpiTalentNoteDetailVO> searchTalentNoteDetailList(RecruitingKpiTalentNoteDetailSearchDto searchDto, Pageable pageable, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchTalentNoteDetailList");
        stopWatch.start();
        String sql = """
                 SELECT
                    tn.id,
                    t.full_name,
                    t.id talent_id,
                    tn.title,
                    tn.priority,
                    tn.note_type,
                    tn.note,
                    tn.additional_info,
                    t.motivation_id job_search_status,
                    tn.puser_id created_by,
                    tn.last_update_user_id last_modified_by,
                    tn.created_date,
                    tn.last_modified_date
                  FROM
                    talent_note tn
                    INNER JOIN talent t ON t.id = tn.talent_id
                    inner join permission_user_team put on put.user_id = tn.puser_id and put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id
                    {joinTables}
                 WHERE
                    {whereCondition}
                    group by tn.id
                    {orderBy}
                """;
        Map<String, String> map = new HashMap<>(16);
        Map<String, Object> whereParamMap = new HashMap<>(16);
        map.put("joinTables", getJoinTablesForTalentNoteDetail(searchDto));
        map.put("whereCondition", getWhereConditionForTalentNoteDetail(searchDto, whereParamMap, teamDTO));
        map.put("orderBy", searchDto.getOrderBySql());
        sql = StrUtil.format(sql, map);
        Query query = entityManager.createNativeQuery(sql, RecruitingKpiTalentNoteDetailVO.class);
        setListWithPage(pageable, whereParamMap, query);
        List<RecruitingKpiTalentNoteDetailVO> voList = query.getResultList();
        stopWatch.stop();
        log.info(" searchTalentNoteDetailList time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return voList;
    }

    public Long searchTalentNoteDetailCount(RecruitingKpiTalentNoteDetailSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchTalentNoteDetailList");
        stopWatch.start();
        String sql = """
                 SELECT
                    count(distinct tn.id)
                  FROM
                    talent_note tn
                    INNER JOIN talent t ON t.id = tn.talent_id
                    inner join permission_user_team put on put.user_id = tn.puser_id and put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id
                    {joinTables}
                 WHERE
                    {whereCondition}
                """;
        Map<String, String> map = new HashMap<>(16);
        Map<String, Object> whereParamMap = new HashMap<>(16);
        map.put("whereCondition", getWhereConditionForTalentNoteDetail(searchDto, whereParamMap, teamDTO));
        map.put("joinTables", getJoinTablesForTalentNoteDetail(searchDto));
        sql = StrUtil.format(sql, map);
        Query query = entityManager.createNativeQuery(sql);
        setListWithPage(null, whereParamMap, query);
        long count = Long.parseLong(String.valueOf(query.getSingleResult()));
        stopWatch.stop();
        log.info(" searchTalentNoteDetailList time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return count;
    }

    public List<RecruitingKpiApplicationNoteDetailVO> searchApplicationNoteDetailList(RecruitingKpiApplicationNoteDetailSearchDto searchDto, Pageable pageable, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchApplicationNoteDetailList");
        stopWatch.start();
        String sql = """
                SELECT
                    node.id virtual_id,
                    node.talent_recruitment_process_id talent_recruitment_process_id,
                	node.node_id id,
                	t.id talent_id,
                	t.full_name full_name,
                	j.title job_title,
                	j.id job_id,
                	j.status job_status,
                    j.pteam_id,
                	CASE WHEN trpn.node_status = 1 THEN trpn.node_type WHEN trpn.node_status = 4 THEN -1 END workflow_status,
                	node.note note,
                	node.node_type,
                	node.user_id created_by,
                	node.created_date created_date,
                	node.last_modified_by_user_id last_modified_by,
                    node.last_modified_date
                FROM
                	talent_recruitment_process_note node
                	INNER JOIN talent_recruitment_process trp ON trp.id = node.talent_recruitment_process_id
                	inner join talent_recruitment_process_kpi_user ul on ul.talent_recruitment_process_id = trp.id
                	inner join job j on j.id = trp.job_id
                	inner join talent t on t.id = trp.talent_id
                	inner join company c on c.id = j.company_id
                	INNER JOIN permission_user_team put ON put.user_id = node.user_id and put.is_primary = 1
                	INNER JOIN permission_team pt on pt.id = put.team_id AND pt.team_category_id IN (15,20)
                	inner JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id AND trpn.node_status IN ( 1, 4 )
                	{joinTables}
                	where
                	{whereCondition}
                	group by node.id
                	{orderBy}
                """;

        Map<String, String> map = new HashMap<>(16);
        Map<String, Object> whereParamMap = new HashMap<>(16);
        map.put("whereCondition", getWhereConditionForApplicationNote(searchDto, whereParamMap, teamDTO));
        map.put("joinTables", getJoinTablesForApplicationNote(searchDto));
        map.put("orderBy", getOrderByForApplicationNote(searchDto.getSort()));
        sql = StrUtil.format(sql, map);
        Query query = entityManager.createNativeQuery(sql, RecruitingKpiApplicationNoteDetailVO.class);
        setListWithPage(pageable, whereParamMap, query);
        List<RecruitingKpiApplicationNoteDetailVO> voList = query.getResultList();
        stopWatch.stop();
        log.info(" searchApplicationNoteDetailList time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return voList;
    }

    public Long searchApplicationNoteDetailCount(RecruitingKpiApplicationNoteDetailSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchApplicationNoteDetailCount");
        stopWatch.start();
        String sql = """
                SELECT
                    count(distinct node.id)
                FROM
                	talent_recruitment_process_note node
                	INNER JOIN talent_recruitment_process trp ON trp.id = node.talent_recruitment_process_id
                	inner join talent_recruitment_process_kpi_user ul on ul.talent_recruitment_process_id = trp.id
                	inner join job j on j.id = trp.job_id
                	inner join talent t on t.id = trp.talent_id
                	inner join company c on c.id = j.company_id
                	INNER JOIN permission_user_team put ON put.user_id = node.user_id and put.is_primary = 1
                	inner JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id AND trpn.node_status IN ( 1, 4 )
                	{joinTables}
                	where 
                	{whereCondition}
                """;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        Map<String, String> map = new HashMap<>(16);
        map.put("joinTables", getJoinTablesForApplicationNote(searchDto));
        map.put("whereCondition", getWhereConditionForApplicationNote(searchDto, whereParamMap, teamDTO));
        sql = StrUtil.format(sql, map);
        Query query = entityManager.createNativeQuery(sql);
        setListWithPage(null, whereParamMap, query);
        long count = Long.parseLong(String.valueOf(query.getSingleResult()));
        stopWatch.stop();
        log.info(" searchApplicationNoteDetailCount time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return count;
    }

    public List<RecruitingKpiApnProNoteDetailVO> searchApnProNoteDetailList(RecruitingKpiApnProNoteDetailSearchDto searchDto, Pageable pageable, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchApnProNoteDetailList");
        stopWatch.start();
        String sql = """
                SELECT
                	ttn.id,
                	t.id talent_id,
                	t.full_name full_name,
                	ttn.tracking_platform source_channel,
                	ttn.note note,
                	t.motivation_id job_search_status,
                	ttn.user_id created_by,
                	ttn.last_update_user_id last_modified_by,
                	ttn.created_date,
                	ttn.last_modified_date
                FROM
                	talent_tracking_note ttn
                	INNER JOIN talent t ON t.id = ttn.synced_talent_id
                    inner join user u on u.id = ttn.user_id
                    inner join permission_user_team put on put.user_id = ttn.user_id  and put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id AND pt.team_category_id IN (15,20)
                    {joinTables}
                where 
                    {whereCondition}
                    group by ttn.id
                    {orderBy}
                """;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        Map<String, String> map = new HashMap<>(16);
        map.put("joinTables", getJoinTablesForApnProNote(searchDto));
        map.put("whereCondition", getWhereConditionForApnProNote(searchDto, whereParamMap, teamDTO));
        map.put("orderBy", searchDto.getOrderBySql());
        sql = StrUtil.format(sql, map);
        Query query = entityManager.createNativeQuery(sql, RecruitingKpiApnProNoteDetailVO.class);
        setListWithPage(pageable, whereParamMap, query);
        List<RecruitingKpiApnProNoteDetailVO> voList = query.getResultList();
        stopWatch.stop();
        log.info(" searchApnProNoteDetailList time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return voList;
    }

    public Long searchApnProNoteDetailCount(RecruitingKpiApnProNoteDetailSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchApnProNoteDetailCount");
        stopWatch.start();
        String sql = """
                SELECT
                	count(distinct ttn.id)
                FROM
                	talent_tracking_note ttn
                	INNER JOIN talent t ON t.id = ttn.synced_talent_id
                    inner join user u on u.id = ttn.user_id
                    inner join permission_user_team put on put.user_id = ttn.user_id  and put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id
                    {joinTables}
                where 
                    {whereCondition}
                """;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        Map<String, String> map = new HashMap<>(16);
        map.put("joinTables", getJoinTablesForApnProNote(searchDto));
        map.put("whereCondition", getWhereConditionForApnProNote(searchDto, whereParamMap, teamDTO));
        sql = StrUtil.format(sql, map);
        Query query = entityManager.createNativeQuery(sql);
        setListWithPage(null, whereParamMap, query);
        long count = Long.parseLong(String.valueOf(query.getSingleResult()));
        stopWatch.stop();
        log.info(" searchApnProNoteDetailCount time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return count;
    }

    private String getJoinTablesForApnProNote(RecruitingKpiApnProNoteDetailSearchDto searchDto) {
        //        if (ObjectUtil.isNotEmpty(searchDto.getJob())
//                || ObjectUtil.isNotEmpty(searchDto.getCompany())
//                || ObjectUtil.isNotEmpty(searchDto.getUser())) {
//            sb.append("""
//                        INNER JOIN talent_recruitment_process trp ON trp.talent_id = t.id
//                        inner join talent_recruitment_process_kpi_user ul on ul.talent_recruitment_process_id = trp.id
//                        inner join job j on j.id = trp.job_id
//                        inner join company c on c.id = j.company_id
//                        """);
//            getJoinTablesByJobOrCompany(searchDto, sb);
//        }
        return "";
    }

    private String getJoinTablesForJobDetailList(RecruitingKpiJobDetailSearchDto searchDto) {
        StringBuilder sb = new StringBuilder();
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) || ObjectUtil.isNotEmpty(searchDto.getCompany()) || ObjectUtil.isNotEmpty(searchDto.getUser())) {
            sb.append("""
                    left join talent_recruitment_process trp ON trp.job_id = j.id
                    left join talent t on t.id = trp.talent_id
                    """);
            if ((ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getIndustries()))
                    || (ObjectUtil.isNotEmpty(searchDto.getCompany()) && CollUtil.isNotEmpty(searchDto.getCompany().getIndustries()))) {
                sb.append(" left join company_industry_relation cir on cir.company_id = c.id ");
            }
            if (ObjectUtil.isNotEmpty(searchDto.getJob()) &&  CollUtil.isNotEmpty(searchDto.getJob().getJobFunctions())) {
                sb.append(" left join job_job_function_relation jjfr on jjfr.job_id = j.id ");
            }
        }
        return sb.toString();
    }

    private String getJoinTablesForTalentDetail(RecruitingKpiTalentDetailSearchDto searchDto) {
        StringBuilder sb = new StringBuilder();
        if (ObjectUtil.isNotEmpty(searchDto.getCompany()) || ObjectUtil.isNotEmpty(searchDto.getJob())
                || ObjectUtil.isNotEmpty(searchDto.getCompanyId()) || ObjectUtil.isNotEmpty(searchDto.getJobId())
                || BooleanUtil.isTrue(searchDto.getIsCompanyFlag())) {
            sb.append("""
                     inner join talent_recruitment_process trp on trp.talent_id = t.id
                     inner join job j on j.id = trp.job_id
                     inner join company c on c.id = j.company_id
                    """);
            getJoinTablesByJobOrCompany(searchDto, sb);
        }
        return sb.toString();
    }

    private String getJoinTablesForApplicationNote(RecruitingKpiApplicationNoteDetailSearchDto searchDto) {
        StringBuilder sb = new StringBuilder();
        getJoinTablesByJobOrCompany(searchDto, sb);
        return sb.toString();
    }

    private String getWhereConditionForTalentNoteDetail(RecruitingKpiTalentNoteDetailSearchDto searchDto, Map<String, Object> whereParamMap, TeamDataPermissionRespDTO teamDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append(" t.tenant_id = :tenantId ");
        whereParamMap.put("tenantId", SecurityUtils.getTenantId());
        if (ObjectUtil.isNotEmpty(searchDto.getTalentNoteType())) {
            sb.append(" and tn.note_type = :talentNoteType ");
            whereParamMap.put("talentNoteType", searchDto.getTalentNoteType().toDbValue());
        }
        sb.append(" and ").append(" tn.created_date ").append(" BETWEEN :startDate AND :endDate ");
        whereParamMap.put("startDate", searchDto.getStartDateUtc());
        whereParamMap.put("endDate", searchDto.getEndDateUtc());
        if (CollUtil.isNotEmpty(searchDto.getUserIdList()) && CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
            sb.append(" and ( tn.puser_id in :userIdList or put.team_id in :teamIdList ) ");
            whereParamMap.put("userIdList", searchDto.getUserIdList());
            whereParamMap.put("teamIdList", searchDto.getTeamIdList());
        } else {
            if (CollUtil.isNotEmpty(searchDto.getUserIdList())) {
                sb.append(" and tn.puser_id in :userIdList ");
                whereParamMap.put("userIdList", searchDto.getUserIdList());
            }
            if (CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
                sb.append(" and put.team_id in :teamIdList ");
                whereParamMap.put("teamIdList", searchDto.getTeamIdList());
            }
        }
        appendDetailCommonCondition(searchDto.getCompanyId(), searchDto.getJobId(), searchDto.getUserId(), searchDto.getTeamId(), sb, whereParamMap);
        //talent search
//        appendTalentSearch(sb, searchDto, whereParamMap);
        //job search
//        appendJobSearch(sb, searchDto, whereParamMap);
        //company search
//        appendCompanySearch(sb, searchDto, whereParamMap);
        //data permission
        searchDto.appendPermissionData(sb, whereParamMap, teamDTO);
        //detail condition
        searchDto.appendDetailCondition(sb, whereParamMap);
        //search permission team
        appendPermissionTeamSearch(sb);
        return sb.toString();
    }

    private String getJoinTablesForTalentNoteDetail(RecruitingKpiTalentNoteDetailSearchDto searchDto) {
        return getJoinTableByUser(searchDto, "tn");
    }


    private String getWhereConditionForApnProNote(RecruitingKpiApnProNoteDetailSearchDto searchDto, Map<String, Object> whereParamMap, TeamDataPermissionRespDTO teamDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append(" ttn.tenant_id = :tenantId ");
        whereParamMap.put("tenantId", searchDto.getSearchTenantId());
        whereParamMap.put("startDate", searchDto.getStartDateUtc());
        whereParamMap.put("endDate", searchDto.getEndDateUtc());
        sb.append(" and ttn.created_date BETWEEN :startDate AND :endDate ");
        appendDetailCommonCondition(searchDto.getCompanyId(), searchDto.getJobId(), searchDto.getUserId(), searchDto.getTeamId(), sb, whereParamMap);
        if (CollUtil.isNotEmpty(searchDto.getUserIdList())) {
            sb.append(" and put.user_id in :userIdList ");
            whereParamMap.put("userIdList", searchDto.getUserIdList());
        }
        if (CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
            sb.append("and put.team_id in :teamIdList  ");
            whereParamMap.put("teamIdList", searchDto.getTeamIdList());
        }
        //talent search
//        appendTalentSearch(sb, searchDto, whereParamMap);
        //job search
//        appendJobSearch(sb, searchDto, whereParamMap);
        //company search
//        appendCompanySearch(sb, searchDto, whereParamMap);
        //data permission
        searchDto.appendPermission(sb, whereParamMap, teamDTO);
        //detail
        searchDto.appendDetailCondition(sb, whereParamMap);
        return sb.toString();
    }

    private String getOrderByForApplicationNote(SearchSortDTO sort) {
        if (ObjectUtil.isEmpty(sort)) {
            return " ";
        }
        return switch (sort.getProperty()) {
            case "fullName" -> " order by CONVERT( t.full_name USING gbk) " + sort.getDirection();
            case "jobTitle" -> " order by CONVERT( j.title USING gbk) " + sort.getDirection();
            case "jobStatus" -> " order by j.status " + sort.getDirection();
            case "workflowStatus" -> " order by workflow_status " + sort.getDirection();
            case "createdDate" -> " order by node.created_date " + sort.getDirection();
            case "lastModifiedDate" -> " order by node.last_modified_date " + sort.getDirection();
            default -> "";
        };
    }

    private String getWhereConditionForApplicationNote(RecruitingKpiApplicationNoteDetailSearchDto searchDto, Map<String, Object> whereParamMap, TeamDataPermissionRespDTO teamDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append(" node.tenant_id = :tenantId ");
        whereParamMap.put("tenantId", searchDto.getSearchTenantId());
        whereParamMap.put("startDate", searchDto.getStartDateUtc());
        whereParamMap.put("endDate", searchDto.getEndDateUtc());
        sb.append(" and ").append(" node.created_date ").append(" BETWEEN :startDate AND :endDate ");
        appendDetailCommonCondition(searchDto.getCompanyId(), searchDto.getJobId(), searchDto.getUserId(), searchDto.getTeamId(), sb, whereParamMap);
        appendCommonUserIdAndTeamId(searchDto, sb, whereParamMap);
        //talent search
        appendTalentSearchForApplication(sb, searchDto, whereParamMap);
        //job search
//        appendJobSearch(sb, searchDto, whereParamMap);
//        //company search
//        appendCompanySearch(sb, searchDto, whereParamMap);
        //data permission
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            sb.append(" and put.user_id = :userId ");
            whereParamMap.put("userId", searchDto.getSearchUserId());
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            sb.append("and put.team_id in :teamIds ");
            whereParamMap.put("teamIds", teamDTO.getNestedTeamIds());
        }
//        appendJobDataPermissionForJobOrDetail(sb, searchDto, whereParamMap, teamDTO);
        if (ObjectUtil.isNotEmpty(searchDto.getDetail())) {
            ApplicationNoteDetailSearchDto detail = searchDto.getDetail();
            if (StrUtil.isNotBlank(detail.getJobTitle())) {
                sb.append(" and j.title like :jobTitle ");
                whereParamMap.put("jobTitle", "%" + detail.getJobTitle() + "%");
            }
            if (ObjectUtil.isNotEmpty(detail.getJobId())) {
                sb.append(" and j.id = :jobId ");
                whereParamMap.put("jobId", detail.getJobId());
            }
            if (ObjectUtil.isNotEmpty(detail.getFullName())) {
                sb.append(" and t.full_name like :fullName ");
                whereParamMap.put("fullName", "%" + detail.getFullName() + "%");
            }
            if (ObjectUtil.isNotEmpty(detail.getTalentId())) {
                sb.append(" and t.id = :talentId ");
                whereParamMap.put("talentId", detail.getTalentId());
            }
            if (ObjectUtil.isNotEmpty(detail.getJobStatus())) {
                sb.append(" and j.status = :jobStatus ");
                whereParamMap.put("jobStatus", detail.getJobStatus().toDbValue());
            }
            if (ObjectUtil.isNotEmpty(detail.getCreatedBy())) {
                sb.append(" and node.user_id = :createdBy ");
                whereParamMap.put("createdBy", detail.getCreatedBy());
            }
            if (ObjectUtil.isNotEmpty(detail.getLastModifiedBy())) {
                sb.append(" and node.last_modified_by_user_id = :lastModifiedBy ");
                whereParamMap.put("lastModifiedBy", detail.getLastModifiedBy());
            }
            appendWorkflowStatus(sb, detail.getWorkflowStatus());
        }
        return sb.toString();
    }

    private void appendWorkflowStatus(StringBuilder sb, ReportTableType workflowStatus) {
        if (ObjectUtil.isEmpty(workflowStatus)) {
            return;
        }
        switch (workflowStatus) {
            case ELIMINATED -> sb.append(" and trpn.node_status = 4 ");
            case SUBMIT_TO_JOB -> sb.append(" and trpn.node_status = 1 and trpn.node_type = 10 ");
            case SUBMIT_TO_CLIENT -> sb.append(" and trpn.node_status = 1 and trpn.node_type = 20 ");
            case INTERVIEW -> sb.append(" and trpn.node_status = 1 and trpn.node_type = 30 ");
            case OFFER -> sb.append(" and trpn.node_status = 1 and trpn.node_type = 40 ");
            case OFFER_ACCEPT -> sb.append(" and trpn.node_status = 1 and trpn.node_type = 41 ");
            case ON_BOARD -> sb.append(" and trpn.node_status = 1 and trpn.node_type = 60 ");
            default -> sb.append(" ");
        }
    }

    private String setApplicationSql(String sql, RecruitingKpiApplicationDetailSearchDto searchDto, Map<String, Object> whereParamMap, TeamDataPermissionRespDTO teamDTO) {
        Map<String, String> map = new HashMap<>(16);
        map.put("withSql", " ");
        map.put("withLeftSql", " ");
        if (searchDto.getApplicationStatusType() == RecruitingKpiApplicationStatusType.CURRENT) {
            String withSql = """
                    WITH interview_max as (
                                    select
                                    id,
                                    max(progress) progress,
                                    talent_recruitment_process_id
                                    from talent_recruitment_process_interview
                                    group by talent_recruitment_process_id
                                    )
                    """;
            String withLeftSql = """
                    left join interview_max max on max.talent_recruitment_process_id = node.talent_recruitment_process_id
                    """;
            map.put("withSql", withSql);
            map.put("withLeftSql", withLeftSql);
        }
        map.put("selectFields", searchDto.getSelectFields(searchDto));
        map.put("processUserRelation", getKpiUserJoinTables(searchDto.getReportApplicationStatus()));
        map.put("fromTables", searchDto.getFromTables(searchDto.getReportApplicationStatus()));
        map.put("leftJoinTables", getLeftJobTablesForApplication(searchDto));
        map.put("whereCondition", getWhereConditionForApplication(searchDto, whereParamMap, teamDTO));
        map.put("orderBy", searchDto.getOrderBy());
        return StrUtil.format(sql, map);
    }

    private String getKpiUserJoinTables(ReportApplicationStatus reportApplicationStatus){
        if (reportApplicationStatus == null) {
            return " ";
        }
        return switch (reportApplicationStatus) {
            case SUBMIT_TO_JOB -> " inner join talent_recruitment_process_user_relation ul on ul.talent_recruitment_process_id = trp.id and ul.node_type in (10,-2) ";
            case SUBMIT_TO_CLIENT -> " inner join talent_recruitment_process_user_relation ul on ul.talent_recruitment_process_id = trp.id and ul.node_type in (20,-2) ";
            case OFFER -> " inner join talent_recruitment_process_user_relation ul on ul.talent_recruitment_process_id = trp.id and ul.node_type in (40,-2) ";
            case OFFER_ACCEPT -> " inner join talent_recruitment_process_user_relation ul on ul.talent_recruitment_process_id = trp.id and ul.node_type in (41,-2) ";
            case ON_BOARD ->  " inner join talent_recruitment_process_user_relation ul on ul.talent_recruitment_process_id = trp.id and ul.node_type in (60,-2) ";
            case ELIMINATED -> " inner join talent_recruitment_process_user_relation ul on ul.talent_recruitment_process_id = trp.id and ul.node_type in (-1,-2) ";
            case INTERVIEW,INTERVIEW_FIRST,INTERVIEW_SECOND,INTERVIEW_FINAL,TWO_OR_MORE_INTERVIEW,INTERVIEW_APPOINTMENTS -> " inner join talent_recruitment_process_user_relation ul on (\n" +
                    "(ul.talent_recruitment_process_id = trp.id and ul.node_type in (30) and node.id = ul.node_id) or (ul.talent_recruitment_process_id = trp.id and ul.node_type in (-2)))";
        };
    }

    private String getWhereConditionForApplication(RecruitingKpiApplicationDetailSearchDto searchDto, Map<String, Object> whereParamMap, TeamDataPermissionRespDTO teamDTO) {
        StringBuilder sb = new StringBuilder();
        ReportApplicationStatus reportApplicationStatus = searchDto.getReportApplicationStatus();
        boolean eventFlag = searchDto.getDateType() == RecruitingKpiDateType.EVENT;
        boolean currentFlag = searchDto.getApplicationStatusType() == RecruitingKpiApplicationStatusType.CURRENT;
        sb.append(" trp.tenant_id = :tenantId ");
        whereParamMap.put("tenantId", SecurityUtils.getTenantId());
        String fieldColumn = " node.created_date ";
        whereParamMap.put("startDate", searchDto.getStartDateUtc());
        whereParamMap.put("endDate", searchDto.getEndDateUtc());
        if (eventFlag) {
            if (reportApplicationStatus == ReportApplicationStatus.SUBMIT_TO_CLIENT) {
                fieldColumn = " DATE_FORMAT(CONVERT_TZ(node.submit_time, 'UTC', '" + searchDto.getTimezone() + "'), '%Y-%m-%d') ";//"node.submit_time_format";
                whereParamMap.put("startDate", searchDto.getStartDate());
                whereParamMap.put("endDate", searchDto.getEndDate());
            } else if (List.of(ReportApplicationStatus.INTERVIEW_FIRST,
                            ReportApplicationStatus.INTERVIEW_SECOND,
                            ReportApplicationStatus.INTERVIEW_FINAL,
                            ReportApplicationStatus.INTERVIEW,
                            ReportApplicationStatus.TWO_OR_MORE_INTERVIEW)
                    .contains(reportApplicationStatus)) {
                fieldColumn = " node.from_time_utc ";
            } else if (reportApplicationStatus == ReportApplicationStatus.ON_BOARD) {
                fieldColumn = "trpod.onboard_date";
                whereParamMap.put("startDate", searchDto.getStartDate());
                whereParamMap.put("endDate", searchDto.getEndDate());
            }
        }
        sb.append(" and ").append(fieldColumn).append(" BETWEEN :startDate AND :endDate ");
        if (reportApplicationStatus == ReportApplicationStatus.SUBMIT_TO_JOB) {
            appendApplicationDetailStayedOver(searchDto, sb, 24);
        } else if (reportApplicationStatus == ReportApplicationStatus.SUBMIT_TO_CLIENT) {
            appendApplicationDetailStayedOver(searchDto, sb, 72);
        }
        appendDetailCommonCondition(searchDto.getCompanyId(), searchDto.getJobId(), searchDto.getUserId(), searchDto.getTeamId(), sb, whereParamMap);
        appendCommonUserIdAndTeamId(searchDto, sb, whereParamMap);
        appendSearchConditionAndPermission(sb, searchDto, whereParamMap, teamDTO);
        appendJobDataPermissionForJobOrDetail(sb, searchDto, whereParamMap, teamDTO);
        if (List.of(ReportApplicationStatus.INTERVIEW_FIRST, ReportApplicationStatus.INTERVIEW_SECOND, ReportApplicationStatus.INTERVIEW_FINAL,ReportApplicationStatus.TWO_OR_MORE_INTERVIEW).contains(searchDto.getReportApplicationStatus())) {
            switch (searchDto.getReportApplicationStatus()) {
                case INTERVIEW_FIRST -> sb.append(" and node.progress = 1 ").append(currentFlag? " and max.progress = 1 ": "");
                case INTERVIEW_SECOND -> sb.append(" and node.progress = 2 ").append(currentFlag? " and max.progress = 2 ": "");
                case INTERVIEW_FINAL -> sb.append(" and node.final_round = 1 ");
                case TWO_OR_MORE_INTERVIEW -> sb.append(" and node.progress >=2 and node.final_round !=1 ");
            }
        }
        searchDto.appendDetailCondition(sb, whereParamMap);
        return sb.toString();
    }

    private String getLeftJobTablesForApplication(RecruitingKpiApplicationDetailSearchDto searchDto) {
        ReportApplicationStatus reportApplicationStatus = searchDto.getReportApplicationStatus();
        StringBuilder sb = new StringBuilder();
        boolean isCurrentFlag = searchDto.getApplicationStatusType() == RecruitingKpiApplicationStatusType.CURRENT;
        if (isCurrentFlag) {
            sb.append(getCurrentNodeTypeForCurrentByApplicationStatus(searchDto.getReportApplicationStatus()));
        } else {
            sb.append(" INNER JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id AND trpn.node_status IN ( 1, 4 ) ");
        }
        if (reportApplicationStatus == ReportApplicationStatus.OFFER_ACCEPT
                || (reportApplicationStatus == ReportApplicationStatus.ON_BOARD && !isCurrentFlag)) {
            sb.append(" left join talent_recruitment_process_onboard_date trpod on trpod.talent_recruitment_process_id = trp.id ");
        }
        if (List.of(ReportApplicationStatus.INTERVIEW,
                ReportApplicationStatus.INTERVIEW_FIRST,
                ReportApplicationStatus.INTERVIEW_SECOND,
                ReportApplicationStatus.INTERVIEW_FINAL,
                ReportApplicationStatus.INTERVIEW_APPOINTMENTS,
                ReportApplicationStatus.TWO_OR_MORE_INTERVIEW).contains(reportApplicationStatus)) {
            sb.append(" left join talent_recruitment_process_interview trpi on trpi.talent_recruitment_process_id = trp.id ");
        }
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) &&  CollUtil.isNotEmpty(searchDto.getJob().getJobFunctions())) {
            sb.append(" left join job_job_function_relation jjfr on jjfr.job_id = j.id ");
        }
        if ((ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getIndustries()))
                || (ObjectUtil.isNotEmpty(searchDto.getCompany()) && CollUtil.isNotEmpty(searchDto.getCompany().getIndustries()))) {
            sb.append(" left join company_industry_relation cir on cir.company_id = c.id ");
        }
        return sb.toString();
    }

    private String getTalentDetailWhereConditionSqlAndSetParam(RecruitingKpiTalentDetailSearchDto searchDto, Map<String, Object> whereParamMap, TeamDataPermissionRespDTO teamDTO) {
        StringBuilder sb = new StringBuilder();
        appendDetailCommonCondition(searchDto.getCompanyId(), searchDto.getJobId(), searchDto.getUserId(), searchDto.getTeamId(), sb, whereParamMap);
        appendCommonUserIdAndTeamId(searchDto, sb, whereParamMap);
        //search permission team
        appendPermissionTeamSearch(sb);
        appendSearchConditionAndPermissionForTalent(sb, searchDto, whereParamMap, teamDTO);
        searchDto.appendDetailCondition(sb, whereParamMap);
        return sb.toString();
    }

    private String getJobDetailWhereConditionSqlAndSetParam(RecruitingKpiJobDetailSearchDto searchDto, Map<String, Object> whereParamMap, TeamDataPermissionRespDTO teamDto) {
        StringBuilder sb = new StringBuilder();
        //tenantId
        sb.append(" and j.tenant_id = :tenantId ");
        whereParamMap.put("tenantId", SecurityUtils.getTenantId());
        boolean createFlag = searchDto.getDateType() == RecruitingKpiDateType.ADD;
        String dateField = createFlag? " j.created_date ": " j.start_date_format ";
        sb.append(" and ").append(dateField).append(" BETWEEN :startDate AND :endDate ");
        whereParamMap.put("startDate", createFlag? searchDto.getStartDateUtc(): searchDto.getStartDate());
        whereParamMap.put("endDate", createFlag? searchDto.getEndDateUtc(): searchDto.getEndDate());
        appendDetailCommonCondition(searchDto.getCompanyId(), searchDto.getJobId(), searchDto.getUserId(), searchDto.getTeamId(), sb, whereParamMap);
        appendCommonUserIdAndTeamId(searchDto, sb, whereParamMap);
        //search job
        appendSearchJobForJobOrDetail(sb, searchDto, whereParamMap);
        //search company
        appendCompanySearch(sb, searchDto, whereParamMap);
        //add job permission
        appendJobDataPermissionForJobOrDetail(sb, searchDto, whereParamMap, teamDto);
        //search permission team
        appendPermissionTeamSearch(sb);
        //job detail
        searchDto.appendDetailCondition(sb, whereParamMap);
        return sb.toString();
    }

    public Page<BDReportKpiUserCompanyDetailVO> searchCreateCompanyDetailList(RecruitingKpiUserCompanyDetailSearchDto searchDto, Pageable pageable) {
        var query = dsl.select(
                        field("c.id"),
                        field("c.full_business_name").as("company_name"),
                        field("c.active").as("active"),
                        field("c.created_date").as("created_date"),
                        field("GROUP_CONCAT(DISTINCT CASE WHEN bfa.sales_lead_role = 0 THEN u.full_name END)").as("am_name"),
                        field("GROUP_CONCAT(DISTINCT CASE WHEN bfa.sales_lead_role = 1 THEN u.full_name END)").as("sales_lead_name"),
                        field("GROUP_CONCAT(DISTINCT CASE WHEN bfa.sales_lead_role = 2 THEN u.full_name END)").as("bd_owner_name"),
                        field("GROUP_CONCAT(DISTINCT CASE WHEN bfa.sales_lead_role = 3 THEN concat(u.full_name, '-', bfa.country) END)").as("co_am_name")
                ).from(companyIdSubQuery(searchDto, "created_company_count").asTable("company_id_subquery"))
                .innerJoin(table("ods_crm.account_company").as("c")).on(field("c.id").eq(field("company_id_subquery.company_id")))
                .innerJoin(table("ods_crm.business_flow_administrator").as("bfa")).on(field("bfa.account_company_id").eq(field("c.id")))
                .leftJoin(table("ods_crm.user").as("u")).on(field("bfa.user_id").eq(field("u.id")));
        Condition condition = field("c.tenant_id").eq(SecurityUtils.getTenantId());
        if (searchDto.getDetail() != null) {
            if (searchDto.getDetail().getCompanyId() != null) {
                condition = condition.and(field("c.id").eq(searchDto.getDetail().getCompanyId()));
            }
            if (searchDto.getDetail().getCompanyName() != null) {
                condition = condition.and(field("c.full_business_name").like("%" + searchDto.getDetail().getCompanyName() + "%"));
            }
        }

        OrderField<Object> orderField;
        if (ObjectUtil.isNotEmpty(searchDto.getSort())) {
            String orderBy = switch (searchDto.getSort().getProperty()) {
                case "companyName" -> "c.full_business_name";
                case "createdDate" -> "c.created_date ";
                default -> "c.created_date";
            };
            if (Sort.Direction.ASC.name().equalsIgnoreCase(searchDto.getSort().getDirection())) {
                orderField = field(orderBy).asc();
            } else {
                orderField = field(orderBy).desc();
            }
        } else {
            orderField = field("c.created_date").desc();
        }
        var finalQuery =  query.where(condition)
                .groupBy(
                        field("c.id"),
                        field("c.full_business_name"),
                        field("c.active"),
                        field("c.created_date"))
                .orderBy(orderField);
        String sql = finalQuery.getSQL(ParamType.NAMED);
        Map<String, Param<?>> params = finalQuery.getParams();
        String countSql = "SELECT COUNT(1) FROM (" + sql + ") getcount";
        Query nativeQuery = entityManager.createNativeQuery(sql, BDReportKpiUserCompanyDetailVO.class);
        Query nativeCountQuery = entityManager.createNativeQuery(countSql);
        for (Map.Entry<String, Param<?>> entry : params.entrySet()) {
            nativeQuery.setParameter(entry.getKey(), entry.getValue().getValue());
            nativeCountQuery.setParameter(entry.getKey(), entry.getValue().getValue());
        }

        // 设置分页参数
        nativeQuery.setFirstResult((pageable.getPageNumber() <= 0 ? 0 : pageable.getPageNumber() - 1) * pageable.getPageSize());
        nativeQuery.setMaxResults(pageable.getPageSize());

        // 执行查询
        BigInteger count = new BigInteger(String.valueOf(nativeCountQuery.getSingleResult()));
        Long total = count.longValue();
        List<BDReportKpiUserCompanyDetailVO> detailVOS = nativeQuery.getResultList();
        detailVOS.forEach(x -> {
            if (StringUtils.isNotBlank(x.getCoAmName())) {
                List<UserCountryVO> voList = new ArrayList<>();
                Arrays.stream(x.getCoAmName().split(",")).forEach(v -> {
                    String[] amCountry = v.split("-");
                    UserCountryVO vo = new UserCountryVO();
                    vo.setUserName(amCountry[0]);
                    vo.setCountryId(amCountry[1]);
                    voList.add(vo);
                });
                x.setCoAmList(voList);
            }
        });
        return new PageImpl<>(detailVOS, Pageable.unpaged(), total);
    }

    public Page<RecruitingKpiUpgradeToClientDetailVO> searchUpgradeToClientDetailListV2(RecruitingKpiUserCompanyDetailSearchDto searchDto, Pageable pageable) {
        var query = dsl.select(
                        field("c.id"),
                        field("c.full_business_name").as("company_name"),
                        field("c.created_date").as("created_date"),
                        field("c.request_date").as("request_date"),
                        field("GROUP_CONCAT(DISTINCT CASE WHEN bfa.sales_lead_role = 0 THEN CONCAT(u.first_name, ' ', u.last_name) END)").as("am_name"),
                        field("GROUP_CONCAT(DISTINCT CASE WHEN bfa.sales_lead_role = 1 THEN CONCAT(u.first_name, ' ', u.last_name) END)").as("sales_lead_name"),
                        field("GROUP_CONCAT(DISTINCT CASE WHEN bfa.sales_lead_role = 2 THEN CONCAT(u.first_name, ' ', u.last_name) END)").as("bd_owner_name"),
                        field("GROUP_CONCAT(DISTINCT CASE WHEN bfa.sales_lead_role = 3 THEN CONCAT(CONCAT(u.first_name, ' ', u.last_name), '-', bfa.country) END)").as("co_am_name"),
                        countDistinct(field("j.id")).as("open_job_count")
                ).from(companyIdSubQuery(searchDto, "upgrade_company_count").asTable("company_id_subquery"))
                .innerJoin(table("company").as("c")).on(field("c.id").eq(field("company_id_subquery.company_id")))
                .innerJoin(table("business_flow_administrator").as("bfa")).on(field("bfa.company_id").eq(field("c.id")))
                .leftJoin(table("job").as("j")).on(field("j.company_id").eq(field("c.id")))
                .leftJoin(table("user").as("u")).on(field("bfa.user_id").eq(field("u.id")));
        Condition condition = field("c.tenant_id").eq(SecurityUtils.getTenantId()).and(field("c.active").eq(30));
        if (searchDto.getDetail() != null && searchDto.getDetail().getCompanyId() != null) {
            condition = condition.and(field("c.id").eq(searchDto.getDetail().getCompanyId()));
        }
        OrderField<Object> orderField;
        if (ObjectUtil.isNotEmpty(searchDto.getSort())) {
            String orderBy = switch (searchDto.getSort().getProperty()) {
                case "companyName" -> "c.full_business_name";
                case "createdDate" -> "c.created_date ";
                case "requestDate" -> "c.request_date ";
                case "openJobCount" -> "open_job_count ";
                default -> "c.created_date";
            };
            if (Sort.Direction.ASC.name().equalsIgnoreCase(searchDto.getSort().getDirection())) {
                orderField = field(orderBy).asc();
            } else {
                orderField = field(orderBy).desc();
            }
        } else {
            orderField = field("c.created_date").desc();
        }
        var finalQuery =  query.where(condition)
                .groupBy(
                        field("c.id"),
                        field("c.full_business_name"),
                        field("c.created_date"),
                        field("c.request_date"))
                .orderBy(orderField);
        String sql = finalQuery.getSQL(ParamType.NAMED);
        Map<String, Param<?>> params = finalQuery.getParams();
        String countSql = "SELECT COUNT(1) FROM (" + sql + ") getcount";
        Query nativeQuery = entityManager.createNativeQuery(sql, RecruitingKpiUpgradeToClientDetailVO.class);
        Query nativeCountQuery = entityManager.createNativeQuery(countSql);
        for (Map.Entry<String, Param<?>> entry : params.entrySet()) {
            nativeQuery.setParameter(entry.getKey(), entry.getValue().getValue());
            nativeCountQuery.setParameter(entry.getKey(), entry.getValue().getValue());
        }

        // 设置分页参数
        nativeQuery.setFirstResult((pageable.getPageNumber() <= 0 ? 0 : pageable.getPageNumber() - 1) * pageable.getPageSize());
        nativeQuery.setMaxResults(pageable.getPageSize());

        // 执行查询
        BigInteger count = new BigInteger(String.valueOf(nativeCountQuery.getSingleResult()));
        Long total = count.longValue();
        List<RecruitingKpiUpgradeToClientDetailVO> detailVOS = nativeQuery.getResultList();

        // 处理结果
        processResult(detailVOS);

        return new PageImpl<>(detailVOS, Pageable.unpaged(), total);
    }

    private SelectConditionStep<Record1<Long>> companyIdSubQuery(RecruitingKpiUserCompanyDetailSearchDto searchDto, String metric) {
        SelectJoinStep<Record1<Long>> query = dsl.select(field("unnest_bitmap", Long.class).as("company_id"))
                .from(table(sql("mv_created_kpi_v2, unnest_bitmap(%s)".formatted(metric))));
        Condition condition = field("mv_created_kpi_v2.tenant_id").eq(SecurityUtils.getTenantId())
                .and(ADD_DATE.between(
                        getUtcByTimeZone(searchDto.getStartDate() + " 00:00:00", searchDto.getTimezone()),
                        getUtcByTimeZone(searchDto.getEndDate() + " 23:59:59", searchDto.getTimezone())
                ))
                .and(field("BITMAP_COUNT(%s)".formatted(metric), Integer.class).gt(0));
        if (CollUtil.isNotEmpty(searchDto.getUserIdList())) {
            condition = condition.and(USER_ID.in(searchDto.getUserIdList()));
        }
        if (ObjectUtil.isNotEmpty(searchDto.getTeamId())) {
            condition = condition.and(TEAM_ID.in(searchDto.getTeamId()));
        }
        return query.where(condition);
    }

    public Page<RecruitingKpiUpgradeToClientDetailVO> searchUpgradeToClientDetailList(RecruitingKpiUserCompanyDetailSearchDto searchDto, Pageable pageable) {
        // 构建基础 SQL
        StringBuilder sqlBuilder = new StringBuilder(buildBaseSql(searchDto));

        // 添加排序逻辑
        appendSortClause(sqlBuilder, searchDto);

        // 构建计数 SQL
        String countSql = "SELECT COUNT(*) FROM (" + sqlBuilder.toString() + ") getcount";

        // 创建查询对象
        Query query = entityManager.createNativeQuery(sqlBuilder.toString(), RecruitingKpiUpgradeToClientDetailVO.class);
        Query countQuery = entityManager.createNativeQuery(countSql);

        // 设置查询参数
        setQueryParameters(query, countQuery, searchDto);

        // 设置分页参数
        query.setFirstResult((pageable.getPageNumber() <= 0 ? 0 : pageable.getPageNumber() - 1) * pageable.getPageSize());
        query.setMaxResults(pageable.getPageSize());

        // 执行查询
        BigInteger count = new BigInteger(String.valueOf(countQuery.getSingleResult()));
        Long total = count.longValue();
        List<RecruitingKpiUpgradeToClientDetailVO> detailVOS = query.getResultList();

        // 处理结果
        processResult(detailVOS);

        return new PageImpl<>(detailVOS, Pageable.unpaged(), total);
    }

    private String buildBaseSql(RecruitingKpiUserCompanyDetailSearchDto searchDto) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("""
        SELECT
            c.id,
            c.full_business_name company_name,
            c.created_date created_date,
            c.request_date,
            GROUP_CONCAT(DISTINCT CASE WHEN bfa.sales_lead_role = 0 THEN CONCAT(u.first_name, ' ', u.last_name) END) AS am_name,
            GROUP_CONCAT(DISTINCT CASE WHEN bfa.sales_lead_role = 1 THEN CONCAT(u.first_name, ' ', u.last_name) END) AS sales_lead_name,
            GROUP_CONCAT(DISTINCT CASE WHEN bfa.sales_lead_role = 2 THEN CONCAT(u.first_name, ' ', u.last_name) END) AS bd_owner_name,
            GROUP_CONCAT(DISTINCT CASE WHEN bfa.sales_lead_role = 3 THEN CONCAT(CONCAT(u.first_name, ' ', u.last_name), '-', bfa.country) END) AS co_am_name,
            COUNT(DISTINCT j.id) open_job_count
        FROM
            company c
            INNER JOIN business_flow_administrator bfa ON bfa.company_id = c.id
            LEFT JOIN job j ON j.company_id = c.id AND j.status = 0
            LEFT JOIN user u ON u.id = bfa.user_id
        WHERE c.active = 30 AND c.tenant_id = :tenantId
            AND DATE_FORMAT(CONVERT_TZ(c.request_date, 'UTC', :timezone), '%Y-%m-%d') BETWEEN :startDate AND :endDate
    """);

        if (!Stream.of(RecruitingKpiGroupByFieldType.TEAM, RecruitingKpiGroupByFieldType.USER)
                .anyMatch(searchDto.getGroupByFieldList()::contains)) {
            if (null != searchDto.getUserIdList() && !searchDto.getUserIdList().isEmpty()) {
                addBaseSqlWhereClause(sqlBuilder,searchDto);
            }
        } else {
            addBaseSqlWhereClause(sqlBuilder,searchDto);
        }

        // 如果指定了公司 ID，则添加公司 ID 条件
        if (null != searchDto.getDetail() && null != searchDto.getDetail().getCompanyId()) {
            sqlBuilder.append(" AND c.id = :companyId ");
        }

        sqlBuilder.append(" GROUP BY c.id, c.full_business_name, c.active, c.request_date ");
        return sqlBuilder.toString();
    }

    private void addBaseSqlWhereClause(StringBuilder sqlBuilder,RecruitingKpiUserCompanyDetailSearchDto searchDto){
        sqlBuilder.append("""
                  and  c.id in (
                                 SELECT
                                     c.id
                                 FROM
                                     company c
                                     INNER JOIN business_flow_administrator bfa ON bfa.company_id = c.id
                                 where c.active=30 and c.tenant_id = :tenantId
                                      and date_format(CONVERT_TZ(c.request_date, 'UTC', :timezone),'%Y-%m-%d') BETWEEN :startDate AND :endDate
                                   
                """);
        if (null != searchDto.getUserIdList() && !searchDto.getUserIdList().isEmpty() && null != searchDto.getTeamIdList() && !searchDto.getTeamIdList().isEmpty()) {
            sqlBuilder.append(" and (bfa.user_id in (select id from user where id in :userIdList ) or bfa.user_id in (select ud from user where id in(select user_id from permission_user_team  where team_id in :teamIdList and is_primary = 1 )) ) ");
        } else {
            if (null != searchDto.getUserIdList() && !searchDto.getUserIdList().isEmpty()) {
                sqlBuilder.append( " and bfa.user_id in (select id from user where id in :userIdList) ");
            }
            if (null != searchDto.getTeamIdList() && !searchDto.getTeamIdList().isEmpty()) {
                sqlBuilder.append(" and bfa.user_id in (select id from user where id in (select user_id from permission_user_team  where team_id in :teamIdList and is_primary = 1 )) ");
            }
        }

        sqlBuilder.append("""
                GROUP BY c.id )
                """);
    }

    private void appendSortClause(StringBuilder sqlBuilder, RecruitingKpiUserCompanyDetailSearchDto searchDto) {
        if (ObjectUtil.isNotEmpty(searchDto.getSort())) {
            sqlBuilder.append(" ORDER BY ")
                    .append(switch (searchDto.getSort().getProperty()) {
                        case "companyName" -> "CONVERT(c.full_business_name USING gbk) " + searchDto.getSort().getDirection();
                        case "createdDate" -> "c.created_date " + searchDto.getSort().getDirection();
                        case "requestDate" -> "c.request_date " + searchDto.getSort().getDirection();
                        case "openJobCount" -> "open_job_count " + searchDto.getSort().getDirection();
                        default -> "c.created_date DESC";
                    });
        } else {
            sqlBuilder.append(" ORDER BY c.created_date DESC ");
        }
    }

    private void setQueryParameters(Query query, Query countQuery, RecruitingKpiUserCompanyDetailSearchDto searchDto) {
        if (CollUtil.isNotEmpty(searchDto.getUserIdList())) {
            query.setParameter("userIdList", searchDto.getUserIdList());
            countQuery.setParameter("userIdList", searchDto.getUserIdList());
        }
        if (CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
            query.setParameter("teamIdList", searchDto.getTeamIdList());
            countQuery.setParameter("teamIdList", searchDto.getTeamIdList());
        }
        query.setParameter("startDate", searchDto.getStartDate());
        query.setParameter("endDate", searchDto.getEndDate());
        query.setParameter("timezone", searchDto.getTimezone());
        query.setParameter("tenantId", SecurityUtils.getTenantId());
        countQuery.setParameter("startDate", searchDto.getStartDate());
        countQuery.setParameter("endDate", searchDto.getEndDate());
        countQuery.setParameter("timezone", searchDto.getTimezone());
        countQuery.setParameter("tenantId", SecurityUtils.getTenantId());

        if (null != searchDto.getDetail() && null != searchDto.getDetail().getCompanyId()) {
            query.setParameter("companyId", searchDto.getDetail().getCompanyId());
            countQuery.setParameter("companyId", searchDto.getDetail().getCompanyId());
        }
    }

    private void processResult(List<RecruitingKpiUpgradeToClientDetailVO> detailVOS) {
        detailVOS.forEach(x -> {
            if (StringUtils.isNotBlank(x.getCoAmName())) {
                List<UserCountryVO> voList = Arrays.stream(x.getCoAmName().split(","))
                        .map(v -> {
                            String[] amCountry = v.split("-");
                            UserCountryVO vo = new UserCountryVO();
                            vo.setUserName(amCountry[0]);
                            vo.setCountryId(amCountry[1]);
                            return vo;
                        })
                        .collect(Collectors.toList());
                x.setCoAmList(voList);
            }
        });
    }

    protected void appendDetailCommonCondition(Long companyId, Long jobId, Long userId, Long teamId, StringBuilder sb, Map<String, Object> whereParamMap) {
        if (ObjectUtil.isNotEmpty(companyId)) {
            sb.append(" and c.id = :companyIdOnly ");
            whereParamMap.put("companyIdOnly", companyId);
        }
        if (ObjectUtil.isNotEmpty(jobId)) {
            sb.append(" and j.id = :jobIdOnly ");
            whereParamMap.put("jobIdOnly", jobId);
        }
        if (ObjectUtil.isNotEmpty(userId)) {
            sb.append(" and put.user_id = :userId ");
            whereParamMap.put("userId", userId);
        }
        if (ObjectUtil.isNotEmpty(teamId)) {
            sb.append(" and put.team_id IN (SELECT id FROM permission_team WHERE code LIKE CONCAT((SELECT code FROM permission_team WHERE id = :teamId), '%')) ");
            whereParamMap.put("teamId", teamId);
        }
    }
}
