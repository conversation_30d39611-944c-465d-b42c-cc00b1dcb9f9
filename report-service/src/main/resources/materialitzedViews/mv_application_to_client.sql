CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.mv_application_to_client
       DISTRIBUTED BY HASH (`team_id`)
REFRESH
       ASYNC EVERY (INTERVAL 20 MINUTE)
ORDER BY (add_date, event_date)
AS
SELECT tenant_id,
       company_id,
       job_id,
       job_pteam_id,
       team_id,
       team_name,
       user_id,
       user_name,
       user_activated,
       add_date                                                                 AS add_date,
       event_date                                                               AS event_date,
       BITMAP_AGG(user_role)                                                    AS user_roles,
       BITMAP_AGG(application.node_id)                                          AS submit_to_client_countNum,
       BITMAP_AGG(IF((ai_score IS NOT NULL), application.talent_recruitment_process_id,
                     NULL))                                                     AS submit_to_client_aiRecommendCountNum,
       BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL), application.talent_recruitment_process_id,
                     NULL))                                                     AS submit_to_client_precisionAiRecommendNum,

       BITMAP_AGG(
               CASE
                   WHEN pn.stayed_add_date IS NOT NULL THEN
                       IF(TIMESTAMPDIFF(HOUR, application.add_date, pn.stayed_add_date) > 72, 1, NULL)
                   WHEN application.node_status = 1 THEN
                       IF(TIMESTAMPDIFF(HOUR, application.add_date, NOW()) > 72, 1, NULL)
                   END
       )
                                                                                AS submit_to_client_stayedOver,
       BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL)) AS submit_to_client_current_countNum,
       BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1),
                     application.talent_recruitment_process_id,
                     NULL))                                                     AS submit_to_client_currentAiRecommendNum,
       BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1),
                     application.talent_recruitment_process_id,
                     NULL))                                                     AS submit_to_client_currentPrecisionAiRecommendNum,
       BITMAP_AGG(IF(((`application`.`node_status` = 1) AND (`application`.`node_type` = 20)) AND
                     ((TIMESTAMPDIFF(HOUR, `application`.add_date, now())) > 72), 1, NULL))
                                                                                AS submit_to_client_currentStayedOver
FROM mv_application_wide AS application
         left join (select pn.talent_recruitment_process_id,
                           IF(MIN(pn.add_date) IS NOT NULL, MIN(pn.add_date),
                              MAX(eli.add_date)) as stayed_add_date
                    from mv_application_fact pn
                             left join mv_application_fact eli on eli.talent_recruitment_process_id =
                                                                  pn.talent_recruitment_process_id and
                                                                  eli.node_type = -1
                    where pn.node_type > 20
                    group by pn.talent_recruitment_process_id) pn
                   on pn.talent_recruitment_process_id = application.talent_recruitment_process_id

WHERE application.node_type = 20
GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated,
         add_date, event_date;
