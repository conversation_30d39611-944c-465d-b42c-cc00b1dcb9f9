CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.mv_application_offer
       DISTRIBUTED BY HASH (`team_id`)
REFRESH
       ASYNC EVERY (INTERVAL 20 MINUTE)
ORDER BY (add_date, event_date)
AS
SELECT tenant_id,
       company_id,
       job_id,
       job_pteam_id,
       team_id,
       team_name,
       user_id,
       user_name,
       user_activated,
       add_date                                                                 AS add_date,
       event_date                                                               AS event_date,
       BITMAP_AGG(user_role)                                                    AS user_roles,
       BITMAP_AGG(application.node_id)                                          AS offer_countNum,
       BITMAP_AGG(IF((ai_score IS NOT NULL), application.talent_recruitment_process_id,
                     NULL))                                                     AS offer_aiRecommendCountNum,
       BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL), application.talent_recruitment_process_id,
                     NULL))                                                     AS offer_precisionAiRecommendNum,
       BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL)) AS offer_current_countNum,
       BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1),
                     application.talent_recruitment_process_id, NULL))          AS offer_currentAiRecommendNum,
       BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1),
                     application.talent_recruitment_process_id,
                     NULL))                                                     AS offer_currentPrecisionAiRecommendNum

FROM mv_application_wide AS application
WHERE node_type = 40
GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated,
         add_date, event_date