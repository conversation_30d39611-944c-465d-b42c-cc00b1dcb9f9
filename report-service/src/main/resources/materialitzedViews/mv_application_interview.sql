CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.mv_application_interview
       DISTRIBUTED BY HASH (`team_id`)
REFRESH
       ASYNC EVERY (INTERVAL 20 MINUTE)
ORDER BY (add_date, event_date)
AS
SELECT tenant_id,
       company_id,
       job_id,
       job_pteam_id,
       team_id,
       team_name,
       user_id,
       user_name,
       user_activated,
       add_date              AS add_date,
       event_date            AS event_date,
       BITMAP_AGG(user_role) AS user_roles,
       BITMAP_AGG(CASE
                      WHEN application.progress = 1 AND node_type = 30
                          THEN node_id
           END)              AS interview1,

       BITMAP_AGG(CASE
                      WHEN application.progress = 2
                          AND node_type = 30
                          THEN node_id
           END)              AS interview2,

       BITMAP_AGG(CASE
                      WHEN (
                          application.progress >= 2
                              AND node_type = 30
                              AND final_round <> 1
                          ) THEN node_id
           END)              AS two_or_more_interviews,

       BITMAP_AGG(CASE
                      WHEN
                          final_round = 1
                              AND node_type = 30
                          THEN node_id
           END)              AS interview_final,
       BITMAP_AGG(node_id)   AS interview_total,
       BITMAP_AGG(talent_id) AS unique_interview_talents,

       BITMAP_AGG(talent_recruitment_process_id)
                             AS interview_total_process,

       BITMAP_AGG(CASE
                      WHEN (
                          ai_score IS NOT NULL
                              AND node_type = 30
                          ) THEN node_id
           END)              AS interviewTotalAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          ai_score IS NOT NULL
                          ) THEN talent_recruitment_process_id
           END)
                             AS interviewTotalProcessAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN
                          application.progress = 1
                              AND ai_score IS NOT NULL
                          THEN talent_recruitment_process_id
           END)
                             AS interview1AiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN
                          application.progress = 2
                              AND ai_score IS NOT NULL
                          THEN talent_recruitment_process_id
           END)
                             AS interview2AiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          application.progress >= 2
                              AND final_round <> 1
                              AND node_type = 30
                              AND ai_score IS NOT NULL
                          ) THEN node_id
           END)              AS twoOrMoreInterviewsAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN final_round = 1 AND ai_score IS NOT NULL
                          THEN talent_recruitment_process_id
           END)
                             AS interviewFinalAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN recommend_feedback_id IS NOT NULL AND node_type = 30 THEN node_id
           END)              AS interviewTotalPrecisionAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN recommend_feedback_id IS NOT NULL THEN talent_recruitment_process_id
           END)
                             AS interviewNumProcessPrecisionAIRecommend,

       BITMAP_AGG(CASE
                      WHEN (
                          application.progress = 1
                              AND recommend_feedback_id IS NOT NULL
                          ) THEN talent_recruitment_process_id
           END)
                             AS interview1PrecisionAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          application.progress = 2
                              AND recommend_feedback_id IS NOT NULL
                          ) THEN talent_recruitment_process_id
           END)
                             AS interview2PrecisionAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          application.progress >= 2
                              AND final_round <> 1
                              AND node_type = 30
                              AND recommend_feedback_id IS NOT NULL
                          ) THEN node_id
           END)              AS twoOrMoreInterviewsPrecisionAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          final_round = 1
                              AND recommend_feedback_id IS NOT NULL
                          ) THEN talent_recruitment_process_id
           END)
                             AS interviewFinalPrecisionAiRecommendNum,
       BITMAP_AGG(CASE
                      WHEN (
                          application.progress = 1
                              AND node_status = 1
                              AND node_type = 30
                              AND max_progress_subquery.progress = 1
                          ) THEN node_id
           END)              AS current_interview1,

       BITMAP_AGG(CASE
                      WHEN (
                          application.progress = 2
                              AND node_status = 1
                              AND node_type = 30
                              AND max_progress_subquery.progress = 2
                          ) THEN node_id
           END)              AS current_interview2,

       BITMAP_AGG(CASE
                      WHEN (
                          application.progress >= 2
                              AND final_round <> 1
                              AND node_status = 1
                              AND node_type = 30
                          ) THEN node_id
           END)              AS current_two_or_more_interviews,
       BITMAP_AGG(CASE
                      WHEN (
                          final_round = 1
                              AND node_status = 1
                              AND node_type = 30
                          ) THEN node_id
           END)              AS current_interview_final,
       BITMAP_AGG(CASE
                      WHEN (
                          node_status = 1
                              AND node_type = 30
                          ) THEN node_id
           END)              AS current_interview_total,

       BITMAP_AGG(CASE
                      WHEN (
                          node_status = 1
                          ) THEN talent_recruitment_process_id
           END)
                             AS current_interview_total_process,

       BITMAP_AGG(CASE
                      WHEN (
                          ai_score IS NOT NULL
                              AND node_status = 1
                              AND node_type = 30
                          ) THEN node_id
           END)              AS currentInterviewTotalAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          ai_score IS NOT NULL
                              AND node_status = 1
                          ) THEN talent_recruitment_process_id
           END)
                             AS currentInterviewTotalProcessAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          application.progress = 1
                              AND node_status = 1
                              AND max_progress_subquery.progress = 1
                              AND ai_score IS NOT NULL
                          ) THEN talent_recruitment_process_id
           END)
                             AS currentInterview1AiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          application.progress = 2
                              AND node_status = 1
                              AND max_progress_subquery.progress = 2
                              AND ai_score IS NOT NULL
                          ) THEN talent_recruitment_process_id
           END)
                             AS currentInterview2AiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          application.progress >= 2
                              AND final_round <> 1
                              AND node_status = 1
                              AND node_type = 30
                              AND ai_score IS NOT NULL
                          ) THEN node_id
           END)              AS currentTwoOrMoreInterviewsAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          final_round = 1
                              AND node_status = 1
                              AND ai_score IS NOT NULL
                          ) THEN talent_recruitment_process_id
           END)
                             AS currentInterviewFinalAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          recommend_feedback_id IS NOT NULL
                              AND node_status = 1
                              AND node_type = 30
                          ) THEN node_id
           END)              AS currentInterviewTotalPrecisionAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          recommend_feedback_id IS NOT NULL
                              AND node_status = 1
                          ) THEN talent_recruitment_process_id
           END)
                             AS currentInterviewNumProcessPrecisionAIRecommend,

       BITMAP_AGG(CASE
                      WHEN (
                          application.progress = 1
                              AND max_progress_subquery.progress = 1
                              AND node_status = 1
                              AND recommend_feedback_id IS NOT NULL
                          ) THEN talent_recruitment_process_id
           END)
                             AS currentInterview1PrecisionAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          application.progress = 2
                              AND max_progress_subquery.progress = 2
                              AND node_status = 1
                              AND recommend_feedback_id IS NOT NULL
                          ) THEN talent_recruitment_process_id
           END)
                             AS currentInterview2PrecisionAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          application.progress >= 2
                              AND final_round <> 1
                              AND node_status = 1
                              AND node_type = 30
                              AND recommend_feedback_id IS NOT NULL
                          ) THEN node_id
           END)              AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          application.final_round = 1
                              AND node_status = 1
                              AND recommend_feedback_id IS NOT NULL
                          ) THEN talent_recruitment_process_id
           END)
                             AS currentInterviewFinalPrecisionAiRecommendNum
FROM mv_application_wide AS application
         LEFT OUTER JOIN (SELECT talent_recruitment_process_id AS application_id,
                                 MAX(progress)                 AS progress
                          FROM mv_application_fact fact
                          WHERE node_type = 30
                          GROUP BY talent_recruitment_process_id) AS max_progress_subquery
                         ON application_id = talent_recruitment_process_id
WHERE node_type = 30
GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated,
         add_date, event_date