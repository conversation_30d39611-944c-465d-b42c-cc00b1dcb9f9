CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.mv_application_emiminate
       DISTRIBUTED BY HASH (`team_id`)
REFRESH
       ASYNC EVERY (INTERVAL 20 MINUTE)
ORDER BY (add_date, event_date)
AS
SELECT tenant_id,
       company_id,
       job_id,
       job_pteam_id,
       team_id,
       team_name,
       user_id,
       user_name,
       user_activated,
       add_date                        AS add_date,
       event_date                      AS event_date,
       BITMAP_AGG(user_role)           AS user_roles,
       BITMAP_AGG(application.node_id) AS eliminate_countNum,

       BITMAP_AGG(CASE
                      WHEN (
                          ai_score IS NOT NULL
                              AND node_id IS NOT NULL
                          ) THEN talent_recruitment_process_id
           END)                        AS eliminate_aiRecommendCountNum,

       BITMAP_AGG(CASE
                      WHEN (
                          recommend_feedback_id IS NOT NULL
                              AND node_id IS NOT NULL
                          ) THEN talent_recruitment_process_id
           END)                        AS eliminate_precisionAiRecommendNum,
       BITMAP_AGG(node_id)   AS eliminate_current_countNum,

       BITMAP_AGG(CASE
                      WHEN (
                          ai_score IS NOT NULL
                              AND node_id IS NOT NULL
                          ) THEN talent_recruitment_process_id
           END)              AS eliminate_currentAiRecommendNum,

       BITMAP_AGG(CASE
                      WHEN (
                          recommend_feedback_id IS NOT NULL
                              AND node_id IS NOT NULL
                          ) THEN talent_recruitment_process_id
           END)              AS eliminate_currentPrecisionAiRecommendNum
FROM mv_application_wide AS application
WHERE node_type = -1
GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated,
         add_date, event_date