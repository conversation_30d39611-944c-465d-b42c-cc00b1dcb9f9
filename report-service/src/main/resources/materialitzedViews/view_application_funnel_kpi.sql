CREATE VIEW ods_apn.view_application_funnel_kpi
AS
SELECT base.tenant_id,
       base.company_id,
       base.job_id,
       base.job_pteam_id,
       hierarchy.parent_team_id                                      AS team_id,
       hierarchy.parent_team_name                                    AS team_name,
       hierarchy.parent_team_parent_id                               AS team_parent_id,
       hierarchy.parent_team_level                                   AS team_level,
       hierarchy.parent_team_is_leaf                                 AS team_is_leaf,
       base.user_id,
       base.user_name,
       base.user_activated,
       base.add_date,
       base.event_date,
       BITMAP_UNION(base.user_roles)                                 AS user_roles,
       BITMAP_UNION(base.submit_to_job_countNum)                     AS submit_to_job_countNum,
       BITMAP_UNION(base.submit_to_job_aiRecommendCountNum)          AS submit_to_job_aiRecommendCountNum,
       BITMAP_UNION(base.submit_to_job_precisionAiRecommendNum)      AS submit_to_job_precisionAiRecommendNum,
       BITMAP_UNION(base.submit_to_job_stayedOver)                   AS submit_to_job_stayedOver,
       BITMAP_UNION(base.submit_to_client_countNum)                  AS submit_to_client_countNum,
       BITMAP_UNION(base.submit_to_client_aiRecommendCountNum)       AS submit_to_client_aiRecommendCountNum,
       BITMAP_UNION(base.submit_to_client_precisionAiRecommendNum)   AS submit_to_client_precisionAiRecommendNum,
       BITMAP_UNION(base.submit_to_client_stayedOver)                AS submit_to_client_stayedOver,
       BITMAP_UNION(base.interview1)                                 AS interview1,
       BITMAP_UNION(base.interview2)                                 AS interview2,
       BITMAP_UNION(base.two_or_more_interviews)                     AS two_or_more_interviews,
       BITMAP_UNION(base.interview_final)                            AS interview_final,
       BITMAP_UNION(base.interview_total)                            AS interview_total,
       BITMAP_UNION(base.unique_interview_talents)                   AS unique_interview_talents,
       BITMAP_UNION(base.interview_total_process)                    AS interview_total_process,
       BITMAP_UNION(base.interviewTotalAiRecommendNum)               AS interviewTotalAiRecommendNum,
       BITMAP_UNION(base.interviewTotalProcessAiRecommendNum)        AS interviewTotalProcessAiRecommendNum,
       BITMAP_UNION(base.interview1AiRecommendNum)                   AS interview1AiRecommendNum,
       BITMAP_UNION(base.interview2AiRecommendNum)                   AS interview2AiRecommendNum,
       BITMAP_UNION(base.twoOrMoreInterviewsAiRecommendNum)          AS twoOrMoreInterviewsAiRecommendNum,
       BITMAP_UNION(base.interviewFinalAiRecommendNum)               AS interviewFinalAiRecommendNum,
       BITMAP_UNION(base.interviewTotalPrecisionAiRecommendNum)      AS interviewTotalPrecisionAiRecommendNum,
       BITMAP_UNION(base.interviewNumProcessPrecisionAIRecommend)    AS interviewNumProcessPrecisionAIRecommend,
       BITMAP_UNION(base.interview1PrecisionAiRecommendNum)          AS interview1PrecisionAiRecommendNum,
       BITMAP_UNION(base.interview2PrecisionAiRecommendNum)          AS interview2PrecisionAiRecommendNum,
       BITMAP_UNION(base.twoOrMoreInterviewsPrecisionAiRecommendNum) AS twoOrMoreInterviewsPrecisionAiRecommendNum,
       BITMAP_UNION(base.interviewFinalPrecisionAiRecommendNum)      AS interviewFinalPrecisionAiRecommendNum,
       BITMAP_UNION(base.reserve_interview_total)                    AS reserve_interview_total,
       BITMAP_UNION(base.reserve_interview_aiRecommendCountNum)      AS reserve_interview_aiRecommendCountNum,
       BITMAP_UNION(base.reserve_interview_precisionAiRecommendNum)  AS reserve_interview_precisionAiRecommendNum,
       BITMAP_UNION(base.offer_countNum)                             AS offer_countNum,
       BITMAP_UNION(base.offer_aiRecommendCountNum)                  AS offer_aiRecommendCountNum,
       BITMAP_UNION(base.offer_precisionAiRecommendNum)              AS offer_precisionAiRecommendNum,
       BITMAP_UNION(base.offer_accept_countNum)                      AS offer_accept_countNum,
       BITMAP_UNION(base.offer_accept_aiRecommendCountNum)           AS offer_accept_aiRecommendCountNum,
       BITMAP_UNION(base.offer_accept_precisionAiRecommendNum)       AS offer_accept_precisionAiRecommendNum,
       BITMAP_UNION(base.onboard_countNum)                           AS onboard_countNum,
       BITMAP_UNION(base.onboard_aiRecommendCountNum)                AS onboard_aiRecommendCountNum,
       BITMAP_UNION(base.onboard_precisionAiRecommendNum)            AS onboard_precisionAiRecommendNum,
       BITMAP_UNION(base.eliminate_countNum)                         AS eliminate_countNum,
       BITMAP_UNION(base.eliminate_aiRecommendCountNum)              AS eliminate_aiRecommendCountNum,
       BITMAP_UNION(base.eliminate_precisionAiRecommendNum)          AS eliminate_precisionAiRecommendNum
FROM (((SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles     AS user_roles,
               submit_to_job_countNum,
               submit_to_job_aiRecommendCountNum,
               submit_to_job_precisionAiRecommendNum,
               submit_to_job_stayedOver,
               BITMAP_EMPTY() AS submit_to_client_countNum,
               BITMAP_EMPTY() AS submit_to_client_aiRecommendCountNum,
               BITMAP_EMPTY() AS submit_to_client_precisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_stayedOver,
               BITMAP_EMPTY() AS interview1,
               BITMAP_EMPTY() AS interview2,
               BITMAP_EMPTY() AS two_or_more_interviews,
               BITMAP_EMPTY() AS interview_final,
               BITMAP_EMPTY() AS interview_total,
               BITMAP_EMPTY() AS unique_interview_talents,
               BITMAP_EMPTY() AS interview_total_process,
               BITMAP_EMPTY() AS interviewTotalAiRecommendNum,
               BITMAP_EMPTY() AS interviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY() AS interview1AiRecommendNum,
               BITMAP_EMPTY() AS interview2AiRecommendNum,
               BITMAP_EMPTY() AS twoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY() AS interviewFinalAiRecommendNum,
               BITMAP_EMPTY() AS interviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY() AS interview1PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interview2PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS twoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS reserve_interview_total,
               BITMAP_EMPTY() AS reserve_interview_aiRecommendCountNum,
               BITMAP_EMPTY() AS reserve_interview_precisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_countNum,
               BITMAP_EMPTY() AS offer_aiRecommendCountNum,
               BITMAP_EMPTY() AS offer_precisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_countNum,
               BITMAP_EMPTY() AS offer_accept_aiRecommendCountNum,
               BITMAP_EMPTY() AS offer_accept_precisionAiRecommendNum,
               BITMAP_EMPTY() AS onboard_countNum,
               BITMAP_EMPTY() AS onboard_aiRecommendCountNum,
               BITMAP_EMPTY() AS onboard_precisionAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_countNum,
               BITMAP_EMPTY() AS eliminate_aiRecommendCountNum,
               BITMAP_EMPTY() AS eliminate_precisionAiRecommendNum
        FROM mv_application_to_job)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               BITMAP_EMPTY() AS submit_to_job_countNum,
               BITMAP_EMPTY() AS submit_to_job_aiRecommendCountNum,
               BITMAP_EMPTY() AS submit_to_job_precisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_stayedOver,
               submit_to_client_countNum,
               submit_to_client_aiRecommendCountNum,
               submit_to_client_precisionAiRecommendNum,
               submit_to_client_stayedOver,
               BITMAP_EMPTY() AS interview1,
               BITMAP_EMPTY() AS interview2,
               BITMAP_EMPTY() AS two_or_more_interviews,
               BITMAP_EMPTY() AS interview_final,
               BITMAP_EMPTY() AS interview_total,
               BITMAP_EMPTY() AS unique_interview_talents,
               BITMAP_EMPTY() AS interview_total_process,
               BITMAP_EMPTY() AS interviewTotalAiRecommendNum,
               BITMAP_EMPTY() AS interviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY() AS interview1AiRecommendNum,
               BITMAP_EMPTY() AS interview2AiRecommendNum,
               BITMAP_EMPTY() AS twoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY() AS interviewFinalAiRecommendNum,
               BITMAP_EMPTY() AS interviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY() AS interview1PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interview2PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS twoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS reserve_interview_total,
               BITMAP_EMPTY() AS reserve_interview_aiRecommendCountNum,
               BITMAP_EMPTY() AS reserve_interview_precisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_countNum,
               BITMAP_EMPTY() AS offer_aiRecommendCountNum,
               BITMAP_EMPTY() AS offer_precisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_countNum,
               BITMAP_EMPTY() AS offer_accept_aiRecommendCountNum,
               BITMAP_EMPTY() AS offer_accept_precisionAiRecommendNum,
               BITMAP_EMPTY() AS onboard_countNum,
               BITMAP_EMPTY() AS onboard_aiRecommendCountNum,
               BITMAP_EMPTY() AS onboard_precisionAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_countNum,
               BITMAP_EMPTY() AS eliminate_aiRecommendCountNum,
               BITMAP_EMPTY() AS eliminate_precisionAiRecommendNum
        FROM mv_application_to_client)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               BITMAP_EMPTY() AS submit_to_job_countNum,
               BITMAP_EMPTY() AS submit_to_job_aiRecommendCountNum,
               BITMAP_EMPTY() AS submit_to_job_precisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_stayedOver,
               BITMAP_EMPTY() AS submit_to_client_countNum,
               BITMAP_EMPTY() AS submit_to_client_aiRecommendCountNum,
               BITMAP_EMPTY() AS submit_to_client_precisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_stayedOver,

               interview1,

               interview2,

               two_or_more_interviews,

               interview_final,
               interview_total,
               unique_interview_talents,

               interview_total_process,

               interviewTotalAiRecommendNum,

               interviewTotalProcessAiRecommendNum,

               interview1AiRecommendNum,

               interview2AiRecommendNum,

               twoOrMoreInterviewsAiRecommendNum,

               interviewFinalAiRecommendNum,

               interviewTotalPrecisionAiRecommendNum,

               interviewNumProcessPrecisionAIRecommend,

               interview1PrecisionAiRecommendNum,

               interview2PrecisionAiRecommendNum,

               twoOrMoreInterviewsPrecisionAiRecommendNum,

               interviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS reserve_interview_total,
               BITMAP_EMPTY() AS reserve_interview_aiRecommendCountNum,
               BITMAP_EMPTY() AS reserve_interview_precisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_countNum,
               BITMAP_EMPTY() AS offer_aiRecommendCountNum,
               BITMAP_EMPTY() AS offer_precisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_countNum,
               BITMAP_EMPTY() AS offer_accept_aiRecommendCountNum,
               BITMAP_EMPTY() AS offer_accept_precisionAiRecommendNum,
               BITMAP_EMPTY() AS onboard_countNum,
               BITMAP_EMPTY() AS onboard_aiRecommendCountNum,
               BITMAP_EMPTY() AS onboard_precisionAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_countNum,
               BITMAP_EMPTY() AS eliminate_aiRecommendCountNum,
               BITMAP_EMPTY() AS eliminate_precisionAiRecommendNum
        FROM mv_application_interview)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               BITMAP_EMPTY() AS submit_to_job_countNum,
               BITMAP_EMPTY() AS submit_to_job_aiRecommendCountNum,
               BITMAP_EMPTY() AS submit_to_job_precisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_stayedOver,
               BITMAP_EMPTY() AS submit_to_client_countNum,
               BITMAP_EMPTY() AS submit_to_client_aiRecommendCountNum,
               BITMAP_EMPTY() AS submit_to_client_precisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_stayedOver,
               BITMAP_EMPTY() AS interview1,
               BITMAP_EMPTY() AS interview2,
               BITMAP_EMPTY() AS two_or_more_interviews,
               BITMAP_EMPTY() AS interview_final,
               BITMAP_EMPTY() AS interview_total,
               BITMAP_EMPTY() AS unique_interview_talents,
               BITMAP_EMPTY() AS interview_total_process,
               BITMAP_EMPTY() AS interviewTotalAiRecommendNum,
               BITMAP_EMPTY() AS interviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY() AS interview1AiRecommendNum,
               BITMAP_EMPTY() AS interview2AiRecommendNum,
               BITMAP_EMPTY() AS twoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY() AS interviewFinalAiRecommendNum,
               BITMAP_EMPTY() AS interviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY() AS interview1PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interview2PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS twoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interviewFinalPrecisionAiRecommendNum,
               reserve_interview_total,
               reserve_interview_aiRecommendCountNum,
               reserve_interview_precisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_countNum,
               BITMAP_EMPTY() AS offer_aiRecommendCountNum,
               BITMAP_EMPTY() AS offer_precisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_countNum,
               BITMAP_EMPTY() AS offer_accept_aiRecommendCountNum,
               BITMAP_EMPTY() AS offer_accept_precisionAiRecommendNum,
               BITMAP_EMPTY() AS onboard_countNum,
               BITMAP_EMPTY() AS onboard_aiRecommendCountNum,
               BITMAP_EMPTY() AS onboard_precisionAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_countNum,
               BITMAP_EMPTY() AS eliminate_aiRecommendCountNum,
               BITMAP_EMPTY() AS eliminate_precisionAiRecommendNum
        FROM mv_application_reserve_interview)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               BITMAP_EMPTY() AS submit_to_job_countNum,
               BITMAP_EMPTY() AS submit_to_job_aiRecommendCountNum,
               BITMAP_EMPTY() AS submit_to_job_precisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_stayedOver,
               BITMAP_EMPTY() AS submit_to_client_countNum,
               BITMAP_EMPTY() AS submit_to_client_aiRecommendCountNum,
               BITMAP_EMPTY() AS submit_to_client_precisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_stayedOver,
               BITMAP_EMPTY() AS interview1,
               BITMAP_EMPTY() AS interview2,
               BITMAP_EMPTY() AS two_or_more_interviews,
               BITMAP_EMPTY() AS interview_final,
               BITMAP_EMPTY() AS interview_total,
               BITMAP_EMPTY() AS unique_interview_talents,
               BITMAP_EMPTY() AS interview_total_process,
               BITMAP_EMPTY() AS interviewTotalAiRecommendNum,
               BITMAP_EMPTY() AS interviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY() AS interview1AiRecommendNum,
               BITMAP_EMPTY() AS interview2AiRecommendNum,
               BITMAP_EMPTY() AS twoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY() AS interviewFinalAiRecommendNum,
               BITMAP_EMPTY() AS interviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY() AS interview1PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interview2PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS twoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS reserve_interview_total,
               BITMAP_EMPTY() AS reserve_interview_aiRecommendCountNum,
               BITMAP_EMPTY() AS reserve_interview_precisionAiRecommendNum,
               offer_countNum,
               offer_aiRecommendCountNum,
               offer_precisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_countNum,
               BITMAP_EMPTY() AS offer_accept_aiRecommendCountNum,
               BITMAP_EMPTY() AS offer_accept_precisionAiRecommendNum,
               BITMAP_EMPTY() AS onboard_countNum,
               BITMAP_EMPTY() AS onboard_aiRecommendCountNum,
               BITMAP_EMPTY() AS onboard_precisionAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_countNum,
               BITMAP_EMPTY() AS eliminate_aiRecommendCountNum,
               BITMAP_EMPTY() AS eliminate_precisionAiRecommendNum
        FROM mv_application_offer)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               BITMAP_EMPTY() AS submit_to_job_countNum,
               BITMAP_EMPTY() AS submit_to_job_aiRecommendCountNum,
               BITMAP_EMPTY() AS submit_to_job_precisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_stayedOver,
               BITMAP_EMPTY() AS submit_to_client_countNum,
               BITMAP_EMPTY() AS submit_to_client_aiRecommendCountNum,
               BITMAP_EMPTY() AS submit_to_client_precisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_stayedOver,
               BITMAP_EMPTY() AS interview1,
               BITMAP_EMPTY() AS interview2,
               BITMAP_EMPTY() AS two_or_more_interviews,
               BITMAP_EMPTY() AS interview_final,
               BITMAP_EMPTY() AS interview_total,
               BITMAP_EMPTY() AS unique_interview_talents,
               BITMAP_EMPTY() AS interview_total_process,
               BITMAP_EMPTY() AS interviewTotalAiRecommendNum,
               BITMAP_EMPTY() AS interviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY() AS interview1AiRecommendNum,
               BITMAP_EMPTY() AS interview2AiRecommendNum,
               BITMAP_EMPTY() AS twoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY() AS interviewFinalAiRecommendNum,
               BITMAP_EMPTY() AS interviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY() AS interview1PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interview2PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS twoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS reserve_interview_total,
               BITMAP_EMPTY() AS reserve_interview_aiRecommendCountNum,
               BITMAP_EMPTY() AS reserve_interview_precisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_countNum,
               BITMAP_EMPTY() AS offer_aiRecommendCountNum,
               BITMAP_EMPTY() AS offer_precisionAiRecommendNum,
               offer_accept_countNum,
               offer_accept_aiRecommendCountNum,
               offer_accept_precisionAiRecommendNum,
               BITMAP_EMPTY() AS onboard_countNum,
               BITMAP_EMPTY() AS onboard_aiRecommendCountNum,
               BITMAP_EMPTY() AS onboard_precisionAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_countNum,
               BITMAP_EMPTY() AS eliminate_aiRecommendCountNum,
               BITMAP_EMPTY() AS eliminate_precisionAiRecommendNum
        FROM mv_application_offer_accept)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               BITMAP_EMPTY() AS submit_to_job_countNum,
               BITMAP_EMPTY() AS submit_to_job_aiRecommendCountNum,
               BITMAP_EMPTY() AS submit_to_job_precisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_stayedOver,
               BITMAP_EMPTY() AS submit_to_client_countNum,
               BITMAP_EMPTY() AS submit_to_client_aiRecommendCountNum,
               BITMAP_EMPTY() AS submit_to_client_precisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_stayedOver,
               BITMAP_EMPTY() AS interview1,
               BITMAP_EMPTY() AS interview2,
               BITMAP_EMPTY() AS two_or_more_interviews,
               BITMAP_EMPTY() AS interview_final,
               BITMAP_EMPTY() AS interview_total,
               BITMAP_EMPTY() AS unique_interview_talents,
               BITMAP_EMPTY() AS interview_total_process,
               BITMAP_EMPTY() AS interviewTotalAiRecommendNum,
               BITMAP_EMPTY() AS interviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY() AS interview1AiRecommendNum,
               BITMAP_EMPTY() AS interview2AiRecommendNum,
               BITMAP_EMPTY() AS twoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY() AS interviewFinalAiRecommendNum,
               BITMAP_EMPTY() AS interviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY() AS interview1PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interview2PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS twoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS reserve_interview_total,
               BITMAP_EMPTY() AS reserve_interview_aiRecommendCountNum,
               BITMAP_EMPTY() AS reserve_interview_precisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_countNum,
               BITMAP_EMPTY() AS offer_aiRecommendCountNum,
               BITMAP_EMPTY() AS offer_precisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_countNum,
               BITMAP_EMPTY() AS offer_accept_aiRecommendCountNum,
               BITMAP_EMPTY() AS offer_accept_precisionAiRecommendNum,
               onboard_countNum,
               onboard_aiRecommendCountNum,
               onboard_precisionAiRecommendNum,
               BITMAP_EMPTY() AS eliminate_countNum,
               BITMAP_EMPTY() AS eliminate_aiRecommendCountNum,
               BITMAP_EMPTY() AS eliminate_precisionAiRecommendNum
        FROM mv_application_onboard)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date       AS add_date,
               event_date     AS event_date,
               user_roles,
               BITMAP_EMPTY() AS submit_to_job_countNum,
               BITMAP_EMPTY() AS submit_to_job_aiRecommendCountNum,
               BITMAP_EMPTY() AS submit_to_job_precisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_job_stayedOver,
               BITMAP_EMPTY() AS submit_to_client_countNum,
               BITMAP_EMPTY() AS submit_to_client_aiRecommendCountNum,
               BITMAP_EMPTY() AS submit_to_client_precisionAiRecommendNum,
               BITMAP_EMPTY() AS submit_to_client_stayedOver,
               BITMAP_EMPTY() AS interview1,
               BITMAP_EMPTY() AS interview2,
               BITMAP_EMPTY() AS two_or_more_interviews,
               BITMAP_EMPTY() AS interview_final,
               BITMAP_EMPTY() AS interview_total,
               BITMAP_EMPTY() AS unique_interview_talents,
               BITMAP_EMPTY() AS interview_total_process,
               BITMAP_EMPTY() AS interviewTotalAiRecommendNum,
               BITMAP_EMPTY() AS interviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY() AS interview1AiRecommendNum,
               BITMAP_EMPTY() AS interview2AiRecommendNum,
               BITMAP_EMPTY() AS twoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY() AS interviewFinalAiRecommendNum,
               BITMAP_EMPTY() AS interviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY() AS interview1PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interview2PrecisionAiRecommendNum,
               BITMAP_EMPTY() AS twoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS interviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY() AS reserve_interview_total,
               BITMAP_EMPTY() AS reserve_interview_aiRecommendCountNum,
               BITMAP_EMPTY() AS reserve_interview_precisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_countNum,
               BITMAP_EMPTY() AS offer_aiRecommendCountNum,
               BITMAP_EMPTY() AS offer_precisionAiRecommendNum,
               BITMAP_EMPTY() AS offer_accept_countNum,
               BITMAP_EMPTY() AS offer_accept_aiRecommendCountNum,
               BITMAP_EMPTY() AS offer_accept_precisionAiRecommendNum,
               BITMAP_EMPTY() AS onboard_countNum,
               BITMAP_EMPTY() AS onboard_aiRecommendCountNum,
               BITMAP_EMPTY() AS onboard_precisionAiRecommendNum,
               eliminate_countNum,

               eliminate_aiRecommendCountNum,

               eliminate_precisionAiRecommendNum
        FROM mv_application_emiminate))) AS base
         INNER JOIN ods_apn.mv_team_hierarchy AS hierarchy ON base.team_id = hierarchy.child_team_id
GROUP BY base.tenant_id,
         base.company_id,
         base.job_id,
         base.job_pteam_id,
         hierarchy.parent_team_id,
         hierarchy.parent_team_name,
         hierarchy.parent_team_parent_id,
         hierarchy.parent_team_level,
         hierarchy.parent_team_is_leaf,
         base.user_id,
         base.user_name,
         base.user_activated,
         base.add_date,
         base.event_date;