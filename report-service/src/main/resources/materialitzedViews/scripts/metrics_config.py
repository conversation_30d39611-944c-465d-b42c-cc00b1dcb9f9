from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field


@dataclass
class MetricConfig:
    """指标配置"""

    sub_query: str  # 所属子查询名称
    expression: str  # SQL 表达式
    alias: str  # 别名
    default_value: str = "BITMAP_EMPTY()"  # 默认值（在不相关子查询中使用）


def get_all_merged_metrics_configs(is_current: bool = False) -> List[MetricConfig]:
    """获取所有合并后的指标配置"""
    metrics = [
        *submit_to_job_metrics_config(is_current),
        *submit_to_client_metrics_config(is_current),
        *interview_config(is_current),
        *reserve_interview_config(is_current),
        *offer_metrics_config(is_current),
        *offer_accepted_metrics_config(is_current),
        *onboard_metrics_config(is_current),
        *eliminated_metrics_config(is_current),
    ]
    return metrics


node_type_mapping = {
    "submit_to_job": 10,
    "submit_to_client": 20,
    "interview": 30,
    "reserve_interview": 30,
    "offer": 40,
    "offer_accept": 41,
    "onboard": 60
}


# 漏斗状态数量
def common_count_num_metric(column: str, sub_query: str) -> MetricConfig:
    return MetricConfig(
        sub_query=sub_query,
        expression=f"BITMAP_AGG(application.node_id)",
        alias=f"{sub_query}_countNum",
    )


# 当前状态数量
def common_current_count_num_metric(column: str, sub_query: str) -> MetricConfig:
    return MetricConfig(
        sub_query=sub_query,
        expression="BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL))",
        alias=f"{sub_query}_current_countNum",
    )


# 漏斗状态 AI 推荐数量
def common_ai_recommend_count_metric(sub_query: str) -> MetricConfig:
    return MetricConfig(
        sub_query=sub_query,
        expression="BITMAP_AGG(IF((ai_score IS NOT NULL), application.talent_recruitment_process_id, NULL))",
        alias=f"{sub_query}_aiRecommendCountNum",
    )


# 当前状态 AI 推荐数量
def common_ai_recommend_current_count_metric(sub_query: str) -> MetricConfig:
    return MetricConfig(
        sub_query=sub_query,
        expression="BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1), application.talent_recruitment_process_id, NULL))",
        alias=f"{sub_query}_currentAiRecommendNum",
    )


# 漏斗状态 AI 精准推荐数量
def common_precision_ai_recommend_count_metric(sub_query: str) -> MetricConfig:
    return MetricConfig(
        sub_query=sub_query,
        expression="BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL), application.talent_recruitment_process_id, NULL))",
        alias=f"{sub_query}_precisionAiRecommendNum",
    )


# 当前状态 AI 精准推荐数量
def common_precision_ai_recommend_current_count_metric(sub_query: str) -> MetricConfig:
    return MetricConfig(
        sub_query=sub_query,
        expression="BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1), application.talent_recruitment_process_id, NULL))",
        alias=f"{sub_query}_currentPrecisionAiRecommendNum",
    )


def common_metrics_config(column: str, sub_query: str) -> List[MetricConfig]:
    metrics = [
        common_count_num_metric(column, sub_query),
        common_current_count_num_metric(column, sub_query),
        common_ai_recommend_count_metric(sub_query),
        common_ai_recommend_current_count_metric(sub_query),
        common_precision_ai_recommend_count_metric(sub_query),
        common_precision_ai_recommend_current_count_metric(sub_query),
    ]
    return metrics


def common_metrics_config_with_state(column: str, sub_query: str, is_current: bool) -> List[MetricConfig]:
    if is_current:
        metrics = [
            common_current_count_num_metric(column, sub_query),
            common_ai_recommend_current_count_metric(sub_query),
            common_precision_ai_recommend_current_count_metric(sub_query),
        ]
        return metrics
    else:
        metrics = [
            common_count_num_metric(column, sub_query),
            common_ai_recommend_count_metric(sub_query),
            common_precision_ai_recommend_count_metric(sub_query),
        ]
        return metrics


def submit_to_job_metrics_config(is_current: bool = False) -> List[MetricConfig]:
    if is_current:
        metrics = [
            *common_metrics_config_with_state("submit_job_id", "submit_to_job", is_current),
            MetricConfig(
                sub_query="submit_to_job",
                expression="""BITMAP_AGG(IF(((`application`.`node_status` = 1) AND (`application`.`node_type` = 10)) AND
                      ((TIMESTAMPDIFF(HOUR, `application`.add_date, now())) > 24), 1, NULL))
                      """,
                alias="submit_to_job_currentStayedOver",
            ),
        ]
        return metrics
    else:
        metrics = [
            *common_metrics_config_with_state("submit_job_id", "submit_to_job", is_current),
            MetricConfig(
                sub_query="submit_to_job",
                expression="""
                BITMAP_AGG(
                CASE
                    WHEN pn.stayed_add_date IS NOT NULL THEN
                        IF(TIMESTAMPDIFF(HOUR, application.add_date, pn.stayed_add_date) > 24, 1, NULL)
                    WHEN application.node_status = 1 THEN
                        IF(TIMESTAMPDIFF(HOUR, application.add_date, NOW()) > 24, 1, NULL)
                    END
                )
                """,
                alias="submit_to_job_stayedOver",
            ),
        ]
        return metrics


def submit_to_client_metrics_config(is_current: bool = False) -> List[MetricConfig]:
    if is_current:
        metrics = [
            *common_metrics_config_with_state("submit_client_id", "submit_to_client", is_current),
            MetricConfig(
                sub_query="submit_to_client",
                expression="""BITMAP_AGG(IF(((`application`.`node_status` = 1) AND (`application`.`node_type` = 20)) AND
                      ((TIMESTAMPDIFF(HOUR, `application`.add_date, now())) > 72), 1, NULL))
                      """,
                alias="submit_to_client_currentStayedOver",
            ),
        ]
        return metrics
    else:
        metrics = [
            *common_metrics_config_with_state("submit_client_id", "submit_to_client", is_current),
            MetricConfig(
                sub_query="submit_to_client",
                expression="""
                BITMAP_AGG(
                CASE
                    WHEN pn.stayed_add_date IS NOT NULL THEN
                        IF(TIMESTAMPDIFF(HOUR, application.add_date, pn.stayed_add_date) > 72, 1, NULL)
                    WHEN application.node_status = 1 THEN
                        IF(TIMESTAMPDIFF(HOUR, application.add_date, NOW()) > 72, 1, NULL)
                    END
                )
                """,
                alias="submit_to_client_stayedOver",
            ),
        ]
        return metrics


def interview_config(is_current: bool = False) -> List[MetricConfig]:
    if is_current:
        # 当前状态指标
        metrics = [
            # 当前状态一面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN (
                        application.progress = 1
                            AND node_status = 1
                            AND node_type = 30
                            AND max_progress_subquery.progress = 1
                        ) THEN node_id
                END)""",
                alias="current_interview1",
            ),
            # 当前状态二面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN (
                        application.progress = 2
                            AND node_status = 1
                            AND node_type = 30
                            AND max_progress_subquery.progress = 2
                        ) THEN node_id
                END)""",
                alias="current_interview2",
            ),
            # 当前状态二面及以上数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN (
                        application.progress >= 2
                            AND final_round <> 1
                            AND node_status = 1
                            AND node_type = 30
                        ) THEN node_id
                END)""",
                alias="current_two_or_more_interviews",
            ),
            # 当前状态终面数量
            MetricConfig(
                sub_query="interview",
                expression="""BITMAP_AGG( CASE
                                           WHEN (
                                               final_round = 1
                                                   AND node_status = 1
                                                   AND node_type = 30
                                               ) THEN node_id
                          END)""",
                alias="current_interview_final",
            ),
            # 当前状态面试总数
            MetricConfig(
                sub_query="interview",
                expression="""BITMAP_AGG( CASE
                                           WHEN (
                                               node_status = 1
                                                   AND node_type = 30
                                               ) THEN node_id
                          END)""",
                alias="current_interview_total",
            ),
            # 当前状态面试总流程数（AI 推荐计算）
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                    WHEN (
                        node_status = 1
                            ) THEN talent_recruitment_process_id
                END)
                """,
                alias="current_interview_total_process",
            ),
            # 当前状态AI推荐面试数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN (
                        ai_score IS NOT NULL
                        AND node_status = 1
                        AND node_type = 30
                    ) THEN node_id
                END)""",
                alias="currentInterviewTotalAiRecommendNum",
            ),
            # 当前状态面试进程数（AI 推荐计算）
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                    WHEN (
                        ai_score IS NOT NULL
                        AND node_status = 1
                    ) THEN talent_recruitment_process_id
                END)
                """,
                alias="currentInterviewTotalProcessAiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
            # 当前状态AI推荐一面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                      WHEN (
                          application.progress = 1
                              AND node_status = 1
                              AND max_progress_subquery.progress = 1
                              AND ai_score IS NOT NULL
                      ) THEN talent_recruitment_process_id
                END)
                """,
                alias="currentInterview1AiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
            # 当前状态AI推荐二面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN (
                        application.progress = 2
                            AND node_status = 1
                            AND max_progress_subquery.progress = 2
                            AND ai_score IS NOT NULL
                        ) THEN talent_recruitment_process_id
                END)
                """,
                alias="currentInterview2AiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
            # 当前状态AI推荐二面及以上数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                      WHEN (
                          application.progress >= 2
                              AND final_round <> 1
                              AND node_status = 1
                              AND node_type = 30
                              AND ai_score IS NOT NULL
                          ) THEN node_id
                END)""",
                alias="currentTwoOrMoreInterviewsAiRecommendNum",
            ),
            # 当前状态AI推荐终面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                      WHEN (
                          final_round = 1
                              AND node_status = 1
                              AND ai_score IS NOT NULL
                          ) THEN talent_recruitment_process_id
                END)
                """,
                alias="currentInterviewFinalAiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
            # 当前状态精准AI推荐面试总数
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN (
                        recommend_feedback_id IS NOT NULL
                        AND node_status = 1
                        AND node_type = 30
                    ) THEN node_id
                END)""",
                alias="currentInterviewTotalPrecisionAiRecommendNum",
            ),
            # 当前状态精准AI推荐面试流程数
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                    WHEN (
                        recommend_feedback_id IS NOT NULL
                        AND node_status = 1
                    ) THEN talent_recruitment_process_id
                END)
                """,
                alias="currentInterviewNumProcessPrecisionAIRecommend",
                default_value="BITMAP_EMPTY()",
            ),
            # 当前状态精准AI推荐一面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                    WHEN (
                        application.progress = 1
                        AND max_progress_subquery.progress = 1
                        AND node_status = 1
                        AND recommend_feedback_id IS NOT NULL
                    ) THEN talent_recruitment_process_id
                END)
                """,
                alias="currentInterview1PrecisionAiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
            # 当前状态精准AI推荐二面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                    WHEN (
                        application.progress = 2
                        AND max_progress_subquery.progress = 2
                        AND node_status = 1
                        AND recommend_feedback_id IS NOT NULL
                    ) THEN talent_recruitment_process_id
                END)
                """,
                alias="currentInterview2PrecisionAiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
            # 当前状态精准AI推荐二面及以上数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN (
                        application.progress >= 2
                        AND final_round <> 1
                        AND node_status = 1
                        AND node_type = 30
                        AND recommend_feedback_id IS NOT NULL
                    ) THEN node_id
                END)""",
                alias="currentTwoOrMoreInterviewsPrecisionAiRecommendNum",
            ),
            # 当前状态精准AI推荐终面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                    WHEN (
                        application.final_round = 1
                        AND node_status = 1
                        AND recommend_feedback_id IS NOT NULL
                    ) THEN talent_recruitment_process_id
                END)
                """,
                alias="currentInterviewFinalPrecisionAiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
        ]
    else:
        # 漏斗状态指标
        metrics = [
            # 漏斗状态一面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN application.progress = 1  AND node_type = 30
                    THEN node_id
                END)""",
                alias="interview1",
            ),
            # 漏斗状态二面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN application.progress = 2
                        AND node_type = 30
                    THEN node_id
                END)""",
                alias="interview2",
            ),
            # 漏斗状态二面及以上数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN (
                        application.progress >= 2
                            AND node_type = 30
                            AND final_round <> 1
                        ) THEN node_id
                END)""",
                alias="two_or_more_interviews",
            ),
            # 漏斗状态终面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN
                        final_round = 1
                        AND node_type = 30
                     THEN node_id
                END)""",
                alias="interview_final",
            ),
            # 漏斗状态面试总数
            MetricConfig(
                sub_query="interview",
                expression="BITMAP_AGG( node_id)",
                alias="interview_total",
            ),
            # 漏斗状态面试人数
            MetricConfig(
                sub_query="interview",
                expression="BITMAP_AGG(talent_id)",
                alias="unique_interview_talents",
            ),
            # 漏斗状态面试总流程数（AI 推荐计算）
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(talent_recruitment_process_id)
                """,
                alias="interview_total_process",
            ),
            # 漏斗状态AI推荐面试数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN (
                        ai_score IS NOT NULL
                        AND node_type = 30
                    ) THEN node_id
                END)""",
                alias="interviewTotalAiRecommendNum",
            ),
            # 漏斗状态面试进程数（AI 推荐计算）
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                    WHEN (
                        ai_score IS NOT NULL
                    ) THEN talent_recruitment_process_id
                END)
                """,
                alias="interviewTotalProcessAiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
            # 漏斗状态AI推荐一面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                    WHEN
                      application.progress = 1
                      AND ai_score IS NOT NULL
                     THEN talent_recruitment_process_id
                END)
                """,
                alias="interview1AiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
            # 漏斗状态AI推荐二面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                    WHEN
                        application.progress = 2
                        AND ai_score IS NOT NULL
                    THEN talent_recruitment_process_id
                END)
                """,
                alias="interview2AiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
            # 漏斗状态AI推荐二面及以上数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN (
                        application.progress >= 2
                            AND final_round <> 1
                            AND node_type = 30
                            AND ai_score IS NOT NULL
                        ) THEN node_id
                END)""",
                alias="twoOrMoreInterviewsAiRecommendNum",
            ),
            # 漏斗状态AI推荐终面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                    WHEN final_round = 1 AND ai_score IS NOT NULL
                    THEN talent_recruitment_process_id
                END)
                """,
                alias="interviewFinalAiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
            # 漏斗状态精准AI推荐面试总数
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN recommend_feedback_id IS NOT NULL AND node_type = 30 THEN node_id
                END)""",
                alias="interviewTotalPrecisionAiRecommendNum",
            ),
            # 漏斗状态精准AI推荐面试流程数
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                    WHEN recommend_feedback_id IS NOT NULL THEN talent_recruitment_process_id
                END)
                """,
                alias="interviewNumProcessPrecisionAIRecommend",
                default_value="BITMAP_EMPTY()",
            ),
            # 漏斗状态精准AI推荐一面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                    WHEN (
                        application.progress = 1
                        AND recommend_feedback_id IS NOT NULL
                    ) THEN talent_recruitment_process_id
                END)
                """,
                alias="interview1PrecisionAiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
            # 漏斗状态精准AI推荐二面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                    WHEN (
                        application.progress = 2
                        AND recommend_feedback_id IS NOT NULL
                    ) THEN talent_recruitment_process_id
                END)
                """,
                alias="interview2PrecisionAiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
            # 漏斗状态精准AI推荐二面及以上数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG( CASE
                    WHEN (
                        application.progress >= 2
                        AND final_round <> 1
                        AND node_type = 30
                        AND recommend_feedback_id IS NOT NULL
                    ) THEN node_id
                END)""",
                alias="twoOrMoreInterviewsPrecisionAiRecommendNum",
            ),
            # 漏斗状态精准AI推荐终面数量
            MetricConfig(
                sub_query="interview",
                expression="""
                BITMAP_AGG(CASE
                    WHEN (
                        final_round = 1
                        AND recommend_feedback_id IS NOT NULL
                    ) THEN talent_recruitment_process_id
                END)
                """,
                alias="interviewFinalPrecisionAiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
        ]
    return metrics


def reserve_interview_config(is_current: bool = False) -> List[MetricConfig]:
    sub_query = "reserve_interview"
    if is_current:
        metrics = [
            # 当前状态预约面试数量
            MetricConfig(
                sub_query=sub_query,
                expression="BITMAP_AGG( IF(node_status = 1, node_id, NULL))",
                alias="reserve_current_interview_total",
                default_value="BITMAP_EMPTY()",
            ),
            MetricConfig(
                sub_query=sub_query,
                expression="BITMAP_AGG( IF((ai_score IS NOT NULL AND application.node_status = 1), application.node_id, NULL))",
                alias=f"{sub_query}_currentAiRecommendNum",
                default_value="BITMAP_EMPTY()",
            ),
            MetricConfig(
                sub_query=sub_query,
                expression="BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1), application.node_id, NULL))",
                alias=f"{sub_query}_currentPrecisionAiRecommendNum",
                default_value="BITMAP_EMPTY()",
            )
        ]
    else:
        metrics = [
            # 漏斗状态预约面试数量
            MetricConfig(
                sub_query=sub_query,
                expression="BITMAP_AGG(node_id)",
                alias="reserve_interview_total",
                default_value="BITMAP_EMPTY()",
            ),
            MetricConfig(
                sub_query=sub_query,
                expression="BITMAP_AGG( IF((ai_score IS NOT NULL), application.node_id, NULL))",
                alias="reserve_interview_aiRecommendCountNum",
                default_value="BITMAP_EMPTY()",
            ),
            MetricConfig(
                sub_query=sub_query,
                expression="BITMAP_AGG( IF((recommend_feedback_id IS NOT NULL), application.node_id, NULL))",
                alias=f"{sub_query}_precisionAiRecommendNum",
                default_value="BITMAP_EMPTY()",
            )
        ]
    return metrics


def offer_metrics_config(is_current: bool = False) -> List[MetricConfig]:
    return common_metrics_config_with_state("offer_id", "offer", is_current)


def offer_accepted_metrics_config(is_current: bool = False) -> List[MetricConfig]:
    return common_metrics_config_with_state("offer_accept_id", "offer_accept", is_current)


def onboard_metrics_config(is_current: bool = False) -> List[MetricConfig]:
    return common_metrics_config_with_state("onboard_id", "onboard", is_current)


def eliminated_metrics_config(is_current: bool = False) -> List[MetricConfig]:
    column = "node_id"
    sub_query = "eliminate"
    # 淘汰节点比较特殊，当前状态和漏斗状态的数据是一样的
    if is_current:
        metrics = [
            MetricConfig(
                sub_query="eliminate",
                expression=f"BITMAP_AGG( {column})",
                alias=f"eliminate_current_countNum",
            ),
            MetricConfig(
                sub_query=sub_query,
                expression=f"""
                    BITMAP_AGG( CASE
                        WHEN (
                            ai_score IS NOT NULL
                            AND {column} IS NOT NULL
                        ) THEN talent_recruitment_process_id
                    END)""",
                alias=f"{sub_query}_currentAiRecommendNum",
            ),
            MetricConfig(
                sub_query=sub_query,
                expression=f"""
                BITMAP_AGG( CASE
                    WHEN (
                        recommend_feedback_id IS NOT NULL
                        AND {column} IS NOT NULL
                    ) THEN talent_recruitment_process_id
                END)""",
                alias=f"{sub_query}_currentPrecisionAiRecommendNum",
            ),
        ]
    else:
        metrics = [
            common_count_num_metric("eliminate_id", "eliminate"),
            MetricConfig(
                sub_query=sub_query,
                expression=f"""
                    BITMAP_AGG( CASE
                        WHEN (
                            ai_score IS NOT NULL
                            AND {column} IS NOT NULL
                        ) THEN talent_recruitment_process_id
                    END)""",
                alias=f"{sub_query}_aiRecommendCountNum"
            ),
            MetricConfig(
                sub_query=sub_query,
                expression=f"""
                BITMAP_AGG( CASE
                    WHEN (
                        recommend_feedback_id IS NOT NULL
                        AND {column} IS NOT NULL
                    ) THEN talent_recruitment_process_id
                END)""",
                alias=f"{sub_query}_precisionAiRecommendNum",
            ),
        ]
    return metrics
