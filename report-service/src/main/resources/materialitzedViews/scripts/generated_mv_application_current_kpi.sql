CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.mv_application_current_kpi
       DISTRIBUTED BY HASH (`user_id`)
REFRESH
       ASYNC EVERY (INTERVAL 10 MINUTE)
ORDER BY (add_date, event_date)
AS
SELECT * 

FROM ((
          (SELECT tenant_id,company_id,job_id,job_pteam_id,team_id,team_name,user_id,user_name,user_activated,
                 add_date AS add_date,
                 event_date AS event_date,
                 BITMAP_AGG(user_role) AS user_roles,
                 BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL)) AS submit_to_job_current_countNum,
                 BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1), application.talent_recruitment_process_id, NULL)) AS submit_to_job_currentAiRecommendNum,
                 BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1), application.talent_recruitment_process_id, NULL)) AS submit_to_job_currentPrecisionAiRecommendNum,
                 BITMAP_AGG(IF(((`application`.`node_status` = 1) AND (`application`.`node_type` = 10)) AND
                                ((TIMESTAMPDIFF(HOUR, `application`.add_date, now())) > 24), 1, NULL))
                                 AS submit_to_job_currentStayedOver,
                 BITMAP_EMPTY() AS submit_to_client_current_countNum,
                 BITMAP_EMPTY() AS submit_to_client_currentAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_client_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_client_currentStayedOver,
                 BITMAP_EMPTY() AS current_interview1,
                 BITMAP_EMPTY() AS current_interview2,
                 BITMAP_EMPTY() AS current_two_or_more_interviews,
                 BITMAP_EMPTY() AS current_interview_final,
                 BITMAP_EMPTY() AS current_interview_total,
                 BITMAP_EMPTY() AS current_interview_total_process,
                 BITMAP_EMPTY() AS currentInterviewTotalAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewTotalProcessAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview1AiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview2AiRecommendNum,
                 BITMAP_EMPTY() AS currentTwoOrMoreInterviewsAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewFinalAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewTotalPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewNumProcessPrecisionAIRecommend,
                 BITMAP_EMPTY() AS currentInterview1PrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview2PrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewFinalPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS reserve_current_interview_total,
                 BITMAP_EMPTY() AS reserve_interview_currentAiRecommendNum,
                 BITMAP_EMPTY() AS reserve_interview_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS offer_current_countNum,
                 BITMAP_EMPTY() AS offer_currentAiRecommendNum,
                 BITMAP_EMPTY() AS offer_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS offer_accept_current_countNum,
                 BITMAP_EMPTY() AS offer_accept_currentAiRecommendNum,
                 BITMAP_EMPTY() AS offer_accept_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS onboard_current_countNum,
                 BITMAP_EMPTY() AS onboard_currentAiRecommendNum,
                 BITMAP_EMPTY() AS onboard_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS eliminate_current_countNum,
                 BITMAP_EMPTY() AS eliminate_currentAiRecommendNum,
                 BITMAP_EMPTY() AS eliminate_currentPrecisionAiRecommendNum
           FROM view_application_v2 AS application

                          left join (
                              select  pn.talent_recruitment_process_id,
                                      IF(MIN(pn.add_date) IS NOT NULL, MIN(pn.add_date), MAX(eli.add_date)) as stayed_add_date
                              from mv_application_fact pn
                                  left join mv_application_fact eli on eli.talent_recruitment_process_id = pn.talent_recruitment_process_id and eli.node_type=-1
                              where pn.node_type > 10  group by pn.talent_recruitment_process_id
                          ) pn on pn.talent_recruitment_process_id = application.talent_recruitment_process_id

           WHERE application.node_type = 10
           GROUP BY tenant_id,          company_id,          job_id,          job_pteam_id,          team_id,          team_name,          user_id,          user_name,          user_activated,          add_date,          event_date
          )
      UNION ALL
          (SELECT tenant_id,company_id,job_id,job_pteam_id,team_id,team_name,user_id,user_name,user_activated,
                 add_date AS add_date,
                 event_date AS event_date,
                 BITMAP_AGG(user_role) AS user_roles,
                 BITMAP_EMPTY() AS submit_to_job_current_countNum,
                 BITMAP_EMPTY() AS submit_to_job_currentAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_job_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_job_currentStayedOver,
                 BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL)) AS submit_to_client_current_countNum,
                 BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1), application.talent_recruitment_process_id, NULL)) AS submit_to_client_currentAiRecommendNum,
                 BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1), application.talent_recruitment_process_id, NULL)) AS submit_to_client_currentPrecisionAiRecommendNum,
                 BITMAP_AGG(IF(((`application`.`node_status` = 1) AND (`application`.`node_type` = 20)) AND
                                ((TIMESTAMPDIFF(HOUR, `application`.add_date, now())) > 72), 1, NULL))
                                 AS submit_to_client_currentStayedOver,
                 BITMAP_EMPTY() AS current_interview1,
                 BITMAP_EMPTY() AS current_interview2,
                 BITMAP_EMPTY() AS current_two_or_more_interviews,
                 BITMAP_EMPTY() AS current_interview_final,
                 BITMAP_EMPTY() AS current_interview_total,
                 BITMAP_EMPTY() AS current_interview_total_process,
                 BITMAP_EMPTY() AS currentInterviewTotalAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewTotalProcessAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview1AiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview2AiRecommendNum,
                 BITMAP_EMPTY() AS currentTwoOrMoreInterviewsAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewFinalAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewTotalPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewNumProcessPrecisionAIRecommend,
                 BITMAP_EMPTY() AS currentInterview1PrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview2PrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewFinalPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS reserve_current_interview_total,
                 BITMAP_EMPTY() AS reserve_interview_currentAiRecommendNum,
                 BITMAP_EMPTY() AS reserve_interview_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS offer_current_countNum,
                 BITMAP_EMPTY() AS offer_currentAiRecommendNum,
                 BITMAP_EMPTY() AS offer_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS offer_accept_current_countNum,
                 BITMAP_EMPTY() AS offer_accept_currentAiRecommendNum,
                 BITMAP_EMPTY() AS offer_accept_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS onboard_current_countNum,
                 BITMAP_EMPTY() AS onboard_currentAiRecommendNum,
                 BITMAP_EMPTY() AS onboard_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS eliminate_current_countNum,
                 BITMAP_EMPTY() AS eliminate_currentAiRecommendNum,
                 BITMAP_EMPTY() AS eliminate_currentPrecisionAiRecommendNum
           FROM view_application_v2 AS application

                          left join (
                              select  pn.talent_recruitment_process_id,
                                      IF(MIN(pn.add_date) IS NOT NULL, MIN(pn.add_date), MAX(eli.add_date)) as stayed_add_date
                              from mv_application_fact pn
                                  left join mv_application_fact eli on eli.talent_recruitment_process_id = pn.talent_recruitment_process_id and eli.node_type=-1
                              where pn.node_type > 20  group by pn.talent_recruitment_process_id
                          ) pn on pn.talent_recruitment_process_id = application.talent_recruitment_process_id

           WHERE application.node_type = 20
           GROUP BY tenant_id,          company_id,          job_id,          job_pteam_id,          team_id,          team_name,          user_id,          user_name,          user_activated,          add_date,          event_date
          )
      UNION ALL
          (SELECT tenant_id,company_id,job_id,job_pteam_id,team_id,team_name,user_id,user_name,user_activated,
                 add_date AS add_date,
                 event_date AS event_date,
                 BITMAP_AGG(user_role) AS user_roles,
                 BITMAP_EMPTY() AS submit_to_job_current_countNum,
                 BITMAP_EMPTY() AS submit_to_job_currentAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_job_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_job_currentStayedOver,
                 BITMAP_EMPTY() AS submit_to_client_current_countNum,
                 BITMAP_EMPTY() AS submit_to_client_currentAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_client_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_client_currentStayedOver,

                          BITMAP_AGG( CASE
                              WHEN (
                                  application.progress = 1
                                      AND node_status = 1
                                      AND node_type = 30
                                      AND max_progress_subquery.progress = 1
                                  ) THEN node_id
                          END) AS current_interview1,

                          BITMAP_AGG( CASE
                              WHEN (
                                  application.progress = 2
                                      AND node_status = 1
                                      AND node_type = 30
                                      AND max_progress_subquery.progress = 2
                                  ) THEN node_id
                          END) AS current_interview2,

                          BITMAP_AGG( CASE
                              WHEN (
                                  application.progress >= 2
                                      AND final_round <> 1
                                      AND node_status = 1
                                      AND node_type = 30
                                  ) THEN node_id
                          END) AS current_two_or_more_interviews,
                 BITMAP_AGG( CASE
                                                     WHEN (
                                                         final_round = 1
                                                             AND node_status = 1
                                                             AND node_type = 30
                                                         ) THEN node_id
                                    END) AS current_interview_final,
                 BITMAP_AGG( CASE
                                                     WHEN (
                                                         node_status = 1
                                                             AND node_type = 30
                                                         ) THEN node_id
                                    END) AS current_interview_total,

                          BITMAP_AGG(CASE
                              WHEN (
                                  node_status = 1
                                      ) THEN talent_recruitment_process_id
                          END)
                           AS current_interview_total_process,

                          BITMAP_AGG( CASE
                              WHEN (
                                  ai_score IS NOT NULL
                                  AND node_status = 1
                                  AND node_type = 30
                              ) THEN node_id
                          END) AS currentInterviewTotalAiRecommendNum,

                          BITMAP_AGG(CASE
                              WHEN (
                                  ai_score IS NOT NULL
                                  AND node_status = 1
                              ) THEN talent_recruitment_process_id
                          END)
                           AS currentInterviewTotalProcessAiRecommendNum,

                          BITMAP_AGG(CASE
                                WHEN (
                                    application.progress = 1
                                        AND node_status = 1
                                        AND max_progress_subquery.progress = 1
                                        AND ai_score IS NOT NULL
                                ) THEN talent_recruitment_process_id
                          END)
                           AS currentInterview1AiRecommendNum,

                          BITMAP_AGG( CASE
                              WHEN (
                                  application.progress = 2
                                      AND node_status = 1
                                      AND max_progress_subquery.progress = 2
                                      AND ai_score IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                          END)
                           AS currentInterview2AiRecommendNum,

                          BITMAP_AGG( CASE
                                WHEN (
                                    application.progress >= 2
                                        AND final_round <> 1
                                        AND node_status = 1
                                        AND node_type = 30
                                        AND ai_score IS NOT NULL
                                    ) THEN node_id
                          END) AS currentTwoOrMoreInterviewsAiRecommendNum,

                          BITMAP_AGG(CASE
                                WHEN (
                                    final_round = 1
                                        AND node_status = 1
                                        AND ai_score IS NOT NULL
                                    ) THEN talent_recruitment_process_id
                          END)
                           AS currentInterviewFinalAiRecommendNum,

                          BITMAP_AGG( CASE
                              WHEN (
                                  recommend_feedback_id IS NOT NULL
                                  AND node_status = 1
                                  AND node_type = 30
                              ) THEN node_id
                          END) AS currentInterviewTotalPrecisionAiRecommendNum,

                          BITMAP_AGG(CASE
                              WHEN (
                                  recommend_feedback_id IS NOT NULL
                                  AND node_status = 1
                              ) THEN talent_recruitment_process_id
                          END)
                           AS currentInterviewNumProcessPrecisionAIRecommend,

                          BITMAP_AGG(CASE
                              WHEN (
                                  application.progress = 1
                                  AND max_progress_subquery.progress = 1
                                  AND node_status = 1
                                  AND recommend_feedback_id IS NOT NULL
                              ) THEN talent_recruitment_process_id
                          END)
                           AS currentInterview1PrecisionAiRecommendNum,

                          BITMAP_AGG(CASE
                              WHEN (
                                  application.progress = 2
                                  AND max_progress_subquery.progress = 2
                                  AND node_status = 1
                                  AND recommend_feedback_id IS NOT NULL
                              ) THEN talent_recruitment_process_id
                          END)
                           AS currentInterview2PrecisionAiRecommendNum,

                          BITMAP_AGG( CASE
                              WHEN (
                                  application.progress >= 2
                                  AND final_round <> 1
                                  AND node_status = 1
                                  AND node_type = 30
                                  AND recommend_feedback_id IS NOT NULL
                              ) THEN node_id
                          END) AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,

                          BITMAP_AGG(CASE
                              WHEN (
                                  application.final_round = 1
                                  AND node_status = 1
                                  AND recommend_feedback_id IS NOT NULL
                              ) THEN talent_recruitment_process_id
                          END)
                           AS currentInterviewFinalPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS reserve_current_interview_total,
                 BITMAP_EMPTY() AS reserve_interview_currentAiRecommendNum,
                 BITMAP_EMPTY() AS reserve_interview_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS offer_current_countNum,
                 BITMAP_EMPTY() AS offer_currentAiRecommendNum,
                 BITMAP_EMPTY() AS offer_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS offer_accept_current_countNum,
                 BITMAP_EMPTY() AS offer_accept_currentAiRecommendNum,
                 BITMAP_EMPTY() AS offer_accept_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS onboard_current_countNum,
                 BITMAP_EMPTY() AS onboard_currentAiRecommendNum,
                 BITMAP_EMPTY() AS onboard_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS eliminate_current_countNum,
                 BITMAP_EMPTY() AS eliminate_currentAiRecommendNum,
                 BITMAP_EMPTY() AS eliminate_currentPrecisionAiRecommendNum
           FROM view_application_v2 AS application
                LEFT OUTER JOIN (SELECT talent_recruitment_process_id AS application_id,
                                                     MAX(progress)                 AS progress
                                              FROM mv_application_fact fact
                                              WHERE node_type = 30
                                              GROUP BY talent_recruitment_process_id) AS max_progress_subquery
                                          ON application_id = talent_recruitment_process_id
           WHERE node_type = 30
           GROUP BY tenant_id,          company_id,          job_id,          job_pteam_id,          team_id,          team_name,          user_id,          user_name,          user_activated,          add_date,          event_date
          )
      UNION ALL
          (SELECT tenant_id,company_id,job_id,job_pteam_id,team_id,team_name,user_id,user_name,user_activated,
                 add_date AS add_date,
                 event_date AS event_date,
                 BITMAP_AGG(user_role) AS user_roles,
                 BITMAP_EMPTY() AS submit_to_job_current_countNum,
                 BITMAP_EMPTY() AS submit_to_job_currentAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_job_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_job_currentStayedOver,
                 BITMAP_EMPTY() AS submit_to_client_current_countNum,
                 BITMAP_EMPTY() AS submit_to_client_currentAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_client_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_client_currentStayedOver,
                 BITMAP_EMPTY() AS current_interview1,
                 BITMAP_EMPTY() AS current_interview2,
                 BITMAP_EMPTY() AS current_two_or_more_interviews,
                 BITMAP_EMPTY() AS current_interview_final,
                 BITMAP_EMPTY() AS current_interview_total,
                 BITMAP_EMPTY() AS current_interview_total_process,
                 BITMAP_EMPTY() AS currentInterviewTotalAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewTotalProcessAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview1AiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview2AiRecommendNum,
                 BITMAP_EMPTY() AS currentTwoOrMoreInterviewsAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewFinalAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewTotalPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewNumProcessPrecisionAIRecommend,
                 BITMAP_EMPTY() AS currentInterview1PrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview2PrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewFinalPrecisionAiRecommendNum,
                 BITMAP_AGG( IF(node_status = 1, node_id, NULL)) AS reserve_current_interview_total,
                 BITMAP_AGG( IF((ai_score IS NOT NULL AND application.node_status = 1), application.node_id, NULL)) AS reserve_interview_currentAiRecommendNum,
                 BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1), application.node_id, NULL)) AS reserve_interview_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS offer_current_countNum,
                 BITMAP_EMPTY() AS offer_currentAiRecommendNum,
                 BITMAP_EMPTY() AS offer_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS offer_accept_current_countNum,
                 BITMAP_EMPTY() AS offer_accept_currentAiRecommendNum,
                 BITMAP_EMPTY() AS offer_accept_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS onboard_current_countNum,
                 BITMAP_EMPTY() AS onboard_currentAiRecommendNum,
                 BITMAP_EMPTY() AS onboard_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS eliminate_current_countNum,
                 BITMAP_EMPTY() AS eliminate_currentAiRecommendNum,
                 BITMAP_EMPTY() AS eliminate_currentPrecisionAiRecommendNum
           FROM view_application_v2 AS application
           WHERE node_type = 30
           GROUP BY tenant_id,          company_id,          job_id,          job_pteam_id,          team_id,          team_name,          user_id,          user_name,          user_activated,          add_date,          event_date
          )
      UNION ALL
          (SELECT tenant_id,company_id,job_id,job_pteam_id,team_id,team_name,user_id,user_name,user_activated,
                 add_date AS add_date,
                 event_date AS event_date,
                 BITMAP_AGG(user_role) AS user_roles,
                 BITMAP_EMPTY() AS submit_to_job_current_countNum,
                 BITMAP_EMPTY() AS submit_to_job_currentAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_job_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_job_currentStayedOver,
                 BITMAP_EMPTY() AS submit_to_client_current_countNum,
                 BITMAP_EMPTY() AS submit_to_client_currentAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_client_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_client_currentStayedOver,
                 BITMAP_EMPTY() AS current_interview1,
                 BITMAP_EMPTY() AS current_interview2,
                 BITMAP_EMPTY() AS current_two_or_more_interviews,
                 BITMAP_EMPTY() AS current_interview_final,
                 BITMAP_EMPTY() AS current_interview_total,
                 BITMAP_EMPTY() AS current_interview_total_process,
                 BITMAP_EMPTY() AS currentInterviewTotalAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewTotalProcessAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview1AiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview2AiRecommendNum,
                 BITMAP_EMPTY() AS currentTwoOrMoreInterviewsAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewFinalAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewTotalPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewNumProcessPrecisionAIRecommend,
                 BITMAP_EMPTY() AS currentInterview1PrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview2PrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewFinalPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS reserve_current_interview_total,
                 BITMAP_EMPTY() AS reserve_interview_currentAiRecommendNum,
                 BITMAP_EMPTY() AS reserve_interview_currentPrecisionAiRecommendNum,
                 BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL)) AS offer_current_countNum,
                 BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1), application.talent_recruitment_process_id, NULL)) AS offer_currentAiRecommendNum,
                 BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1), application.talent_recruitment_process_id, NULL)) AS offer_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS offer_accept_current_countNum,
                 BITMAP_EMPTY() AS offer_accept_currentAiRecommendNum,
                 BITMAP_EMPTY() AS offer_accept_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS onboard_current_countNum,
                 BITMAP_EMPTY() AS onboard_currentAiRecommendNum,
                 BITMAP_EMPTY() AS onboard_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS eliminate_current_countNum,
                 BITMAP_EMPTY() AS eliminate_currentAiRecommendNum,
                 BITMAP_EMPTY() AS eliminate_currentPrecisionAiRecommendNum
           FROM view_application_v2 AS application
           WHERE node_type = 40
           GROUP BY tenant_id,          company_id,          job_id,          job_pteam_id,          team_id,          team_name,          user_id,          user_name,          user_activated,          add_date,          event_date
          )
      UNION ALL
          (SELECT tenant_id,company_id,job_id,job_pteam_id,team_id,team_name,user_id,user_name,user_activated,
                 add_date AS add_date,
                 event_date AS event_date,
                 BITMAP_AGG(user_role) AS user_roles,
                 BITMAP_EMPTY() AS submit_to_job_current_countNum,
                 BITMAP_EMPTY() AS submit_to_job_currentAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_job_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_job_currentStayedOver,
                 BITMAP_EMPTY() AS submit_to_client_current_countNum,
                 BITMAP_EMPTY() AS submit_to_client_currentAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_client_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_client_currentStayedOver,
                 BITMAP_EMPTY() AS current_interview1,
                 BITMAP_EMPTY() AS current_interview2,
                 BITMAP_EMPTY() AS current_two_or_more_interviews,
                 BITMAP_EMPTY() AS current_interview_final,
                 BITMAP_EMPTY() AS current_interview_total,
                 BITMAP_EMPTY() AS current_interview_total_process,
                 BITMAP_EMPTY() AS currentInterviewTotalAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewTotalProcessAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview1AiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview2AiRecommendNum,
                 BITMAP_EMPTY() AS currentTwoOrMoreInterviewsAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewFinalAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewTotalPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewNumProcessPrecisionAIRecommend,
                 BITMAP_EMPTY() AS currentInterview1PrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview2PrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewFinalPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS reserve_current_interview_total,
                 BITMAP_EMPTY() AS reserve_interview_currentAiRecommendNum,
                 BITMAP_EMPTY() AS reserve_interview_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS offer_current_countNum,
                 BITMAP_EMPTY() AS offer_currentAiRecommendNum,
                 BITMAP_EMPTY() AS offer_currentPrecisionAiRecommendNum,
                 BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL)) AS offer_accept_current_countNum,
                 BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1), application.talent_recruitment_process_id, NULL)) AS offer_accept_currentAiRecommendNum,
                 BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1), application.talent_recruitment_process_id, NULL)) AS offer_accept_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS onboard_current_countNum,
                 BITMAP_EMPTY() AS onboard_currentAiRecommendNum,
                 BITMAP_EMPTY() AS onboard_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS eliminate_current_countNum,
                 BITMAP_EMPTY() AS eliminate_currentAiRecommendNum,
                 BITMAP_EMPTY() AS eliminate_currentPrecisionAiRecommendNum
           FROM view_application_v2 AS application
           WHERE node_type = 41
           GROUP BY tenant_id,          company_id,          job_id,          job_pteam_id,          team_id,          team_name,          user_id,          user_name,          user_activated,          add_date,          event_date
          )
      UNION ALL
          (SELECT tenant_id,company_id,job_id,job_pteam_id,team_id,team_name,user_id,user_name,user_activated,
                 add_date AS add_date,
                 event_date AS event_date,
                 BITMAP_AGG(user_role) AS user_roles,
                 BITMAP_EMPTY() AS submit_to_job_current_countNum,
                 BITMAP_EMPTY() AS submit_to_job_currentAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_job_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_job_currentStayedOver,
                 BITMAP_EMPTY() AS submit_to_client_current_countNum,
                 BITMAP_EMPTY() AS submit_to_client_currentAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_client_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_client_currentStayedOver,
                 BITMAP_EMPTY() AS current_interview1,
                 BITMAP_EMPTY() AS current_interview2,
                 BITMAP_EMPTY() AS current_two_or_more_interviews,
                 BITMAP_EMPTY() AS current_interview_final,
                 BITMAP_EMPTY() AS current_interview_total,
                 BITMAP_EMPTY() AS current_interview_total_process,
                 BITMAP_EMPTY() AS currentInterviewTotalAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewTotalProcessAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview1AiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview2AiRecommendNum,
                 BITMAP_EMPTY() AS currentTwoOrMoreInterviewsAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewFinalAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewTotalPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewNumProcessPrecisionAIRecommend,
                 BITMAP_EMPTY() AS currentInterview1PrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview2PrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewFinalPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS reserve_current_interview_total,
                 BITMAP_EMPTY() AS reserve_interview_currentAiRecommendNum,
                 BITMAP_EMPTY() AS reserve_interview_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS offer_current_countNum,
                 BITMAP_EMPTY() AS offer_currentAiRecommendNum,
                 BITMAP_EMPTY() AS offer_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS offer_accept_current_countNum,
                 BITMAP_EMPTY() AS offer_accept_currentAiRecommendNum,
                 BITMAP_EMPTY() AS offer_accept_currentPrecisionAiRecommendNum,
                 BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL)) AS onboard_current_countNum,
                 BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1), application.talent_recruitment_process_id, NULL)) AS onboard_currentAiRecommendNum,
                 BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1), application.talent_recruitment_process_id, NULL)) AS onboard_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS eliminate_current_countNum,
                 BITMAP_EMPTY() AS eliminate_currentAiRecommendNum,
                 BITMAP_EMPTY() AS eliminate_currentPrecisionAiRecommendNum
           FROM view_application_v2 AS application
           WHERE node_type = 60
           GROUP BY tenant_id,          company_id,          job_id,          job_pteam_id,          team_id,          team_name,          user_id,          user_name,          user_activated,          add_date,          event_date
          )
      UNION ALL
          (SELECT tenant_id,company_id,job_id,job_pteam_id,team_id,team_name,user_id,user_name,user_activated,
                 add_date AS add_date,
                 event_date AS event_date,
                 BITMAP_AGG(user_role) AS user_roles,
                 BITMAP_EMPTY() AS submit_to_job_current_countNum,
                 BITMAP_EMPTY() AS submit_to_job_currentAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_job_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_job_currentStayedOver,
                 BITMAP_EMPTY() AS submit_to_client_current_countNum,
                 BITMAP_EMPTY() AS submit_to_client_currentAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_client_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS submit_to_client_currentStayedOver,
                 BITMAP_EMPTY() AS current_interview1,
                 BITMAP_EMPTY() AS current_interview2,
                 BITMAP_EMPTY() AS current_two_or_more_interviews,
                 BITMAP_EMPTY() AS current_interview_final,
                 BITMAP_EMPTY() AS current_interview_total,
                 BITMAP_EMPTY() AS current_interview_total_process,
                 BITMAP_EMPTY() AS currentInterviewTotalAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewTotalProcessAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview1AiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview2AiRecommendNum,
                 BITMAP_EMPTY() AS currentTwoOrMoreInterviewsAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewFinalAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewTotalPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewNumProcessPrecisionAIRecommend,
                 BITMAP_EMPTY() AS currentInterview1PrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterview2PrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS currentInterviewFinalPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS reserve_current_interview_total,
                 BITMAP_EMPTY() AS reserve_interview_currentAiRecommendNum,
                 BITMAP_EMPTY() AS reserve_interview_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS offer_current_countNum,
                 BITMAP_EMPTY() AS offer_currentAiRecommendNum,
                 BITMAP_EMPTY() AS offer_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS offer_accept_current_countNum,
                 BITMAP_EMPTY() AS offer_accept_currentAiRecommendNum,
                 BITMAP_EMPTY() AS offer_accept_currentPrecisionAiRecommendNum,
                 BITMAP_EMPTY() AS onboard_current_countNum,
                 BITMAP_EMPTY() AS onboard_currentAiRecommendNum,
                 BITMAP_EMPTY() AS onboard_currentPrecisionAiRecommendNum,
                 BITMAP_AGG( node_id) AS eliminate_current_countNum,

                              BITMAP_AGG( CASE
                                  WHEN (
                                      ai_score IS NOT NULL
                                      AND node_id IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                              END) AS eliminate_currentAiRecommendNum,

                          BITMAP_AGG( CASE
                              WHEN (
                                  recommend_feedback_id IS NOT NULL
                                  AND node_id IS NOT NULL
                              ) THEN talent_recruitment_process_id
                          END) AS eliminate_currentPrecisionAiRecommendNum
           FROM view_application_v2 AS application
           WHERE node_type = -1
           GROUP BY tenant_id,          company_id,          job_id,          job_pteam_id,          team_id,          team_name,          user_id,          user_name,          user_activated,          add_date,          event_date
          )
)) AS final_table;