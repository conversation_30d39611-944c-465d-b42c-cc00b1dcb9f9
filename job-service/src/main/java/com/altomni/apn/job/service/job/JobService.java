package com.altomni.apn.job.service.job;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.company.CompanyBriefDTO;
import com.altomni.apn.common.dto.company.ICompanyTeamUser;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.job.*;
import com.altomni.apn.common.dto.search.SearchConditionDTO;
import com.altomni.apn.common.dto.job.FindPrivateJobByIdsDTO;
import com.altomni.apn.job.domain.job.RelateJobFolderInfo;
import com.altomni.apn.job.service.dto.job.*;
import com.altomni.apn.job.web.rest.vm.CompanyVM;
import com.altomni.apn.job.web.rest.vm.MyApplication;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Set;

public interface JobService {

    JobDTOV3 update(JobDTOV3 jobDTO) throws IOException;

    String deleteJob(Long jobId, Long tenantId);

    JobDTOV3 updatePrivateJob(JobDTOV3 jobDTO) throws IOException;

    List<ResignUserReportJobDTO> findAllByIds(List<Long> jobIds);

    JobDTOV3 findOneWithEntity(Long id);

    JobDTOV3 findOneWithEntity(Long id,Long tenantId);

    JobDTOV3 findOnePrivateJobWithEntity(Long id);

    JobDTOV3 findOneBasicInfoNoToken(Long id);

    JobDTOV3 findOneWithEntityNoToken(Long id);

    List<LocationDTO> getJobLocation(Long jobId);

    void sendJobStatusChangeEmail(JobV3 job, JobStatus newStatus, JobStatus oldStatus);

    JobDTOV3 formatAndSave(JobDTOV3 jobDTO, boolean isPrivateJob) throws IOException;

    void saveJobLocationsV3(Long jobId, List<LocationDTO> locations, Boolean isCreated);

    String searchJob(SearchConditionDTO condition, Pageable pageable, HttpHeaders headers) throws Throwable;

    JobDTOV3 updateStatus(Long id, JobStatus status);

    JobDTOV3 updatePrivateJobStatus(Long id, JobStatus status);

    List<JobCompanyContactRelationVO> getJobHrInfo(Long companyId);

    List<CompanyVM> findAllCompanyForDashboard(Boolean myJobsOnly, Instant from, Instant to);

    List<MyApplication> findAllMyApplicationsForDashboard(Long jobId, NodeType status, boolean relatedToMe);

    Set<String> getJobCountries();

    List<CompanyBriefDTO> getJobCompanies();

    Set<String> getUserCountries();

    String formatJd(String data);

    JobDTOV3 getJobWithoutEntity(Long id);

    List<IDormantJobDTO> findAllDormantJobsByAmId(Long tenantId, Long amId);

    List<JobV3> findAllByHotListId(Long hotListId);

    HttpResponse syncJobToIpg(JobToIpgDTO jobDTO) throws IOException;

    HttpResponse updateSyncJobToIpg(JobToIpgDTO jobDTO) throws IOException ;

    HttpResponse deleteSyncJobToIpg(Long id, JobStatus status) throws IOException;

    HttpResponse querySyncJobToIpg(Long id) throws IOException;

    void sendMailToAmOfJob(Long jobId, String emailProject, String emailContent);

    void syncIpgJob(JobDTOV3 jobDTO, JobDTOV3 saved) throws IOException;

    void syncIpgJobDto(JobDTOV3 result);

    void numberOfOfferAcceptedEqualsOpenings(Long jobId, Integer status) throws IOException;

    List<SearchCategoryCountDTO> getJobSearchCategoryStatistics();

    List<JobDTOV3> updateJobsStatus(UpdateJobsStatusDTO updateJobsStatusDTO);

    List<String> querySearchHistory();

    void insertSearchHistoryDummy(SearchConditionDTO searchDTO);

    /***
     * Get job update activities
     * @param jobId
     * @param pageable
     * @return
     */
    Page<JobActivityDTO> getJobActivities(Long jobId, Pageable pageable) throws Throwable;

    List<ICompanyTeamUser.CompanyTeamUserDTO> getCompanyTeamUserByJobId(Long jobId);

    String getJobTitleByJobId(Long jobId);

    List<RelateJobFolderInfo> searchRelateJobFolders(SearchConditionDTO searchDTO, Pageable pageable, HttpHeaders headers) throws Throwable;

    JobDTOV3 findOneBrief(Long jobId);

    Integer searchJobsCountByPTeamId(Long teamId);

    List<SalesLeadDTO> searchJobSalesLead(Long companyId,String subcategory);

    List<JobHistoryRecommendDTO> searchHistoryJobRecommend(Long talentId);

    String getJobLocationsByJobId(Long jobId);

    List<JobBriefDTO> getBriefJobsByIds(List<Long> ids);

    Set<Long> findPrivateJobIds(List<Long> ids);

    List<JobCrossSellDTOV3> getJobInfoByBusinessId(List<Long> businessIdList);


    List<JobPermissionDTO>  checkJobPermissionById(@RequestBody List<Long> jobIds);

    JobPermissionDTO checkJobPermissionByChinaInvoicing(@RequestBody Long jobIds);

    JobDTOV3 translate2JobDTOV3(String request);

    String getSearchCondition(ExtractKeywordsDTO dto) throws IOException;

    List<Long> findPrivateJobByIds(FindPrivateJobByIdsDTO dto);
}
