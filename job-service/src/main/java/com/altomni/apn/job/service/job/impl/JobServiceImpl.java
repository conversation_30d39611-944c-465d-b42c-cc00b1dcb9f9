package com.altomni.apn.job.service.job.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.config.constants.ElasticSearchConstants;
import com.altomni.apn.common.config.constants.EsFillerConstants;
import com.altomni.apn.common.constants.ResponsibilityConstants;
import com.altomni.apn.common.domain.dict.*;
import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarRelationEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarTypeEnum;
import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderStatus;
import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.job.OperationType;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.domain.job.JobAdditionalInfo;
import com.altomni.apn.common.domain.job.JobNote;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.domain.talent.TalentAssociationJobFolder;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.FeeDTO;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.SkillDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessBriefDTO;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessStats;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import com.altomni.apn.common.dto.calendar.CompleteSystemCalendarDTO;
import com.altomni.apn.common.dto.company.*;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.job.*;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.redis.ParserRedisResponse;
import com.altomni.apn.common.dto.search.ConditionParam;
import com.altomni.apn.common.dto.search.*;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.ParseStatus;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.errors.NoPermissionException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.repository.talent.TalentRelateJobFolderRepository;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.enums.*;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.company.domain.business.AccountBusiness;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.job.config.DefaultApplicationInfoConfig;
import com.altomni.apn.job.config.env.ApplicationProperties;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.domain.aisourcing.JobAiSourcing;
import com.altomni.apn.job.domain.enumeration.SearchCategory;
import com.altomni.apn.job.domain.job.*;
import com.altomni.apn.job.domain.project.JobProject;
import com.altomni.apn.job.repository.aisourcing.JobAiSourcingRepository;
import com.altomni.apn.job.repository.job.*;
import com.altomni.apn.job.repository.project.JobProjectRepository;
import com.altomni.apn.job.service.application.ApplicationService;
import com.altomni.apn.job.service.common.CommonClient;
import com.altomni.apn.job.service.company.CompanyService;
import com.altomni.apn.job.service.dto.folder.JobCategoryCountRequestDTO;
import com.altomni.apn.job.service.dto.job.*;
import com.altomni.apn.job.service.elastic.EsFillerJobService;
import com.altomni.apn.job.service.finance.FinanceService;
import com.altomni.apn.job.service.job.*;
import com.altomni.apn.job.service.jobdiva.JobdivaService;
import com.altomni.apn.job.service.mail.MailService;
import com.altomni.apn.job.service.store.StoreService;
import com.altomni.apn.job.service.user.UserService;
import com.altomni.apn.job.util.DataServiceConvertUtil;
import com.altomni.apn.job.util.ObjectMapperUtils;
import com.altomni.apn.job.web.rest.vm.CompanyVM;
import com.altomni.apn.job.web.rest.vm.JobCompanyIdAndSalesLeadIdVM;
import com.altomni.apn.job.web.rest.vm.MyApplication;
import com.altomni.apn.user.service.calendar.CalendarService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.EntityNotFoundException;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.common.config.constants.Constants.GENERALTEXT;
import static com.altomni.apn.common.config.constants.ElasticSearchConstants.*;
import static com.altomni.apn.common.config.constants.RedisConstants.DATA_KEY_JOB_SEARCH_HISTORY;
import static com.altomni.apn.job.service.elastic.impl.EsFillerJobServiceImpl.NOT_KEEP_KEYS;
import static com.altomni.apn.job.service.elastic.impl.EsFillerJobServiceImpl.USELESS_KEYS;

@Slf4j
@Service
public class JobServiceImpl implements JobService {

    @Resource
    private JobRepository jobRepository;

    @Resource
    private UserService userService;

    @Resource
    private CalendarService calendarService;

    @Resource
    private JobNoteRepository jobNoteRepository;

    @Resource
    private EsFillerJobService esFillerJobService;

    @Resource
    private JobBoolStringRepository jobBoolStringRepository;

    @Resource
    private CompanyService companyService;

    @Resource
    private JobCompanyContactRelationService jobCompanyContactRelationService;

    @Resource
    private JobCompanyContactRelationRepository jobCompanyContactRelationRepository;

    @Resource
    private JobLocationRepository jobLocationRepository;

    @Resource
    private JobParticipantRepository jobParticipantRepository;

    //TODO: to remove
    @Resource
    private UserJobRelationRepository userJobRelationRepository;

    @Resource
    private SyncJobToIpgRelationRepository syncJobToIpgRelationRepository;

    @Resource
    private IpgService ipgService;

    @Resource
    private ApplicationService applicationService;

    @Resource
    private MailService mailService;

    @Resource
    private CachePermission cachePermission;

    @Resource
    private InitiationService initiationService;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    private EnumJobFunctionService enumJobFunctionService;

    @Resource
    private EnumDegreeService enumDegreeService;

    @Resource
    private EnumJobPriorityService enumJobPriorityService;

    @Resource
    private EnumLanguageService enumLanguageService;

    @Resource
    private EnumUserResponsibilityService enumUserResponsibilityService;

    @Resource
    private StoreService storeService;

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private JobRelationService jobRelationService;

    @Resource
    private JobActivityService jobActivityService;

    @Resource
    private JobFolderService jobFolderService;

    @Resource
    private DefaultApplicationInfoConfig defaultApplicationInfoConfig;

    @Resource(name = "commonThreadPool")
    private Executor executor;
    @Resource
    private PlatformTransactionManager transactionManager;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    @Resource
    private JobProjectRepository jobProjectRepository;

    @Resource
    private FinanceService financeService;

    @Resource
    private JobdivaService jobdivaService;

    @Resource
    private CommonClient commonClient;

    @Resource
    private JobAiSourcingRepository jobAiSourcingRepository;

    @Value("${job_search_condition_generator_url}")
    private String jobSearchConditionGeneratorUrl;

    private void replacePteamIdWithProjectId(Long jobId){
        Optional<JobProject> projectOptional = jobProjectRepository.findFirstByTenantId(SecurityUtils.getTenantId());
        if (projectOptional.isEmpty()){
            throw new CustomParameterizedException("Private job is not available!");
        }
        jobRepository.updatePteamId(jobId, projectOptional.get().getId());
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public JobDTOV3 formatAndSave(JobDTOV3 jobDTO, boolean isPrivateJob) {
        // 手动控制事务
        TransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);
        JobV3 save;
        try {
            validateInputValue(jobDTO);
            jobDTO.setStatus(JobStatus.OPEN);
            removeEnumsNull(jobDTO);
            JobV3 job = translate(null, jobDTO);
            //set jdUrl
            if (jobDTO.getRawJdFile() != null && ObjectUtil.isNotEmpty(jobDTO.getRawJdFile().getUuid())) {
                if (BooleanUtil.isTrue(storeService.isExists(jobDTO.getRawJdFile().getUuid(), UploadTypeEnum.JD.getKey()).getBody())) {
                    job.setJdUrl(jobDTO.getRawJdFile().getUuid());
                }
            }
            checkEnumValid(job);
            save = jobRepository.save(setSalary(job));
            jobDTO.setId(save.getId());
            saveJobLocationsV3(save.getId(), jobDTO.getLocations(), true);
            jobCompanyContactRelationService.create(jobDTO);
            jobRelationService.saveJobAssignedUsers(jobDTO.getAssignedUsers(), save.getId(), true);
            userService.updateUserRecentlyUsedRecruitmentProcess(save.getRecruitmentProcessId(), SecurityUtils.getUserId());
            if (isPrivateJob){
                this.replacePteamIdWithProjectId(save.getId());
                userService.updateUserRecentlyUsedRecruitmentProcessForPrivateJob(save.getRecruitmentProcessId(), SecurityUtils.getUserId());
            }else{
                userService.updateUserRecentlyUsedRecruitmentProcess(save.getRecruitmentProcessId(), SecurityUtils.getUserId());
            }
            saveBoolStrings(jobDTO.getBoolstr(), save.getId());
            transactionManager.commit(status);
        } catch ( Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
        return toDto(save);
    }

    private void validateInputValue(JobDTOV3 jobDTO) {
        if (SecurityUtils.getUserType() != TenantUserTypeEnum.EMPLOYER && (ObjectUtil.isNull(jobDTO.getCompany()) || ObjectUtil.isNull(jobDTO.getCompany().getId()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDATEINPUTVALUE_COMPANYPARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (SecurityUtils.getUserType() == TenantUserTypeEnum.EMPLOYER){
            List<BriefCompanyDTO> companyList = companyService.getAllBriefCompanyList(SecurityUtils.getTenantId()).getBody();
            if(companyList == null || companyList.size() != 1){
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDATEINPUTVALUE_COMPANYNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
            CompanyBriefDTO companyBriefDTO = new CompanyBriefDTO();
            companyBriefDTO.setId(companyList.get(0).getId());
            jobDTO.setCompany(companyBriefDTO);
        }
        if (ObjectUtil.isNull(jobDTO.getTitle())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDATEINPUTVALUE_JOBTITLENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        if (ObjectUtil.isNull(jobDTO.getRecruitmentProcess()) || ObjectUtil.isNull(jobDTO.getRecruitmentProcess().getId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDATEINPUTVALUE_RECRUITMENTPROCESSNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        RecruitmentProcessVO recruitmentProcessVO = applicationService.getRecruitmentProcessBriefById(jobDTO.getRecruitmentProcess().getId()).getBody();
        if (recruitmentProcessVO == null || recruitmentProcessVO.getStatus().equals(ActiveStatus.INACTIVE)) {
            throw new CustomParameterizedException("Recruitment Process is not valid!");
        }
        if (SecurityUtils.getUserType() != TenantUserTypeEnum.EMPLOYER && (ObjectUtil.isNotNull(jobDTO.getClientContact()) && ObjectUtil.isNotEmpty(jobDTO.getClientContact().getId()))) {
            ClientContactBriefInfoDTO contactBriefInfoDTO = companyService.getCompanyClientContactBriefInfo(jobDTO.getCompany().getId(), jobDTO.getClientContact().getId()).getBody();
            if (ObjectUtil.isNull(contactBriefInfoDTO)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDATEINPUTVALUE_CONTACTBRIEFNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
        }
        checkContractDuration(jobDTO);
    }

    private void checkContractDuration(JobDTOV3 jobDTO){
        RecruitmentProcessVO recruitmentProcessVO = applicationService.getRecruitmentProcessBriefById(jobDTO.getRecruitmentProcess().getId()).getBody();

        if (recruitmentProcessVO.getJobType() != null && jobDTO.getContractDuration() != null && jobDTO.getStartDate() != null && jobDTO.getEndDate() != null) {

            if (!List.of(JobType.CONTRACT, JobType.MSP).contains(recruitmentProcessVO.getJobType())){
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDDATEINPUTVALUE_CONTRACTDURATION_WRONG.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
            if(jobDTO.getContractDuration() > 9999){
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDDATEINPUTVALUE_CONTRACTDURATION_WRONG.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
            Integer duration = Integer.parseInt(DateUtil.calculateDays(jobDTO.getStartDate(), jobDTO.getEndDate())) + 1;
            if (!duration.equals(jobDTO.getContractDuration())){
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDDATEINPUTVALUE_CONTRACTDURATION_WRONG.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
        }
    }


    private void checkEnumValid(JobV3 job) {
        /*if (ObjectUtil.isNotEmpty(job.getEnumPriorityId())) {
            EnumJobPriority enumJobPriority = enumJobPriorityService.findEnumJobPriorityById(job.getEnumPriorityId());
        }
        if (ObjectUtil.isNotEmpty(job.getCurrency())) {
            EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(job.getCurrency());
        }*/
        if (ObjectUtil.isNotEmpty(job.getMinimumDegreeId())) {
            if (job.getMinimumDegreeId() > Integer.MAX_VALUE || job.getMinimumDegreeId() < Integer.MIN_VALUE) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CHECKENUMVALID_MINIMUMDEGREEIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
            //EnumDegree enumDegree = enumDegreeService.findEnumDegreeById(job.getMinimumDegreeId());
        }
        if(CollUtil.isEmpty(job.getJobFunctions())){
            job.setJobFunctions(new HashSet<>());
        }
        if(CollUtil.isEmpty(job.getPreferredDegrees())){
            job.setPreferredDegrees(new HashSet<>());
        }
        if(CollUtil.isEmpty(job.getRequiredLanguages())){
            job.setRequiredLanguages(new HashSet<>());
        }
        if(CollUtil.isEmpty(job.getPreferredLanguages())){
            job.setPreferredLanguages(new HashSet<>());
        }
    }


    @Override
    public void saveJobLocationsV3(Long jobId, List<LocationDTO> locations, Boolean isCreated) { //TODO: job location merge and delete
        if (CollUtil.isEmpty(locations)) {
            if (!isCreated) {
                jobLocationRepository.deleteAllByJobId(jobId);
            }
            return;
        }

        if(isCreated){
            jobLocationRepository.saveAll(formatJobLocation(locations, jobId));
            return;
        }

        //existed
        List<JobLocation> existingLocations = jobLocationRepository.findAllByJobId(jobId);

        List<LocationDTO> newLocations = locations.stream()
                .filter(l -> l.getId() == null)
                .collect(Collectors.toList());
        //old to delete
        Set<Long> locationIdsFromInput = locations.stream()
                .map(LocationDTO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<JobLocation> locationsToDelete = existingLocations.stream()
                .filter(l -> !locationIdsFromInput.contains(l.getId()))
                .collect(Collectors.toList());

        if(CollUtil.isNotEmpty(locationsToDelete)){
            jobLocationRepository.deleteAll(locationsToDelete);
        }
        if (CollUtil.isNotEmpty(newLocations)) {
            jobLocationRepository.saveAll(formatJobLocation(newLocations, jobId));
        }
    }

    private List<JobLocation> formatJobLocation(List<LocationDTO> locations, Long jobId){
        List<JobLocation> jobLocationList = new ArrayList<>();
        locations.forEach(l -> {
            JobLocation jobLocation = new JobLocation();
            jobLocation.setOriginalLoc(JSONUtil.toJsonStr(l));
            jobLocation.setJobId(jobId);
            jobLocationList.add(jobLocation);
        });
        return jobLocationList;
    }

    private void formatJobLocation(Long id, JobDTOV3 result) {
        List<JobLocation> jobLocationList = jobLocationRepository.findAllByJobId(id);
        jobLocationList = jobLocationList.stream().sorted(Comparator.comparingLong(JobLocation::getId)).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(jobLocationList)) {
            result.setLocations(convertToLocationDTOList(jobLocationList));
            result.setDefaultCommission(defaultApplicationInfoConfig.getCommissionFloat(
                    jobLocationList.stream()
                            .map(JobLocation::getOfficialCountry)
                            .collect(Collectors.toList())));
        }
    }


    private List<LocationDTO> convertToLocationDTOList(List<JobLocation> jobLocationList) {
        return jobLocationList.stream()
                .filter(s -> ObjectUtil.isNotEmpty(s.getOriginalLoc()))
                .map(l -> {
                    LocationDTO locationDTO = JSONUtil.toBean(l.getOriginalLoc(), LocationDTO.class);
                    locationDTO.setId(l.getId());
                    return locationDTO;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<LocationDTO> getJobLocation(Long jobId) {
        List<JobLocation> jobLocationList = jobLocationRepository.findAllByJobId(jobId);
        jobLocationList = jobLocationList.stream().sorted(Comparator.comparingLong(JobLocation::getId)).collect(Collectors.toList());
        return CollUtil.isNotEmpty(jobLocationList) ? convertToLocationDTOList(jobLocationList) : new ArrayList<>();
    }




    private void saveParticipants(List<ParticipantDTO> participants, Long jobId, Boolean isCreated) {
        if (!isCreated) {
            jobParticipantRepository.deleteAllByJobId(jobId);
        }
        if (ObjectUtil.isNotEmpty(participants)) {
            List<JobParticipant> jobParticipantList = participants.stream().map(s -> {
                JobParticipant userJobRelation = new JobParticipant();
                userJobRelation.setUserId(SecurityUtils.getUserId());
                userJobRelation.setJobId(jobId);
                return userJobRelation;
            }).collect(Collectors.toList());
            jobParticipantRepository.saveAll(jobParticipantList);
        }
    }


    private List<AccountBusiness> getSalesLeadInfo(SearchConditionDTO condition){
        if(!condition.getSearch().isEmpty()){
            List<SearchParam> searchParams = condition.getSearch();
            AccountBusinessJobDTO dto = new AccountBusinessJobDTO();
            for (SearchParam searchParam : searchParams) {
                List<ConditionParam> conditionParams = searchParam.getCondition();
                for (ConditionParam conditionParam : conditionParams) {
                    if (conditionParam.getKey().equals("businessUnit")) {
                        LinkedHashMap x = (LinkedHashMap) conditionParam.getValue();
                        String data = x.get("data").toString();
                        JSONArray dataArr = JSONUtil.parseArray(data);
                        dto.setBusinessUnit(dataArr.getStr(0));
                    }
                    if (conditionParam.getKey().equals("companyId")) {
                        LinkedHashMap x = (LinkedHashMap) conditionParam.getValue();
                        String data = x.get("data").toString();
                        JSONArray dataArr = JSONUtil.parseArray(data);
                        dto.setCompanyId(dataArr.getLong(0));
                    }
                }
            }
            if (null != dto.getBusinessUnit() && StringUtils.isNotBlank(dto.getBusinessUnit())) {
                List<AccountBusiness> accountBusinesses = companyService.getAllByBusinessUnit(dto).getBody();

                if (null != accountBusinesses && !accountBusinesses.isEmpty()) {
                    Set<Long> salesLeadIds = accountBusinesses.stream().map(AccountBusiness::getId).collect(Collectors.toSet());
                    ConditionParam param = ConditionParam.of("salesLeadId", salesLeadIds);
                    searchParams.get(0).getCondition().add(param);
                } else {
                    //businessUnit查不到任何数据时，给dataService传递-1，确保查询返回正确
                    Set<Long> salesLeadIds = Set.of(-1L);
                    ConditionParam param = ConditionParam.of("salesLeadId", salesLeadIds);
                    searchParams.get(0).getCondition().add(param);
                }

                return accountBusinesses;
            } else {
                List<AccountBusiness> accountBusinesses = companyService.getAllByBusinessUnit(dto).getBody();
                return accountBusinesses;
            }
        }
        return new ArrayList<>();
    }

   /* private void setSalesLeadId(List<SearchParam> searchParams,Set<Long> salesLeadIds){
        for (SearchParam searchParam : searchParams) {
            List<ConditionParam> conditionParams = searchParam.getCondition();
            boolean flag = false;
            for (ConditionParam conditionParam : conditionParams) {
                if (conditionParam.getKey().equals("salesLeadId")) {
                    flag = true;
                    LinkedHashMap x = (LinkedHashMap) conditionParam.getValue();
                    String data = x.get("data").toString();
                    JSONArray dataArr = JSONUtil.parseArray(data);
                    for (int i = 0; i < dataArr.size(); i++) {
                        salesLeadIds.add(dataArr.getLong(i));
                    }
                    JSONObject dataJson = new JSONObject();
                    dataJson.put("data", salesLeadIds);
                    conditionParam.setValue(dataJson);
                    break;
                }
            }
            if (!flag) {
                ConditionParam param = ConditionParam.of("salesLeadId", salesLeadIds);
                searchParam.getCondition().add(param);
            }
        }
    }*/

    /*************************************
     * Job List page Search
     * ***********************************************/
    @Override
    public String searchJob(SearchConditionDTO condition, Pageable pageable, HttpHeaders headers) throws Throwable {

        List<AccountBusiness> accountBusinesses = getSalesLeadInfo(condition);

        SearchGroup searchGroup = new SearchGroup();
        ServiceUtils.myCopyProperties(condition, searchGroup);
        //check search area
        SearchConditionDTO.checkPageable(pageable);
        //fix pageInfo
        pageable = PageRequest.of(pageable.getPageNumber() - 1, pageable.getPageSize(), pageable.getSort());
        //format filter
        SearchFilterDTO searchFilterDTO = new SearchFilterDTO();
        List<String> cloneList = ObjectUtil.cloneByStream(ElasticSearchConstants.JOB_SEARCH_SOURCE);
        List<String> sourceList = new ArrayList<>(cloneList);
        if (Objects.equals(condition.getLanguage(), LanguageEnum.EN)) {
            ElasticSearchConstants.EN_OR_CH_SORT_KEY_FOR_JOB.forEach(sortKye -> sourceList.add(sortKye + ElasticSearchConstants.EN_SORT_KEY));
        } else {
            ElasticSearchConstants.EN_OR_CH_SORT_KEY_FOR_JOB.forEach(sortKye -> sourceList.add(sortKye + ElasticSearchConstants.CN_SORT_KEY));
        }
        // For filling private job
        if (ModuleType.ALL_JOB.getName().equals(condition.getModule().getName())){
            sourceList.add(ElasticSearchConstants.JOB_SEARCH_SOURCE_AFFILIATIONS);
        }
        searchFilterDTO.setSource(sourceList);
        searchFilterDTO.setQueryFilter(condition.getFilter());
        searchGroup.setIndex(Constants.INDEX_JOBS + SecurityUtils.getTenantId());
        searchGroup.setModule(condition.getModule().getName());
        searchGroup.setFilter(searchFilterDTO);
        searchGroup.setTimeZone(condition.getTimezone());
        searchGroup.setLanguage(condition.getLanguage().toDbValue());
        //format condition
        if (CollUtil.isNotEmpty(condition.getSearch())) {
            formatSearchGroup(condition.getSearch());
        } else {
            searchGroup.setSearch(new ArrayList<>());
        }
        //search from commonService
        List<EnumUserResponsibility> enumUserResponsibilityList = enumUserResponsibilityService.findAllUserResponsibility();
        this.applyDataPermission(condition, searchGroup);
        this.validateSearchJob(searchGroup, condition.getFolderIds());
        //convertEsAndFilter(condition.getFilter(), searchGroup);// if "ANY" convert to responsibility10
        DataServiceConvertUtil.translateFilterSearchParams(searchGroup);
        DataServiceConvertUtil.convertEsFilterEnumToJobEsKey(searchGroup, enumUserResponsibilityList);
        DataServiceConvertUtil.convertFilterParamToEsFilterEnumToJobEsKey(searchGroup, enumUserResponsibilityList);
        HttpResponse response;
        try {
            response = esFillerJobService.searchFromCommonService(searchGroup, pageable);

        } catch (ExternalServiceInterfaceException es) {
            throw es;
        } catch (Exception e) {
            if (Objects.isNull(e.getCause())){
                throw e;
            }
            throw e.getCause();
        }
        List<Object> subList;
        if (ObjectUtil.isEmpty(response) || ObjectUtil.isEmpty(response.getBody())) {
            headers.set("Pagination-Count", String.valueOf(0));
            return "";
        } else {
            headers.set("Pagination-Count", ElasticSearchUtil.getObjectCountFromResponseHeader(response.getHeaders()) + "");
            Map<Long,AccountBusiness> accountBusinessMap = accountBusinesses.stream().collect(Collectors.toMap(AccountBusiness::getId, Function.identity()));
            return this.fillPrivateJob(response.getBody(), condition.getModule(), accountBusinessMap);
        }
    }

    private String fillPrivateJob(String jobsFromEs, ModuleType module,Map<Long,AccountBusiness> accountBusinesses){
        if (ModuleType.ALL_JOB.getName().equals(module.getName()) && StringUtils.isNotBlank(jobsFromEs)){
            Set<String> privateJobTeamIds = jobRepository.findPrivateJobTeamIds(SecurityUtils.getTenantId()).stream().map(t -> "pteam_" + t).collect(Collectors.toSet());
            JSONArray jobs = new JSONArray(jobsFromEs);
            for (int i = 0; i < jobs.size(); i++) {
                JSONObject job = jobs.getJSONObject(i);
                JSONObject jobSource = job.getJSONObject("_source");
                boolean isPrivateJob = false;
                if (jobSource.containsKey(ElasticSearchConstants.JOB_SEARCH_SOURCE_AFFILIATIONS)){
                    Set<String> affiliations = jobSource.get(ElasticSearchConstants.JOB_SEARCH_SOURCE_AFFILIATIONS, Set.class);
                    isPrivateJob = CollectionUtils.isNotEmpty(SetUtils.intersection(affiliations, privateJobTeamIds));
                    jobSource.remove(ElasticSearchConstants.JOB_SEARCH_SOURCE_AFFILIATIONS);
                }
                jobSource.put("isPrivateJob", isPrivateJob);
                if (!accountBusinesses.isEmpty() && jobSource.containsKey(JOB_SEARCH_SOURCE_SALES_LEAD_ID)) {
                    Long salesLeadId = jobSource.getLong(JOB_SEARCH_SOURCE_SALES_LEAD_ID);
                    AccountBusiness accountBusiness = accountBusinesses.get(salesLeadId);
                    jobSource.put("businessUnit", accountBusiness.getBusinessUnit());
                    jobSource.put("businessName", accountBusiness.getName());
                }
            }
            jobsFromEs = JSONUtil.toJsonStr(jobs);
        }
        return jobsFromEs;
    }

    private boolean validateSearchJob(SearchGroup searchGroup, List<String> folderIds) {
        if (folderIds == null || folderIds.isEmpty()) {
            return true;
        }
        List<Long> lFolderIds = folderIds.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        if (jobFolderService.validateFolderInSearch(lFolderIds)) {
            searchGroup.getFilter().setFolders(folderIds);
            return true;
        } else {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDATESEARCHJOB_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
    }

    private void convertEsAndFilter(List<SearchParam> filter, SearchGroup searchGroup) {
        if (CollUtil.isEmpty(filter)) {
            return;
        }

        Optional.of(searchGroup.getFilter()).ifPresent(filterList -> {
            List<com.altomni.apn.common.dto.search.ConditionParam> list = new ArrayList<>();
            filter.forEach(param -> {
                com.altomni.apn.common.dto.search.ConditionParam conditionParam = param.getCondition().stream().filter(e -> Objects.equals("ANY", e.getKey())).findAny().orElse(null);
                if (ObjectUtil.isNotNull(conditionParam)) {
                    Object value = conditionParam.getValue();
                    param.getCondition().remove(conditionParam);
                    JSONObject jsonObject = JSONUtil.parseObj(value);
                    Object data = jsonObject.get("data");
                    if (data instanceof String) {
                        String dataStr = (String) data;
                        jsonObject.put("data", CollUtil.newArrayList(Long.parseLong(dataStr)));
                    } else if (data instanceof Integer) {
                        Integer dataStr = (Integer) data;
                        jsonObject.put("data", CollUtil.newArrayList(dataStr.longValue()));
                    }
                    list.add(new ConditionParam(EsFillerConstants.ES_PARTICIPANTS + EsFillerConstants.ES_POSTFIX, jsonObject, null));
                    //list.add(new ConditionParam("userResponsibility10.userId", jsonObject, null));
                }
            });
            if (CollUtil.isNotEmpty(list)) {
                SearchParam searchParam = new SearchParam();
                searchParam.setRelation(Relation.OR);
                searchParam.setCondition(list);
                filter.add(searchParam);
            }
            searchGroup.getFilter().setQueryFilter(filter.stream().filter(s -> CollUtil.isNotEmpty(s.getCondition())).collect(Collectors.toList()));
        });
    }

    private void fillConditionForSearchingMyJobWithAllPermission(List<SearchParam> filter){
        SearchParam searchParam = new SearchParam();
        searchParam.setRelation(Relation.OR);
        List<ConditionParam> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", CollUtil.newArrayList(SecurityUtils.getUserId()));
        list.add(new ConditionParam(EsFillerConstants.ES_CREATED_BY + ".id", jsonObject, null));
        list.add(new ConditionParam(EsFillerConstants.ES_PARTICIPANTS + ".id", jsonObject, null));
        list.add(new ConditionParam(EsFillerConstants.ES_APPLICATION_PARTICIPANT + ".id", jsonObject, null));
        searchParam.setCondition(list);
        filter.add(searchParam);
    }

    private void fillConditionForSearchingMyJobWithSelfPermission(List<SearchParam> filter){
        SearchParam searchParam = new SearchParam();
        searchParam.setRelation(Relation.OR);
        List<ConditionParam> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", CollUtil.newArrayList(SecurityUtils.getUserId()));
        list.add(new ConditionParam(EsFillerConstants.ES_CREATED_BY + ".id", jsonObject, null));

        JSONObject participants = new JSONObject();
        participants.put("data", CollUtil.newArrayList(SecurityUtils.getUserId()));
        list.add(new ConditionParam("assignedUser", participants, null));

        searchParam.setCondition(list);
        filter.add(searchParam);
    }

    /**
     * search from ES with data permission
     * if the current user has all permission, skip append filters.
     * if the current user has some teams data permission, add filter: affiliations: ["pteam_53", "pteam_65", "all"], all is for public job
     * if the current user has only user limited permission, add filter: userResponsibility5.userId: [1052], 1052 is user ID.
     *
     * @param condition
     */
    private void applyDataPermission(SearchConditionDTO condition, SearchGroup searchGroup) {
        List<SearchParam> filter = condition.getFilter();
        TeamDataPermissionRespDTO teamDataPermission = cachePermission.getTeamDataPermissionFromCacheOnly(SecurityUtils.getUserId());
        log.info("DataPermission (user: {}) = {}", SecurityUtils.getUserId(), teamDataPermission);
        if (Objects.isNull(teamDataPermission)) {
            teamDataPermission = initiationService.initiateDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        }
        if (Objects.nonNull(teamDataPermission.getTeamIdForPrivateJob())){
            searchGroup.getFilter().setAffiliationForPrivateJob("pteam_" + teamDataPermission.getTeamIdForPrivateJob());
        }
        searchGroup.getFilter().setPrivateJobPermission(teamDataPermission.isPrivateJobPermission());
        searchGroup.getFilter().setPuserId(SecurityUtils.getUserId());
        // private job filter

        if (condition.getModule() == ModuleType.PRIVATE_JOB){
            List<ConditionParam> list = new ArrayList<>();
            SearchParam privateJobSearchParam = new SearchParam();
            privateJobSearchParam.setRelation(Relation.OR);
            JSONObject createdBy = new JSONObject();
            createdBy.put("data", CollUtil.newArrayList(SecurityUtils.getUserId()));
            list.add(new ConditionParam("createdBy", createdBy, null));

            JSONObject participants = new JSONObject();
            participants.put("data", CollUtil.newArrayList(SecurityUtils.getUserId()));
            list.add(new ConditionParam("assignedUser", participants, null));
            privateJobSearchParam.setCondition(list);
            filter.add(privateJobSearchParam);
        }

        if (BooleanUtils.isTrue(teamDataPermission.getAll())) {
            if (ModuleType.MY_JOB.equals(condition.getModule())){
                this.fillConditionForSearchingMyJobWithAllPermission(filter);
            }
            return;
        }

        if (CollectionUtils.isNotEmpty(teamDataPermission.getReadableTeamIds())) {
            List<String> readableTeamIds = teamDataPermission.getReadableTeamIds().stream().map(tId -> EsFillerConstants.ES_PTEAM + tId).collect(Collectors.toList());
            readableTeamIds.add(EsFillerConstants.ES_PUSER + SecurityUtils.getUserId());
            if (ModuleType.MY_JOB.equals(condition.getModule())){
                this.fillConditionForSearchingMyJobWithAllPermission(filter);
            }
            searchGroup.getFilter().setAffiliations(readableTeamIds);
        } else if (BooleanUtils.isTrue(teamDataPermission.getSelf())) {
            this.fillConditionForSearchingMyJobWithSelfPermission(filter);
        }
        if (!filter.isEmpty()){
            searchGroup.getFilter().setQueryFilter(filter.stream().filter(s -> CollUtil.isNotEmpty(s.getCondition())).collect(Collectors.toList()));
        }
    }

    private void formatSearchGroup(List<SearchParam> search) {
        search.forEach(s -> {
            s.getCondition().forEach(l -> {
                //translate dictCode
                if (ElasticSearchConstants.DROP_DOWN_KEYS.contains(l.getKey())) {
                    cn.hutool.json.JSONObject data = JSONUtil.parseObj(JSONUtil.toJsonStr(JSONUtil.parse(l.getValue())));
                    Iterator iter = data.entrySet().iterator();
                    while (iter.hasNext()) {
                        Map.Entry entry = (Map.Entry) iter.next();
                        if ("data".contains(entry.getKey().toString())) {
                            switch (l.getKey()) {
                                //jobFunction
                                case ElasticSearchConstants.DROP_DOWN_JOBFUNCTION:
                                    List<String> list = Convert.toList(String.class, entry.getValue());
                                    entry.setValue(list.stream().map(Long::valueOf).toList());
                                    break;
                                default:
                                    break;
                            }
                            l.setValue(data);
                        }
                    }
                }
            });
        });
    }


    private void addClientBoardInterviewStats(List<JobESSearchDTO> subList, List<Long> jobIds) {
        if (CollUtil.isEmpty(subList)) {
            return;
        }
        List<RecruitmentProcessStats> recruitmentProcessStatsList = applicationService.getClintBoardInterviewStats(jobIds).getBody();
        if (CollUtil.isEmpty(recruitmentProcessStatsList)) {
            return;
        }
        Map<Long, JobESSearchDTO> subListMap = subList.stream().collect(Collectors.toMap(JobESSearchDTO::getId, a -> a));
        Map<Long, List<RecruitmentProcessStats>> listMap = recruitmentProcessStatsList.stream().collect(Collectors.groupingBy(RecruitmentProcessStats::getJobId));
        subListMap.forEach((k, v) -> {
            List<RecruitmentProcessStats> processStatsList = listMap.get(k);
            if (CollUtil.isNotEmpty(processStatsList)) {
                v.setProcessStats(processStatsList);
            }
        });
    }

    private List<JobESSearchDTO> translateCompanyData(List<JobLocation> jobLocationList, List<JobESSearchDTO> subList) {
        if (ObjectUtil.isEmpty(subList)) {
            return subList;
        }
        List<String> cities = new ArrayList<>();
        List<String> countries = new ArrayList<>();
        List<String> provinces = new ArrayList<>();
        subList.forEach(s -> {
            jobLocationList.stream().filter(jobLocation -> NumberUtil.compare(s.getId(), jobLocation.getJobId()) == 0)
                    .forEach(l -> {
                        if (ObjectUtil.isNotEmpty(l.getOfficialCity())) {
                            cities.add(l.getOfficialCity());
                        }
                        if (ObjectUtil.isNotEmpty(l.getOfficialCountry())) {
                            countries.add(l.getOfficialCountry());
                        }
                        if (ObjectUtil.isNotEmpty(l.getOfficialProvince())) {
                            provinces.add(l.getOfficialProvince());
                        }
                        if (ObjectUtil.isNotEmpty(cities)) {
                            s.setCities(cities);
                        }
                        if (ObjectUtil.isNotEmpty(provinces)) {
                            s.setProvinces(provinces);
                        }
                        if (ObjectUtil.isNotEmpty(countries)) {
                            s.setCountries(countries);
                        }
                    });
            if (s.getType() instanceof String) {
                s.setJobType(Convert.convert(JobType.class, s.getType()));
            }
        });
        return subList;
    }


    private JobV3 translate(JobV3 jobLocal, JobDTOV3 jobDTO) {

        //recruitment process to get job type
        if (ObjectUtil.isEmpty(jobDTO.getRecruitmentProcess()) || ObjectUtil.isEmpty(jobDTO.getRecruitmentProcess().getId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_TRANSLATE_RECRUITMENTPROCESSNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        log.info("Processing a job with recruitmentProcessId: {}", jobDTO.getRecruitmentProcess().getId());
        RecruitmentProcessVO recruitmentProcessVO = applicationService.getRecruitmentProcessBriefById(jobDTO.getRecruitmentProcess().getId()).getBody();
        //TODO: empty recruitmentProcessVO how to handle
        if (recruitmentProcessVO != null) {
            if (!recruitmentProcessVO.getTenantId().equals(SecurityUtils.getTenantId())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_TRANSLATE_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
            if (recruitmentProcessVO.getJobType() == null) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_TRANSLATE_JOBTYPENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
            JobType jobType = recruitmentProcessVO.getJobType();
            if (!JobType.PAY_ROLL.getName().equals(jobType.getName())) {
                if (List.of(JobType.CONTRACT.getName(), JobType.MSP.getName()).contains(jobType.getName())) {
                    if (ObjectUtil.isNotEmpty(jobDTO.getBillRange()) &&
                            (ObjectUtil.isEmpty(jobDTO.getCurrency()) || ObjectUtil.isEmpty(jobDTO.getPayType()))) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_TRANSLATE_BILLRATEEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
                    }
                }
                if ((ObjectUtil.isNotEmpty(jobDTO.getBillRange()) || ObjectUtil.isNotEmpty(jobDTO.getSalaryRange())) &&
                        (ObjectUtil.isEmpty(jobDTO.getCurrency()) || ObjectUtil.isEmpty(jobDTO.getPayType()))) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_TRANSLATE_BILLRATEEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
                }
            } else {
                jobDTO.setStatus(JobStatus.NO_STATUS);
            }
        }
        if (ObjectUtil.isNotEmpty(jobDTO.getOpenings()) && StrUtil.toString(jobDTO.getOpenings()).length() > 4) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_TRANSLATE_JOBOPENLIMIT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (ObjectUtil.isNotEmpty(jobDTO.getMaxSubmissions()) && StrUtil.toString(jobDTO.getMaxSubmissions()).length() > 4) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_TRANSLATE_MAXSUBMISSIONSLIMIT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        JobV3 jobNew = JobV3.fromJobDTO(jobDTO);
        if (jobLocal != null) {
            if (jobNew.getStatus() == null) {
                jobNew.setStatus(jobLocal.getStatus());
            }
            //set date/by
            jobNew.setCreatedDate(jobLocal.getCreatedDate());
            jobNew.setCreatedBy(jobLocal.getCreatedBy());

            //set
            jobNew.setIsNeedSyncHr(jobLocal.getIsNeedSyncHr());

            //set job function and language
            processEnumValue(jobNew, jobLocal);

            //keep the last sync time
            jobNew.setLastSyncTime(jobLocal.getLastSyncTime());

            String preExtendInfo = null;
            String newExtendInfo = null;
            if (Objects.nonNull(jobLocal.getJobAdditionalInfo())){
                preExtendInfo = jobLocal.getJobAdditionalInfo().getExtendedInfo();
            }

            if (Objects.nonNull(jobNew.getJobAdditionalInfo())){
                newExtendInfo = jobNew.getJobAdditionalInfo().getExtendedInfo();
            }

            if (StringUtils.isNotBlank(newExtendInfo) && !newExtendInfo.equals(preExtendInfo)){
                jobAiSourcingRepository.setJobInfoUpdatedAndIncreaseSearchVersion(jobLocal.getId(), true);
            }
        }

        //BEGIN: job additional info

        //1. get data from redis and fill into additional info
        if (ObjectUtil.isNotNull(jobDTO.getRawJdFile())) {
            formatFromRedis(jobNew, jobDTO.getRawJdFile().getUuid());
        }

        JobAdditionalInfo jobAdditionalInfo = new JobAdditionalInfo();
        if (jobNew.getJobAdditionalInfo() != null) {
            jobAdditionalInfo = jobNew.getJobAdditionalInfo();
        }

        //keep update existing additional info and keep the publicDesc
        if (jobLocal != null && jobLocal.getJobAdditionalInfo() != null) {
            jobAdditionalInfo.setId(jobLocal.getJobAdditionalInfo().getId());
            jobAdditionalInfo.setPublicDesc(jobLocal.getJobAdditionalInfo().getPublicDesc());
        }

        //TODO: combine two jd constant => remove after key name fully update
//        final String JD_SUMMARY = "summary";
//        final String JD_REQUIREMENT = "requirements";
//        final String JD_RESPONSIBILITY = "responsibilities";
        //set additional extra info
        com.alibaba.fastjson.JSONObject extendedInfo = new com.alibaba.fastjson.JSONObject();
        if (StrUtil.isNotBlank(jobNew.getJobExtendedInfo())) {
            extendedInfo.putAll(JSON.parseObject(jobNew.getJobExtendedInfo()));
        }

        //清理可能存在的parser的结果
//        extendedInfo.remove(JD_REQUIREMENT);
//        extendedInfo.remove(JD_SUMMARY);
//        extendedInfo.remove(JD_RESPONSIBILITY);
        extendedInfo.remove(JD_REQUIREMENT_TEXT);
        extendedInfo.remove(JD_SUMMARY_TEXT);
        extendedInfo.remove(JD_RESPONSIBILITY_TEXT);
        extendedInfo.remove(JOB_PUBLICDESC);

        //end of valid
        if (jobLocal != null){ //与数据库中已存在的extendedInfo合并
            fillExtendInfo(extendedInfo, jobLocal.getJobExtendedInfo());
        }
        // 使用 Fastjson 生成 JSON 字符串，保留数组中的 null 元素
        jobAdditionalInfo.setExtendedInfo(
                JSON.toJSONString(
                        extendedInfo,
                        // 关键配置：保留 null 值
                        SerializerFeature.WriteMapNullValue,
                        SerializerFeature.WriteNullListAsEmpty
                )
        );
        jobAdditionalInfo.setLocalExtendedInfo(generateLocalExtendedInfo(jobDTO)); //TODO: merge with jobLocal localExtendedInfo

//        if (StringUtils.isNotBlank(jobDTO.getSummary())) {
//            jobAdditionalInfo.setSummary(jobDTO.getSummary());
//        }
//        if (StringUtils.isNotBlank(jobDTO.getRequirements())) {
//            jobAdditionalInfo.setRequirements(jobDTO.getRequirements());
//        }
//        if (StringUtils.isNotBlank(jobDTO.getResponsibilities())) {
//            jobAdditionalInfo.setResponsibilities(jobDTO.getResponsibilities());
//        }

        jobNew.setJobAdditionalInfo(jobAdditionalInfo);
        //END job additional info

        jobNew.setRecruitmentProcessId(jobDTO.getRecruitmentProcess().getId());
        jobNew.setCompanyId(jobDTO.getCompany().getId());
        jobNew.setTenantId(SecurityUtils.getTenantId());
        jobNew.setOpenings(jobDTO.getOpenings() != null ? jobDTO.getOpenings() : 1);
        if (ObjectUtil.isEmpty(jobNew.getId())) {
//            jobNew.setPostingTime(Instant.now());
            jobNew.setOpenTime(Instant.now());
        } else {
            if (ObjectUtil.isNotEmpty(jobLocal)) {
                jobNew.setPostingTime(jobLocal.getPostingTime());
                jobNew.setOpenTime(jobLocal.getOpenTime());
            }
        }
        jobNew.setLastEditedTime(Instant.now());
        jobNew.setLastModifiedDate(Instant.now());
        return jobNew;
    }


    private void fillExtendInfo(com.alibaba.fastjson.JSONObject extendedInfo, String localJobExtendedInfoStr) {
        if (StringUtils.isNotEmpty(localJobExtendedInfoStr)) {
            // 使用 Fastjson 解析 JSON
            com.alibaba.fastjson.JSONObject localJobExtendedInfo = JSON.parseObject(localJobExtendedInfoStr);
            if (!localJobExtendedInfo.isEmpty()) {
                localJobExtendedInfo.keySet().stream()
                        .filter(localKey ->
                                !USELESS_KEYS.contains(localKey) &&
                                        !extendedInfo.containsKey(localKey) &&
                                        !NOT_KEEP_KEYS.contains(localKey)
                        )
                        .forEach(t -> {
                            extendedInfo.put(t, localJobExtendedInfo.get(t));
                        });
            }
        }
    }

    private static String generateLocalExtendedInfo(JobDTOV3 dto) {
        com.alibaba.fastjson.JSONObject extendedInfo = new com.alibaba.fastjson.JSONObject();
        if (dto.getReasonForRecruitment() != null) {
            extendedInfo.put("reasonForRecruitment", dto.getReasonForRecruitment());
        }
        if (dto.getTeamComposition() != null) {
            extendedInfo.put("teamComposition", dto.getTeamComposition());
        }
        if (dto.getPreferredCompanies() != null) {
            extendedInfo.put("preferredCompanies", dto.getPreferredCompanies());
        }
        if (dto.getSuggestionsForProspecting() != null) {
            extendedInfo.put("suggestionsForProspecting", dto.getSuggestionsForProspecting());
        }
        if (dto.getEstimatedJobFee() != null) {
            extendedInfo.put("estimatedJobFee", dto.getEstimatedJobFee());
        }
        if (dto.getFeeStructure() != null) {
            extendedInfo.put("feeStructure", dto.getFeeStructure());
        }
        if (dto.getContractSigningParty() != null) {
            extendedInfo.put("contractSigningParty", dto.getContractSigningParty());
        }
        if (dto.getRecommendedApproach() != null) {
            extendedInfo.put("recommendedApproach", dto.getRecommendedApproach());
        }
        if (dto.getPreferredIndustry() != null) {
            extendedInfo.put("preferredIndustry", dto.getPreferredIndustry());
        }
        return JSONUtil.toJsonStr(extendedInfo);
    }

//    @Deprecated
//    private void fillExtendInfo(JSONObject additionalJson, String jobExtendedInfo) {
//        if (StringUtils.isNotEmpty(jobExtendedInfo)) {
//            JSONObject exists = JSONUtil.parseObj(jobExtendedInfo);
//            if (!exists.isEmpty()){
//                exists.keySet().stream().filter(t->!USELESS_KEYS.contains(t) && !additionalJson.containsKey(t) && !NOT_KEEP_KEYS.contains(t)).forEach(t->{
//                    additionalJson.put(t, exists.get(t));
//                });
//            }
//        }
//    }

    private JobV3 formatFromRedis(JobV3 job, String uuid) {
        if (ObjectUtil.isNotEmpty(uuid)) {
            String extendedInfo = job.getJobExtendedInfo();
            ParserRedisResponse response = commonRedisService.getParserJDData(uuid);
            if (ObjectUtil.isNotEmpty(response.getStatus()) && ParseStatus.FINISHED.equals(response.getStatus())) {
                if (ObjectUtil.isNotEmpty(response.getData())) {
                    if (ObjectUtil.isNotEmpty(response.getData())) {
                        String extendedInfoRedis = response.getData();
                        extendedInfo = JobV3.mergeExtendedInfo(extendedInfo, extendedInfoRedis);
                        job.setJobExtendedInfo(extendedInfo);
                    }
                }
                //save jd_has_display
                if (ObjectUtil.isNotEmpty(response.getImagesInfo())) {
                    if (ObjectUtil.isNotNull(response.getImagesInfo().getHas_display())) {
                        job.setJdHasDisplay(BooleanUtil.toBoolean(response.getImagesInfo().getHas_display().toString()));
                    }
                }
            }
        }
        return job;
    }

    private void processEnumValue(JobV3 jobNew, JobV3 jobOld){
        Map<String, JobJobFunctionRelation> existJobFunctionRelationMap = jobOld.getJobFunctions().stream()
                .collect(Collectors.toMap(JobJobFunctionRelation::getEnumId, Function.identity(), (existing, replacement) -> existing));
        if (jobNew.getJobFunctions() != null) {
            jobNew.getJobFunctions().forEach(relation -> {
                JobJobFunctionRelation existingRelation = existJobFunctionRelationMap.get(relation.getEnumId());
                if (existingRelation != null) {
                    relation.setJobId(existingRelation.getJobId());
                    relation.setId(existingRelation.getId());
                }else{
                    relation.setJobId(jobNew.getId());
                }
            });
        }

        Map<String, JobPreferredDegreeRelation> existRequiredDegreesMap = jobOld.getPreferredDegrees().stream()
                .collect(Collectors.toMap(JobPreferredDegreeRelation::getEnumId, Function.identity(), (existing, replacement) -> existing));
        if (jobNew.getPreferredDegrees() != null) {
            jobNew.getPreferredDegrees().forEach(relation -> {
                JobPreferredDegreeRelation existingRelation = existRequiredDegreesMap.get(relation.getEnumId());
                if (existingRelation != null) {
                    relation.setJobId(existingRelation.getJobId());
                    relation.setId(existingRelation.getId());
                }else{
                    relation.setJobId(jobNew.getId());
                }
            });
        }

        Map<String, JobRequiredLanguagesRelation> existRequiredLanguagesMap = jobOld.getRequiredLanguages().stream()
                .collect(Collectors.toMap(JobRequiredLanguagesRelation::getEnumId, Function.identity(), (existing, replacement) -> existing));
        if (jobNew.getRequiredLanguages() != null) {
            jobNew.getRequiredLanguages().forEach(relation -> {
                JobRequiredLanguagesRelation existingRelation = existRequiredLanguagesMap.get(relation.getEnumId());
                if (existingRelation != null) {
                    relation.setJobId(existingRelation.getJobId());
                    relation.setId(existingRelation.getId());
                }else{
                    relation.setJobId(jobNew.getId());
                }
            });
        }

        Map<String, JobPreferredLanguagesRelation> existPreferredLanguagesRelationMap = jobOld.getPreferredLanguages().stream()
                .collect(Collectors.toMap(JobPreferredLanguagesRelation::getEnumId, Function.identity(), (existing, replacement) -> existing));
        if (jobNew.getPreferredLanguages() != null) {
            jobNew.getPreferredLanguages().forEach(relation -> {
                JobPreferredLanguagesRelation existingRelation = existPreferredLanguagesRelationMap.get(relation.getEnumId());
                if (existingRelation != null) {
                    relation.setJobId(existingRelation.getJobId());
                    relation.setId(existingRelation.getId());
                }else{
                    relation.setJobId(jobNew.getId());
                }
            });
        }
    }


    private JobV3 setSalary(JobV3 job) {
        com.alibaba.fastjson.JSONObject esDocumentJson = JSON.parseObject(job.getJobExtendedInfo());
        if ((esDocumentJson.containsKey("salaryRange") || esDocumentJson.containsKey("billRange")) && !esDocumentJson.containsKey("payType")) {
            esDocumentJson.put("payType", RateUnitType.MONTHLY);
        }
        if (ObjectUtil.isNotEmpty(job.getCurrency())) {
            EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(job.getCurrency());
            esDocumentJson.put("currencyUSDExchangeRate", enumCurrency.getFromUsdRate());
        } else {
            esDocumentJson.remove("currencyUSDExchangeRate");
        }
        job.setJobExtendedInfo(JSON.toJSONString(esDocumentJson, (PropertyFilter) (o, s, o1) -> ObjectUtil.isNotEmpty(o1)));
        return job;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveBoolStrings(List<JobBoolStringDTO> boolStringDtoList, Long jobId) {
        if (CollUtil.isNotEmpty(boolStringDtoList)) {
            List<JobBoolString> boolStrings = new ArrayList<>();
            boolStringDtoList.forEach(jobBoolStringDTO -> {
                JobBoolString jobBoolString = new JobBoolString();
                ServiceUtils.myCopyProperties(jobBoolStringDTO, jobBoolString);
                jobBoolString.setStrings(JsonUtil.toJSONString(jobBoolStringDTO.getStrings()));
                jobBoolString.setJobId(jobId);
                boolStrings.add(jobBoolString);
            });
            jobBoolStringRepository.saveAll(boolStrings);
        }
    }

    @Override
    public JobDTOV3 update(JobDTOV3 updateJobDTO) throws IOException {
        JobV3 existing = jobRepository.findById(updateJobDTO.getId())
                .orElseThrow(() -> new NoPermissionException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_FINDONEWITHENTITY_NOPERMISSION.getKey(),
                        CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService())));
        return this.updateJob(updateJobDTO, existing);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteJob(Long jobId, Long tenantId) {
        JSONObject jobObj;
        JobV3 jobV3 = jobRepository.findById(jobId).orElse(null);
        if (jobV3 == null) {
            jobObj = new JSONObject();
            jobObj.put("id", jobId);
        } else {
            tenantId = jobV3.getTenantId();
            jobObj = DtoToJsonUtil.toJsonWithColumnNames(jobV3);
            jobObj.put("job_required_languages_relation", DtoToJsonUtil.toJsonArrayWithColumnNames(jobV3.getRequiredLanguages()));
            jobObj.put("job_preferred_languages_relation", DtoToJsonUtil.toJsonArrayWithColumnNames(jobV3.getPreferredLanguages()));
            jobObj.put("job_additional_info", DtoToJsonUtil.toJsonWithColumnNames(jobV3.getJobAdditionalInfo()));
            jobObj.put("job_job_function_relation", DtoToJsonUtil.toJsonArrayWithColumnNames(jobV3.getJobFunctions()));
            jobObj.put("job_preferred_degree_relation", DtoToJsonUtil.toJsonArrayWithColumnNames(jobV3.getPreferredDegrees()));
            List<JobLocation> jobLocationList = jobLocationRepository.findAllByJobId(jobId);
            List<JobNote> jobNoteList = jobNoteRepository.findAllByJobIdAndVisible(jobId, true);
            List<JobCompanyContactRelation> jobCompanyContactRelationList = jobCompanyContactRelationRepository.findAllByJobId(jobId);
            List<UserJobRelation> userJobRelationList = userJobRelationRepository.findAllByJobId(jobId);
            jobObj.put("job_location", DtoToJsonUtil.toJsonArrayWithColumnNames(jobLocationList));
            jobObj.put("job_note", DtoToJsonUtil.toJsonArrayWithColumnNames(jobNoteList));
            jobObj.put("job_company_contact_relation", DtoToJsonUtil.toJsonArrayWithColumnNames(jobCompanyContactRelationList));
            jobObj.put("user_job_relation", DtoToJsonUtil.toJsonArrayWithColumnNames(userJobRelationList));
            JSONArray application = applicationService.deleteByJobId(jobId).getBody();
            jobObj.put("talent_recruitment_process", application);
            JSONArray start = financeService.deleteStartByJobId(jobId).getBody();
            jobObj.put("start", start);
            JSONArray timesheet = jobdivaService.deleteAssignmentByJobId(jobId).getBody();
            jobObj.put("timesheet", timesheet);
            List<Long> jobNoteIds = jobNoteList.stream().map(JobNote::getId).toList();
            JSONArray calendarEventJobNote = commonClient.deleteCalendarEventsByRecruitmentProcessRelationIds(jobNoteIds).getBody();
            jobObj.put("calendar_event_job_note", calendarEventJobNote);
            jobRepository.deleteById(jobId);
            jobLocationRepository.deleteAllByIdInBatch(jobLocationList.stream().map(JobLocation::getId).toList());
            jobNoteRepository.deleteAllByIdInBatch(jobNoteIds);
            jobCompanyContactRelationRepository.deleteAllByIdInBatch(jobCompanyContactRelationList.stream().map(JobCompanyContactRelation::getId).toList());
            userJobRelationRepository.deleteAllByIdInBatch(userJobRelationList.stream().map(UserJobRelation::getId).toList());
        }

        esFillerJobService.deleteJobToMq(jobId, tenantId, 1);
        return JsonUtil.toJson(jobObj);
    }

    @Override
    public JobDTOV3 updatePrivateJob(JobDTOV3 updateJobDTO) throws IOException {
        JobV3 existing = jobRepository.findPrivateJobById(updateJobDTO.getId(), SecurityUtils.getUserId())
                .orElseThrow(() -> new NoPermissionException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_FINDONEWITHENTITY_NOPERMISSION.getKey(),
                        CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService())));
        return this.updateJob(updateJobDTO, existing);
    }

    private JobDTOV3 updateJob(JobDTOV3 updateJobDTO, JobV3 existing) throws IOException {
        checkContractDuration(updateJobDTO);
        // 手动控制事务
        TransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);
        JobV3 save;
        String createdBy;
        try {
            createdBy = existing.getCreatedBy();
            checkJobPermission(existing);
            removeEnumsNull(updateJobDTO);
            JobStatus oldJobStatus = existing.getStatus();
            JobStatus newJobStatus = updateJobDTO.getStatus();
            existing = translate(existing, updateJobDTO);
            if (newJobStatus != null) {
                boolean opened = JobStatus.OPEN.equals(newJobStatus) && !JobStatus.OPEN.equals(oldJobStatus);
                if (opened) {
//                    existing.setPostingTime(Instant.now());
                    existing.setOpenTime(Instant.now());
                }
                if (newJobStatus != oldJobStatus) {
                    sendJobStatusChangeEmail(existing, newJobStatus, oldJobStatus);
                }
            }
            //set jdUrl
            if (updateJobDTO.getRawJdFile() != null && ObjectUtil.isNotEmpty(updateJobDTO.getRawJdFile().getUuid())) {
                if (BooleanUtil.isTrue(storeService.isExists(updateJobDTO.getRawJdFile().getUuid(), UploadTypeEnum.JD.getKey()).getBody())) {
                    existing.setJdUrl(updateJobDTO.getRawJdFile().getUuid());
                } else {
                    //TODO: handle the JdURL cannot process
                    existing.setJdUrl(null);
                }
            } else {
                existing.setJdUrl(null);
            }
            //validate the enum value
            checkEnumValid(existing);
            updateBoolStrings(updateJobDTO);//TODO: JOBV3 Deprecate
            try {
                save = jobRepository.saveAndFlush(setSalary(existing));
            } catch (ObjectOptimisticLockingFailureException e) {
                throw new CustomParameterizedException("You don't have permission to update this job.");
            }
            save.setTenantId(existing.getTenantId());
            save.setCreatedDate(existing.getCreatedDate());
            saveJobLocationsV3(save.getId(), updateJobDTO.getLocations(), false);
            jobCompanyContactRelationService.update(updateJobDTO);
            jobRelationService.saveJobAssignedUsers(updateJobDTO.getAssignedUsers(), save.getId(), false);
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
        JobDTOV3 dto = toDto(save);
        dto.setCreatedBy(createdBy);
        dto.setCompanyAmIds(companyService.getAllAmByCompany(save.getCompanyId()).getBody());
        //TODO: check
        syncIpgJob(updateJobDTO, dto);
        return dto;
    }

    /***
     * remove null enum and discard invalid enum TODO: rename method
     * @param jobDTOV3
     */
    private void removeEnumsNull(JobDTOV3 jobDTOV3) {
        Set<Long> enumJobFunctions = enumJobFunctionService.findValidEnumJobFunctionsIds();
        jobDTOV3.setJobFunctions(jobDTOV3.getJobFunctions() == null ? null : jobDTOV3.getJobFunctions().stream()
                .filter(o -> {
                    if (StringUtils.isNotEmpty(o.getEnumId())) {
                        try {
                            Long enumId = Long.parseLong(o.getEnumId());
                            if (enumJobFunctions.contains(enumId)) {
                                return true;
                            } else {
                                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_REMOVEENUMSNULL_JOBFUNCTIONVALUEVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
                            }
                        } catch (NumberFormatException e) {
                            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_REMOVEENUMSNULL_JOBFUNCTIONVALUEVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
                        }
                    }
                    return false;
                }).collect(Collectors.toList()));

        Set<Long> enumLanguages = enumLanguageService.findValidEnumLanguagesIds();
        jobDTOV3.setRequiredLanguages(jobDTOV3.getRequiredLanguages() == null ? null :
                jobDTOV3.getRequiredLanguages().stream()
                        .filter(o -> {
                            if (StringUtils.isNotEmpty(o.getEnumId())) {
                                try {
                                    Long enumId = Long.parseLong(o.getEnumId());
                                    if (enumLanguages.contains(enumId)) {
                                        return true;
                                    } else {
                                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_REMOVEENUMSNULL_LANGUAGEVALUEVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
                                    }
                                } catch (NumberFormatException e) {
                                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_REMOVEENUMSNULL_LANGUAGEVALUEVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
                                }
                            }
                            return false;
                        })
                        .collect(Collectors.toList()));
        jobDTOV3.setPreferredLanguages(jobDTOV3.getPreferredLanguages() == null ? null :
                jobDTOV3.getPreferredLanguages().stream()
                        .filter(o -> {
                            if (StringUtils.isNotEmpty(o.getEnumId())) {
                                try {
                                    Long enumId = Long.parseLong(o.getEnumId());
                                    if (enumLanguages.contains(enumId)) {
                                        return true;
                                    } else {
                                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_REMOVEENUMSNULL_PREFERREDLANGUAGEVALUEVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
                                    }
                                } catch (NumberFormatException e) {
                                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_REMOVEENUMSNULL_PREFERREDLANGUAGEVALUEVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
                                }
                            }
                            return false;
                        })
                        .collect(Collectors.toList()));

        Set<Long> enumDegrees = enumDegreeService.findValidEnumDegreesIds();
        jobDTOV3.setPreferredDegrees(jobDTOV3.getPreferredDegrees() == null ? null :
                jobDTOV3.getPreferredDegrees().stream()
                        .filter(o -> {
                            if (StringUtils.isNotEmpty(o.getEnumId())) {
                                try {
                                    Long enumId = Long.parseLong(o.getEnumId());
                                    if (enumDegrees.contains(enumId)) {
                                        return true;
                                    } else {
                                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_REMOVEENUMSNULL_PREFERREDDEGREEVALUEVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
                                    }
                                } catch (NumberFormatException e) {
                                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_REMOVEENUMSNULL_PREFERREDDEGREEVALUEVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
                                }
                            }
                            return false;
                        })
                        .collect(Collectors.toList()));
    }

    @Override
    public JobDTOV3 updateStatus(Long id, JobStatus status) {
        JobV3 job = jobRepository.getById(id);
        return this.updateStatus(job, status);
    }

    @Override
    public JobDTOV3 updatePrivateJobStatus(Long id, JobStatus status) {
        JobV3 job = jobRepository.findPrivateJobByIdForUpdate(id, SecurityUtils.getUserId()).orElse(null);
        return this.updateStatus(job, status);
    }

    private JobDTOV3 updateStatus(JobV3 job, JobStatus status) {

        checkJobPermission(job);

        RecruitmentProcessVO recruitmentProcessVO = applicationService.getRecruitmentProcessBriefById(job.getRecruitmentProcessId()).getBody();
        if (recruitmentProcessVO == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_UPDATESTATUS_INVALIDJOB.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (!recruitmentProcessVO.getTenantId().equals(job.getTenantId()) || !job.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_UPDATESTATUS_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (recruitmentProcessVO.getStatus().equals(ActiveStatus.INACTIVE)) {
            throw new CustomParameterizedException("You are not authorized to update a job associated with current recruitment process!");
        }

        if (JobType.PAY_ROLL.equals(recruitmentProcessVO.getJobType())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_UPDATESTATUS_PAYROLLINGJOB.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(job.getId()),jobApiPromptProperties.getJobService()));
        }

        JobStatus oldJobStatus = job.getStatus();
        if (status != null) {
            boolean opened = JobStatus.OPEN.equals(status) && !JobStatus.OPEN.equals(oldJobStatus);
            if (opened) {
                job.setOpenTime(Instant.now());
            }
            if (status != oldJobStatus) {
                CompletableFuture.runAsync(() -> {
                    sendJobStatusChangeEmail(job, status, oldJobStatus);
                });
                // 如果状态变更且最终状态不为OPEN,那么需要更新job的 last_non_open_time
                if (!JobStatus.OPEN.equals(status)){
                    job.setLastNonOpenTime(Instant.now());
                }
            }

        }

        JobDTOV3 jobDTOV3 = new JobDTOV3();
        try {
            job.setStatus(status);
            JobV3 newJob = jobRepository.saveAndFlush(job);
            jobDTOV3.setLastModifiedDate(newJob.getLastModifiedDate());

        } catch (ObjectOptimisticLockingFailureException e) {
            log.error(ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_UPDATE_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        jobDTOV3.setId(job.getId());
        jobDTOV3.setTitle(job.getTitle());

        //set assigned user
        //List<AssignedUserDTO> asList = getJobAssignedUserDTOS(job);
        List<AssignedUserDTO> asList = jobRelationService.getJobAssignedUserDTOList(job.getId());
        jobDTOV3.setAssignedUsers(CollUtil.isEmpty(asList) ? null : asList);

        //return AM only if previous status is open and ipg published(not check since it is in other table)
        if (JobStatus.OPEN.equals(oldJobStatus)) {
            List<AssignedUserDTO> amList = companyService.getApplicationUsers(job.getCompanyId()).getBody();
            jobDTOV3.setAccountManagers(amList);
        }

        if(!JobStatus.OPEN.equals(status)) {
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                notifyCompleteSystemCalendar(job.getId(), CalendarRelationEnum.JOB, List.of(CalendarTypeEnum.NO_SUBMIT_TALENT, CalendarTypeEnum.NO_INTERVIEW));
            });
        }
        return jobDTOV3;
    }

    private void notifyCompleteSystemCalendar(Long jobId, CalendarRelationEnum calendarRelation, List<CalendarTypeEnum> type) {
        if(jobId == null) {
            return;
        }
        CompleteSystemCalendarDTO dto = new CompleteSystemCalendarDTO();
        dto.setRelationType(calendarRelation);
        dto.setType(type);
        dto.setUniqueReferenceId(jobId);
        calendarService.completeSystemCalendar(dto);
    }

    @Override
    @Transactional
    @Deprecated
    public List<JobDTOV3> updateJobsStatus(UpdateJobsStatusDTO updateJobsStatusDTO) {
        if (ObjectUtil.isEmpty(updateJobsStatusDTO) || ObjectUtil.isEmpty(updateJobsStatusDTO.getJobIds()) || ObjectUtil.isEmpty(updateJobsStatusDTO.getToStatus())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_UPDATEJOBSSTATUS_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        List<Long> jobIds = new ArrayList<>(new HashSet<>(updateJobsStatusDTO.getJobIds()));
        List<JobV3> jobList = jobRepository.findAllByIdIn(jobIds);//.stream().collect(Collectors.toMap(JobV3::getId, Function.identity()));
        Set<Long> recruitmentProcessIds = jobList.stream().map(JobV3::getRecruitmentProcessId).collect(Collectors.toSet());
        //TODO: batch get recruitmentProcess
        Map<Long, RecruitmentProcessVO> recruitmentProcessMap = new HashMap<>();
        for (Long id : recruitmentProcessIds) {
            if (id == 0) {//TODO: remove after removing JobType
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_UPDATEJOBSSTATUS_IDERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
            RecruitmentProcessVO recruitmentProcessVO = applicationService.getRecruitmentProcessBriefById(id).getBody();
            if (recruitmentProcessVO == null) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_UPDATEJOBSSTATUS_RECRUITMENTPROCESSNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
            recruitmentProcessMap.put(id, recruitmentProcessVO);
        }


        JobStatus newStatus = updateJobsStatusDTO.getToStatus();
        for (JobV3 job : jobList) {

            if (recruitmentProcessMap.containsKey(job.getRecruitmentProcessId()) && JobType.PAY_ROLL.equals(recruitmentProcessMap.get(job.getRecruitmentProcessId()).getJobType())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_UPDATEJOBSSTATUS_PAYROLLJOB.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
            checkJobPermission(job);
            JobStatus oldJobStatus = job.getStatus();
            if (newStatus != null) {
                boolean opened = JobStatus.OPEN.equals(newStatus) && !JobStatus.OPEN.equals(oldJobStatus);
                if (opened) {
                    job.setOpenTime(Instant.now());
                }
                if (newStatus != oldJobStatus) {
                    //TODO: performance improvement
                    sendJobStatusChangeEmail(job, newStatus, oldJobStatus);
                }
                if (newStatus.equals(JobStatus.CLOSED)) {
                    //TODO: check all talent under this job already finish
                }
            }
            job.setStatus(newStatus);
        }
        try {
            int rows = jobRepository.updateJobsStatus(jobIds, newStatus.toDbValue());
            jobRepository.flush();
            if (rows == 0) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_UPDATE_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
        } catch (ObjectOptimisticLockingFailureException e) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_UPDATE_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        //current design dont return jobDTO
        return new ArrayList<>();

    }


    @Transactional(rollbackFor = Exception.class)
    public void updateBoolStrings(JobDTOV3 updateJobDTO) {
        if (CollUtil.isNotEmpty(updateJobDTO.getBoolstr())) {
            List<JobBoolString> boolStrings = new ArrayList<>();
            updateJobDTO.getBoolstr().forEach(jobBoolStringDTO -> {
                JobBoolString jobBoolString = new JobBoolString();
                ServiceUtils.myCopyProperties(jobBoolStringDTO, jobBoolString);
                jobBoolString.setStrings(JsonUtil.toJSONString(jobBoolStringDTO.getStrings()));
                jobBoolString.setJobId(updateJobDTO.getId());
                boolStrings.add(jobBoolString);
            });
            jobBoolStringRepository.deleteByByJobId(updateJobDTO.getId());
            jobBoolStringRepository.saveAll(boolStrings);
        }
    }


    @Override
    public JobDTOV3 findOneWithEntity(Long id) {
        if (CollectionUtils.isNotEmpty(jobRepository.findPrivateJobId(id))){
            JobDTOV3 job = this.findOnePrivateJobWithEntity(id);

            job.setIsPrivateJob(true);
            return job;
        }
        JobV3 job = jobRepository.findRegularJobById(id, SecurityUtils.getTenantId()).orElse(null);
        if (job == null) {
            //if job do not exist or has no data permission
            throw new NoPermissionException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_FINDONEWITHENTITY_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        JobDTOV3 jobDtoV3 = toDto(hasVisiblePermission(job));
        syncIpgJobDto(jobDtoV3);
        if(jobDtoV3 != null){
            jobDtoV3.setCompanyAmIds(companyService.getAllAmByCompany(job.getCompanyId()).getBody());
            jobDtoV3.setIsPrivateJob(false);
            //设置saleleadName
            setSalesLeadName(jobDtoV3);
            this.setAiSourcing(jobDtoV3, id);
        }
        return jobDtoV3;
    }

    private void setAiSourcing(JobDTOV3 jobDtoV3, Long jobId){
        List<JobAiSourcing> jobAiSourcings = jobAiSourcingRepository.findFirstByJobId(jobId);
        if (CollUtil.isNotEmpty(jobAiSourcings)){
            JobAiSourcing jobAiSourcing = jobAiSourcings.get(0);
            boolean hasAiSourcingPagePermission = cachePermission.hasUserPrivilegePermission(SecurityUtils.getUserId(), applicationProperties.getAiSourcingPermission());
            jobDtoV3.setHasAiSourcingPermission(hasAiSourcingPagePermission);
            jobDtoV3.setRefreshAiSourcingAvailable(jobAiSourcing.isJobInfoUpdated() && jobAiSourcing.isEnabled());
            jobDtoV3.setAiSourcingEnabled(jobAiSourcing.isEnabled());
        }
    }

    private void setSalesLeadName(JobDTOV3 jobDtoV3) {
        Long salesLeadId = jobDtoV3.getSalesLeadId();
        Long companyId = jobDtoV3.getCompanyId();
        RecruitmentProcessBriefDTO recruitmentProcess = jobDtoV3.getRecruitmentProcess();
        if(recruitmentProcess != null && salesLeadId != null && companyId != null) {
            JobType jobType = recruitmentProcess.getJobType();
            if(jobType != null) {
                List<SalesLeadDTO> salesLeadDTOS = searchJobSalesLead(companyId, jobType.name());
                if(salesLeadDTOS != null) {
                    for(int i = 0; i < salesLeadDTOS.size(); i++) {
                        SalesLeadDTO salesLeadDTO = salesLeadDTOS.get(i);
                        Long sId = salesLeadDTO.getId() != null ? salesLeadDTO.getId().longValue() : null;
                        if(salesLeadId.equals(sId)) {
                            jobDtoV3.setSalesLeadName(salesLeadDTO.getSalesLeadName() != null ? salesLeadDTO.getSalesLeadName() : "销售线索" + (i + 1));
                        }
                    }
                } else {
                    jobDtoV3.setSalesLeadName("销售线索1");
                }
            }
        }
    }

    @Override
    public JobDTOV3 findOneWithEntity(Long id,Long tenantId) {
        JobV3 job = jobRepository.findById(id).orElse(null);
        if (job == null) {
            //if job do not exist or has no data permission
            throw new NoPermissionException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_FINDONEWITHENTITY_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        JobDTOV3 jobDtoV3 = toDto(hasVisiblePermission(job));
        syncIpgJobDto(jobDtoV3);
        if(jobDtoV3 != null){
            jobDtoV3.setCompanyAmIds(companyService.getAllAmByCompany(job.getCompanyId()).getBody());
            jobDtoV3.setIsPrivateJob(false);
        }
        return jobDtoV3;
    }

    @Override
    public JobDTOV3 findOnePrivateJobWithEntity(Long id) {
        JobV3 job = jobRepository.findPrivateJobById(id, SecurityUtils.getUserId()).orElse(null);
        log.info("[findOnePrivateJobWithEntity] jobId:{}, userId: {}", id, SecurityUtils.getUserId());
        if (job == null) {
            //if job do not exist or has no data permission
            throw new NoPermissionException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_FINDONEWITHENTITY_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        JobDTOV3 jobDtoV3 = toDto(hasVisiblePermission(job));
        syncIpgJobDto(jobDtoV3);

        if(jobDtoV3 != null){
            jobDtoV3.setCompanyAmIds(companyService.getAllAmByCompany(job.getCompanyId()).getBody());
            //设置saleleadName
            setSalesLeadName(jobDtoV3);
            this.setAiSourcing(jobDtoV3, id);
        }
        return jobDtoV3;
    }

    @Override
    public JobDTOV3 findOneWithEntityNoToken(Long id) {
        JobV3 job = jobRepository.findById(id).orElse(null);
        if (job == null) {
            return null;
        }
        JobDTOV3 jobDtoV3 = toDto(job);
        syncIpgJobDto(jobDtoV3);
        return jobDtoV3;
    }

    @Override
    public JobDTOV3 findOneBasicInfoNoToken(Long id) {
        JobV3 job = jobRepository.findById(id).orElse(null);
        if (job == null) {
            return null;
        }
        JobDTOV3 jobDtoV3 = new JobDTOV3();
        ServiceUtils.myCopyProperties(job, jobDtoV3);
        return jobDtoV3;
    }

    @Override
    public void syncIpgJobDto(JobDTOV3 jobDtoV3) {
        if (jobDtoV3 != null) {
            SyncJobToIpgRelation syncJobToIpgRelation = syncJobToIpgRelationRepository.findByApnJobId(jobDtoV3.getId());
            if (syncJobToIpgRelation != null) {
                JobType jobType = esFillerJobService.getJobTypeByRecruitmentProcessId(jobDtoV3.getRecruitmentProcess().getId());
                jobDtoV3.setIpgJobStatus(syncJobToIpgRelation.getIpgJobStatus() == null ? jobDtoV3.getStatus() : syncJobToIpgRelation.getIpgJobStatus());
                jobDtoV3.setIpgJobType(syncJobToIpgRelation.getIpgJobType() == null ? jobType : syncJobToIpgRelation.getIpgJobType());
                //TODO: sync with check the logical
                jobDtoV3.setIpgRequirements(syncJobToIpgRelation.getIpgJobRequirements() == null && StringUtils.isNotEmpty(jobDtoV3.getRequirements()) ? HtmlUtil.cleanHtmlTag(jobDtoV3.getRequirements()) : syncJobToIpgRelation.getIpgJobRequirements());
                jobDtoV3.setIpgResponsibilities(syncJobToIpgRelation.getIpgJobResponsibilities() == null && StringUtils.isNotEmpty(jobDtoV3.getResponsibilities()) ? HtmlUtil.cleanHtmlTag(jobDtoV3.getResponsibilities()) : syncJobToIpgRelation.getIpgJobResponsibilities());
                jobDtoV3.setIpgSummary(syncJobToIpgRelation.getIpgJobSummary() == null && StringUtils.isNotEmpty(jobDtoV3.getSummary()) ? HtmlUtil.cleanHtmlTag(jobDtoV3.getSummary()) : syncJobToIpgRelation.getIpgJobSummary());
            }
        }
    }

    @Override
    public void numberOfOfferAcceptedEqualsOpenings(Long jobId, Integer status) throws IOException {
        JobV3 jobV3 = jobRepository.findById(jobId).orElse(null);
        if (jobV3 == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_NUMBEROFOFFERACCEPTEDEQUALSOPENINGS_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        jobV3.setStatus(JobStatus.FILLED);
        jobRepository.saveAndFlush(jobV3);
        sendJobStatusChangeEmail(jobV3, JobStatus.FILLED, JobStatus.fromDbValue(status));
        closeIpgJob(jobV3);
    }


    @Override
    public JobDTOV3 getJobWithoutEntity(Long id) {
        JobV3 job = null;
        try {
            job = hasVisiblePermission(jobRepository.getById(id));
        } catch (EntityNotFoundException e) {
            throw new NoPermissionException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_GETJOBWITHOUTENTITY_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        return toDto(job);
    }

    private void closeIpgJob(JobV3 job) {
        if (job == null || job.getId() == null) {
            log.error("[APN: JobService] close a job synchronized to IpgFiller error, parameter error. This Job ID cannot be empty");
            return;
        }
        SyncJobToIpgRelation oldSyncJobToIpgRelation = syncJobToIpgRelationRepository.findByApnJobId(job.getId());
        if (oldSyncJobToIpgRelation == null || oldSyncJobToIpgRelation.getApnJobId() == null || oldSyncJobToIpgRelation.getIpgJobId() == null) {
            log.error("[APN: JobService] close a job synchronized to IpgFiller error, parameter error. This job does not exist or has not been synchronized, job ID: {}", job.getId());
            return;
        }
        Long ipgJobId = oldSyncJobToIpgRelation.getIpgJobId();
        try {
            HttpResponse response = ipgService.deleteSyncJobToIpg(job.getId(), ipgJobId, JobStatus.CLOSED);
            if (response != null && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                SyncJobToIpgRelation syncJobToIpgRelation = new SyncJobToIpgRelation();
                ServiceUtils.myCopyProperties(oldSyncJobToIpgRelation, syncJobToIpgRelation);
                syncJobToIpgRelation.setIpgJobStatus(JobStatus.CLOSED);
                syncJobToIpgRelationRepository.saveAndFlush(syncJobToIpgRelation);
//                esFillerJobService.updateJobPublishedToEs(job.getId(), false);
            }
        } catch (IOException e) {
            log.error("[APN: JobService] close a job synchronized to IpgFiller error, job ID: {}", job.getId());
        }
    }

    /**
     * Check Permission
     *
     * @param job
     * @return
     */
    private JobV3 hasVisiblePermission(JobV3 job) {
        if (ObjectUtil.isEmpty(job)) {
            return null;
        }
        if (SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.ADMIN) || SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.TENANT_ADMIN)) {
            return job;
        }
        if (ObjectUtil.isNull(job) || !job.getTenantId().equals(SecurityUtils.getTenantId())) {
            return null;
        }
        return job;
    }

    private JobDTOV3 toDto(JobV3 job) {
        if (job == null) {
            return null;
        }
        JobDTOV3 result = JobDTOV3.fromJob(job);
        //set enum name; not required now.
        Authentication authentication = SecurityUtils.getAuthentication();

        //set recruitment process
        var setRecruitmentProcessFuture = ObjectUtil.isEmpty(job.getRecruitmentProcessId()) ?
            CompletableFuture.completedFuture(null) :
            CompletableFuture.runAsync(() -> {
                    SecurityUtils.setAuthentication(authentication);
                RecruitmentProcessVO recruitmentProcessVO = applicationService.getRecruitmentProcessBriefById(job.getRecruitmentProcessId()).getBody();
                    if (recruitmentProcessVO != null && ObjectUtil.isNotEmpty(recruitmentProcessVO.getJobType())) {
                        result.getRecruitmentProcess().setJobType(recruitmentProcessVO.getJobType());
                        result.getRecruitmentProcess().setName(recruitmentProcessVO.getJobType().getName());
                        result.getRecruitmentProcess().setStatus(recruitmentProcessVO.getStatus());
                    }

                }, executor);

        //company: companyId is null when it is employer type company
        var setCompanyFuture = job.getCompanyId() == null ?
            CompletableFuture.completedFuture(null) :
            CompletableFuture.runAsync(() -> {
                SecurityUtils.setAuthentication(authentication);
                CompanyDTO company = companyService.getCompany(job.getCompanyId()).getBody();
                if (ObjectUtil.isNotEmpty(company)) {
                    CompanyBriefDTO companyBriefDTO = new CompanyBriefDTO();
                    companyBriefDTO.setId(company.getId());
                    companyBriefDTO.setName(company.getName());
                    companyBriefDTO.setIndustries(company.getIndustries());
                    result.setCompany(companyBriefDTO);
                }
            }, executor);

        var setBoolstrFuture = CompletableFuture.runAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            List<JobBoolStringDTO> boolStringDtoList = new ArrayList<>();
            for (JobBoolString jobBoolString : jobBoolStringRepository.findAllByJobId(job.getId())) {
                JobBoolStringDTO boolStringDTO = new JobBoolStringDTO();
                ServiceUtils.myCopyProperties(jobBoolString, boolStringDTO);
                if (StringUtils.isNotEmpty(jobBoolString.getStrings()) && !"{}".equals(jobBoolString.getStrings())) {
                    boolStringDTO.setStrings(JsonUtil.fromJsonArray(jobBoolString.getStrings()));
                }
                boolStringDtoList.add(boolStringDTO);
            }
            result.setBoolstr(boolStringDtoList);
        }, executor);

        var setClientContactFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return jobCompanyContactRelationRepository.findAllByJobId(job.getId());
        }, executor).thenAcceptAsync(jobCompanyContactRelationList -> {
            SecurityUtils.setAuthentication(authentication);
            if (ObjectUtil.isEmpty(jobCompanyContactRelationList)) {
                return;
            }
            int last = jobCompanyContactRelationList.size() - 1;
            JobCompanyContactRelation jobCompanyContactRelation = jobCompanyContactRelationList.get(last);
            SalesLeadClientContactDTO salesLeadClientContactDTO = new SalesLeadClientContactDTO();
            salesLeadClientContactDTO.setId(jobCompanyContactRelation.getClientContactId());
            salesLeadClientContactDTO.setTalentId(jobCompanyContactRelation.getTalentId());
            ClientContactBriefInfoDTO contactBriefInfoDTO = companyService.getCompanyClientContactBriefInfo(job.getCompanyId(), salesLeadClientContactDTO.getId()).getBody();
            if (ObjectUtil.isNotEmpty(contactBriefInfoDTO)) {
                salesLeadClientContactDTO.setName(CommonUtils.formatFullName(contactBriefInfoDTO.getFirstName(), contactBriefInfoDTO.getLastName()));
            }
            result.setClientContactCategory(jobCompanyContactRelation.getContactCategory());
            result.setClientContact(salesLeadClientContactDTO);
        }, executor);

        var formatJobLocationFuture = CompletableFuture.runAsync(() -> formatJobLocation(job.getId(), result), executor);

        //set job assigned users
        var setAssignedUsersFuture = CompletableFuture.runAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            List<AssignedUserDTO> jobAssignedUserDTOs = jobRelationService.getJobAssignedUserDTOList(job.getId());
            result.setAssignedUsers(CollUtil.isEmpty(jobAssignedUserDTOs) ? null : jobAssignedUserDTOs);
        }, executor);

        var setRawJdFileFuture = CompletableFuture.runAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            //get jd uuid
            if (ObjectUtil.isNotEmpty(job.getJdUrl())) {
                String[] strings = org.apache.commons.lang3.StringUtils.split(job.getJdUrl(), "/");
                String jduuid = strings[strings.length - 1];
                try {

                    CloudFileObjectMetadata jdMetaData = storeService.getFileDetailWithoutFileFromS3(jduuid, UploadTypeEnum.JD.getKey()).getBody();
                    if (jdMetaData != null) {
                        RawJdFileDTO rawJdFileDTO = new RawJdFileDTO();
                        rawJdFileDTO.setUuid(jduuid);
                        rawJdFileDTO.setContentType(jdMetaData.getContentType());
                        try {
                            String decodedString = URLDecoder.decode(jdMetaData.getFileName(), StandardCharsets.UTF_8.name());
                            rawJdFileDTO.setFileName(decodedString);
                        } catch (UnsupportedEncodingException e) {
                            log.error("[APN JobService] toDTO:  failed to decode the fileName");
                            rawJdFileDTO.setFileName(null);
                        }
                        try {
                            rawJdFileDTO.setPresignedUrl(jdMetaData.getS3Link());
                        } catch (Exception ex) {
                            log.error("[APN JobService] toDTO: failed to get presigned Jd Url, error: {}", ex);
                        }
                        result.setRawJdFile(rawJdFileDTO);
                    }
                } catch (Exception ex) {
                    log.error("[APN JobService] toDTO: Failed to get JD metadata, error: {}", ex);
                }
            }
        }, executor);

        CompletableFuture.allOf(setRecruitmentProcessFuture, setCompanyFuture, setBoolstrFuture, setClientContactFuture, formatJobLocationFuture, setAssignedUsersFuture, setRawJdFileFuture)
            .exceptionally(t -> {
                log.error("Error occurred when fetching job data: ", t);
                throw new ExternalServiceInterfaceException("Error occurred when fetching job data");
            }).join();

        /*
        This logic is redundant since it’s already handled by JobDTOV3.fromJob(job);
         */
        //set additional info
//        result.byDTOSetAdditionalInfo(toAdditionalInfoDTO(job));

        // 查询发布到公司官网的时间
        SyncJobToIpgRelation oldSyncJobToIpgRelation = syncJobToIpgRelationRepository.findByApnJobId(job.getId());
        if (Objects.nonNull(oldSyncJobToIpgRelation)
                && oldSyncJobToIpgRelation.getIpgJobStatus() != JobStatus.CLOSED
                && oldSyncJobToIpgRelation.getIpgJobStatus() != JobStatus.NO_PUBLISHED) {
            result.setTenantWebsitePostingTime(oldSyncJobToIpgRelation.getLastModifiedDate());
        }
        return result;
    }

    private AdditionalInfoDTO toAdditionalInfoDTO(JobV3 job) {
        AdditionalInfoDTO additionalInfoDTO = new AdditionalInfoDTO();
        if (ObjectUtil.isNotEmpty(job.getJobAdditionalInfo())) {
            if (ObjectUtil.isNotEmpty(job.getJobAdditionalInfo().getExtendedInfo())) {
                additionalInfoDTO = JsonUtil.fromJson(JSONUtil.toJsonStr(job.getJobAdditionalInfo().getExtendedInfo()), AdditionalInfoDTO.class);
            }

            additionalInfoDTO.setSummary(job.getJobAdditionalInfo().getSummary());
            additionalInfoDTO.setResponsibilities(job.getJobAdditionalInfo().getResponsibilities());
            additionalInfoDTO.setRequirements(job.getJobAdditionalInfo().getRequirements());

        }
        return additionalInfoDTO;
    }

    @Override
    public void sendJobStatusChangeEmail(JobV3 job, JobStatus newStatus, JobStatus oldStatus) {
        List<Long> companyAMIds = companyService.getAllAmByCompany(job.getCompanyId()).getBody();
        List<User> users = userService.findByIds(companyAMIds).getBody();
        List<String> emailAddressList = users.stream().map(User::getEmail).collect(Collectors.toList());
        JSONObject jo = JSONUtil.parseObj(job.getJobExtendedInfo());
        String subject = "Job Status Update: " + job.getTitle() + " | Status updated from [" + oldStatus.name() + "] to [" + newStatus.name() + "]";
        StringBuilder sb = new StringBuilder();
        sb.append("\nJob ID: ").append(job.getId());
        sb.append("\n\nJob Title: ").append(job.getTitle());
        sb.append("\n\nJob Company: ").append(jo.getObj("company") == null ? "" : Convert.convert(CompanyBriefDTO.class, jo.getObj("company")).getName());
        if (!StringUtils.isEmpty(job.getCode())) {
            sb.append("\n\nJob Code: ").append(job.getCode());
        }
        sb.append("\n\n\nTime in US: ").append(DateUtil.currentTimeOfTimezone(DateUtil.US_LA_TIMEZONE));
        sb.append("\nTime in China: ").append(DateUtil.currentTimeOfTimezone(DateUtil.CN_BJ_TIMEZONE));
        MailVM mailVm = new MailVM(applicationProperties.getSupportSender(), emailAddressList, null, null, subject, sb.toString(), null, true);
        mailService.sendHtmlMail(mailVm);
    }

    private void checkJobPermission(JobV3 job) {
        if (job == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_FINDONEWITHENTITY_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (!ObjectUtils.equals(job.getTenantId(), SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_GETJOBWITHOUTENTITY_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        boolean permitted = false;
        if (SecurityUtils.isAdmin()) {
            permitted = true;
        }
        List<Long> companyAMIds = companyService.getAllAmByCompany(job.getCompanyId()).getBody();
        if (companyAMIds.contains(SecurityUtils.getUserId())) {
            permitted = true;
        }
        if (SecurityUtils.getUserId().equals(SecurityUtils.getUserIdFromCreatedBy(job.getCreatedBy()))) {
            permitted = true;
        }
        //assignedUser is allowed update the job
        Boolean isAssignedUser = jobRelationService.isAssignedUsers(job.getId(), SecurityUtils.getUserId());
        if (isAssignedUser) {
            permitted = true;
        }

        if (!permitted) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_GETJOBWITHOUTENTITY_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
    }


    @Override
    public List<JobCompanyContactRelationVO> getJobHrInfo(Long companyId) {
        return jobCompanyContactRelationRepository.findJobContactByCompany(companyId, SecurityUtils.getTenantId());
    }

    @Override
    public List<CompanyVM> findAllCompanyForDashboard(Boolean myJobsOnly, Instant from, Instant to) {
        return jobRepository.findAllCompanyForDashboard(myJobsOnly ? SecurityUtils.getUserId() : null, from, to, SecurityUtils.getTenantId());
    }

    @Override
    public List<MyApplication> findAllMyApplicationsForDashboard(Long jobId, NodeType status, boolean relatedToMe) {
        return jobRepository.findMyApplicationsForDashboard(relatedToMe ? SecurityUtils.getUserId() : null, jobId, status); // userId: 77L for testing
    }

    @Override
    public List<ResignUserReportJobDTO> findAllByIds(List<Long> jobIds) {
        List<JobV3> result = jobRepository.findAllById(jobIds);
        List<Long> companyIds = result.stream().map(JobV3::getCompanyId).collect(Collectors.toList());
        List<CompanyDTO> companyList = companyService.getCompanyByIds(companyIds).getBody();
        return CollUtil.isNotEmpty(result) ? result.stream().map(s -> {
            ResignUserReportJobDTO dto = new ResignUserReportJobDTO();
            ServiceUtils.myCopyProperties(s, dto);
            companyList.stream().forEach(c -> {
                if (c.getId().equals(s.getCompanyId())) {
                    dto.setCompany(c.getName());
                }
            });
            return dto;
        }).collect(Collectors.toList()) : new ArrayList<>();
    }

    @Override
    public Set<String> getJobCountries() {
        Set<String> countries = jobRepository.findAllCountriesByTenantId(SecurityUtils.getTenantId());
        return countries;
    }

    @Override
    public List<CompanyBriefDTO> getJobCompanies() {
        List<Object[]> companies = jobRepository.findAllCompaniesByTenantId(SecurityUtils.getTenantId());
        List<CompanyBriefDTO> companyList = new ArrayList<>();
        companies.stream().forEach(s -> {
            CompanyBriefDTO company = new CompanyBriefDTO();
            if (ObjectUtil.isNotEmpty(String.valueOf(s[0]))) {
                company.setId(Long.parseLong(String.valueOf(s[0])));
            }
            company.setName(String.valueOf(s[1]));
            companyList.add(company);
        });
        return companyList;
    }

    @Override
    public Set<String> getUserCountries() {
        return userService.findUserCountryByTenantId(SecurityUtils.getTenantId()).getBody();
    }

    @Override
    public String formatJd(String data) {
        log.info("[JobServiceImpl: formatJD] Request to format JD :{}", data);
        return format(data);
    }

    /**
     * clean & format JD data
     *
     * @param data
     * @return
     */
    private String format(String data) {
        if (ObjectUtil.isNotEmpty(data)) {
            JobESDocument parserData = JsonUtil.fromJson(JSONUtil.toJsonStr(data), JobESDocument.class);
            JobParserDTO parserDTO = Convert.convert(JobParserDTO.class, parserData);
            if (ObjectUtil.isNotEmpty(parserData)) {
                if (ObjectUtil.isNotEmpty(parserData.getJobFunctions())) {
                    parserDTO.setJobFunctions(parserData.getJobFunctions().stream().map(Long::valueOf).toList());
                } else {
                    parserDTO.setJobFunctions(null);
                }
                data = JSONUtil.toJsonStr(JSONUtil.parse(parserDTO));
            } else {
                return null;
            }
        }
        return data;
    }

    @Override
    public List<IDormantJobDTO> findAllDormantJobsByAmId(Long tenantId, Long amId) {
        return jobRepository.findAllDormantJobsByAmId(tenantId, amId);
    }

    @Override
    public List<JobV3> findAllByHotListId(Long hotListId) {
        return jobRepository.findAllByHotListId(hotListId);
    }

    private void checkJobWritePermission(Long jobId) {
        try {
            int rows = jobRepository.checkJobWritePermission(jobId);
            if (rows == 0) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_UPDATE_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
        } catch (ObjectOptimisticLockingFailureException e) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_UPDATE_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
    }

    @Override
    public HttpResponse syncJobToIpg(JobToIpgDTO jobDTO) throws IOException {
        if (jobDTO.getStatus().equals(JobStatus.CLOSED)) {
            throw new CustomParameterizedException("status cannot be CLOSED.");
        }
        entityManager.clear();
        JobV3 job = jobRepository.findById(jobDTO.getId()).orElseThrow(() -> new NotFoundException("job does not exist"));
        if (hasSyncToIpgPermission(job) == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_SYNCJOBTOIPG_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        this.checkJobWritePermission(job.getId());
        SyncJobToIpgRelation oldSyncJobToIpgRelation = syncJobToIpgRelationRepository.findByApnJobId(job.getId());
        if (job.getId() == null || oldSyncJobToIpgRelation != null) {
            log.error("[APN: IpgFillerJobService] create a job synchronized to IpgFiller error, parameter error. This job does not exist or has been synchronized, job ID: {}", jobDTO.getId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_SYNCJOBTOIPG_IPGFILLERERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        //First obtain the job status of APN system to prevent it from being overwritten.
        JobToIpgDTOV3 jobToIpgDtoV3 = formatJobToIpgDtoV3(job, jobDTO);

        HttpResponse response = ipgService.syncJobToIpg(job.getId(), jobToIpgDtoV3);
        if (response != null && ObjectUtils.equals(HttpStatus.CREATED.value(), response.getCode())) {
            JSONObject resJson = JSONUtil.parseObj(response.getBody());
            SyncJobToIpgRelation syncJobToIpgRelation = new SyncJobToIpgRelation();
            syncJobToIpgRelation.setApnJobId(job.getId());
            syncJobToIpgRelation.setIpgJobId(Long.parseLong(resJson.get("id").toString()));
            syncJobToIpgRelation.setIpgJobStatus(jobToIpgDtoV3.getStatus());
            syncJobToIpgRelation.setIpgJobType(jobToIpgDtoV3.getType());
            syncJobToIpgRelation.setIpgJobDescription(jobToIpgDtoV3.getPublicDesc());
            syncJobToIpgRelation.setIpgJobSummary(jobToIpgDtoV3.getIpgJobSummary());
            syncJobToIpgRelation.setIpgJobRequirements(jobToIpgDtoV3.getIpgJobRequirements());
            syncJobToIpgRelation.setIpgJobResponsibilities(jobToIpgDtoV3.getIpgJobResponsibilities());
            syncJobToIpgRelation.setIpgJobRequiredSkills(JSON.toJSONString(jobToIpgDtoV3.getRequiredSkills()));
            syncJobToIpgRelationRepository.saveAndFlush(syncJobToIpgRelation);
        }
        return response;
    }

    private JobToIpgDTOV3 formatJobToIpgDtoV3(JobV3 job, JobToIpgDTO jobDTO) {
        JobType ipgJobType = jobDTO.getJobType();
        String jdUrl = jobDTO.getJdUrl();
        EnumRelationDTO minimumDegreeList = jobDTO.getMinimumDegreeLevel();
        String jobName = jobDTO.getTitle();
        RangeDTO rangeDTO = jobDTO.getExperienceYearRange();
        List<EnumRelationDTO> jobFunctionList = jobDTO.getJobFunctions();
        List<LocationDTO> locations = jobDTO.getLocations();
        List<SkillDTO> skillList = jobDTO.getRequiredSkills();
        ServiceUtils.myCopyProperties(job, jobDTO);
        jobDTO.setJobType(ipgJobType);
        jobDTO.setMinimumDegreeLevel(minimumDegreeList);
        jobDTO.setJdUrl(jdUrl);
        jobDTO.setExperienceYearRange(rangeDTO);
        jobDTO.setJobFunctions(jobFunctionList);
        jobDTO.setTitle(jobName);
        jobDTO.setRequiredSkills(skillList);
        jobDTO.setLocations(locations);
        JobToIpgDTOV3 jobToIpgDTOV3 = JobToIpgDTOV3.fromJobDTO(jobDTO);
        jobToIpgDTOV3.setIpgJobRequirements(HtmlUtil.cleanHtmlTag(jobDTO.getIpgJobRequirements()));
        jobToIpgDTOV3.setIpgJobResponsibilities(HtmlUtil.cleanHtmlTag(jobDTO.getIpgJobResponsibilities()));
        jobToIpgDTOV3.setIpgJobSummary(jobDTO.getIpgJobSummary() != null ? HtmlUtil.cleanHtmlTag(jobDTO.getIpgJobSummary()) : null);
        return jobToIpgDTOV3;

    }

    @Override
    public HttpResponse updateSyncJobToIpg(JobToIpgDTO jobDTO) throws IOException {
        entityManager.clear();
        JobV3 job = jobRepository.findById(jobDTO.getId()).orElseThrow(() -> new NotFoundException("job does not exist"));
        if (hasSyncToIpgPermission(job) == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_SYNCJOBTOIPG_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        this.checkJobWritePermission(job.getId());
        SyncJobToIpgRelation oldSyncJobToIpgRelation = syncJobToIpgRelationRepository.findByApnJobId(job.getId());
        if (job.getId() == null || oldSyncJobToIpgRelation == null) {
            log.error("[APN: IpgFillerJobService] update a job synchronized to IpgFiller error, parameter error. This job does not exist or has not been synchronized, job ID: {}", jobDTO.getId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_SYNCJOBTOIPG_IPGFILLERERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        JobStatus apnJobStatus = job.getStatus();
        JobToIpgDTOV3 jobToIpgDtoV3 = formatJobToIpgDtoV3(job, jobDTO);
        Long ipgJobId = oldSyncJobToIpgRelation.getIpgJobId();
        HttpResponse response = ipgService.updateSyncJobToIpg(job.getId(), ipgJobId, jobToIpgDtoV3);
        if (response != null && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
            SyncJobToIpgRelation syncJobToIpgRelation = new SyncJobToIpgRelation();
            ServiceUtils.myCopyProperties(oldSyncJobToIpgRelation, syncJobToIpgRelation);
            syncJobToIpgRelation.setApnJobStatus(apnJobStatus);
            syncJobToIpgRelation.setIpgJobStatus(jobToIpgDtoV3.getStatus());
            syncJobToIpgRelation.setIpgJobType(jobToIpgDtoV3.getType());
            syncJobToIpgRelation.setIpgJobDescription(jobToIpgDtoV3.getPublicDesc());
            syncJobToIpgRelation.setIpgJobSummary(jobToIpgDtoV3.getIpgJobSummary());
            syncJobToIpgRelation.setIpgJobRequirements(jobToIpgDtoV3.getIpgJobRequirements());
            syncJobToIpgRelation.setIpgJobResponsibilities(jobToIpgDtoV3.getIpgJobResponsibilities());
            syncJobToIpgRelation.setIpgJobRequiredSkills(JSON.toJSONString(jobToIpgDtoV3.getRequiredSkills()));
            syncJobToIpgRelationRepository.saveAndFlush(syncJobToIpgRelation);
        }
        return response;
    }

    @Override
    public HttpResponse deleteSyncJobToIpg(Long id, JobStatus status) throws IOException {
        if (!status.equals(JobStatus.CLOSED)) {
            throw new CustomParameterizedException("status must be CLOSED.");
        }
        entityManager.clear();
        JobV3 job = jobRepository.findById(id).orElseThrow(() -> new NotFoundException("job does not exist"));
        if (hasSyncToIpgPermission(job) == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_SYNCJOBTOIPG_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        this.checkJobWritePermission(job.getId());
        SyncJobToIpgRelation oldSyncJobToIpgRelation = syncJobToIpgRelationRepository.findByApnJobId(id);
        if (job.getId() == null || oldSyncJobToIpgRelation == null) {
            log.error("[APN: IpgFillerJobService] close a job synchronized to IpgFiller error, parameter error. This job does not exist or has not been synchronized, job ID: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_SYNCJOBTOIPG_IPGFILLERERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        Long ipgJobId = oldSyncJobToIpgRelation.getIpgJobId();
        HttpResponse response = null;
        if (!oldSyncJobToIpgRelation.getIpgJobStatus().equals(status)) {
            response = ipgService.deleteSyncJobToIpg(id, ipgJobId, status);
            if (response != null && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                SyncJobToIpgRelation syncJobToIpgRelation = new SyncJobToIpgRelation();
                ServiceUtils.myCopyProperties(oldSyncJobToIpgRelation, syncJobToIpgRelation);
                syncJobToIpgRelation.setApnJobStatus(job.getStatus());
                syncJobToIpgRelation.setIpgJobStatus(status);
                syncJobToIpgRelationRepository.saveAndFlush(syncJobToIpgRelation);
            }
        }
        return response;
    }

    @Override
    public HttpResponse querySyncJobToIpg(Long id) throws IOException {
        entityManager.clear();
        JobV3 job = jobRepository.findById(id).orElseThrow(() -> new NotFoundException("job does not exist"));

        SyncJobToIpgRelation oldSyncJobToIpgRelation = syncJobToIpgRelationRepository.findByApnJobId(job.getId());
        if (job.getId() == null || oldSyncJobToIpgRelation == null) {
            log.error("[APN: IpgFillerJobService] query a job synchronized to IpgFiller error, parameter error. This job does not exist or has not been synchronized, job ID: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_SYNCJOBTOIPG_IPGFILLERERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        Long ipgJobId = oldSyncJobToIpgRelation.getIpgJobId();
        return ipgService.querySyncJobToIpg(id, ipgJobId);
    }

    @Override
    public void sendMailToAmOfJob(Long ipgJobId, String subject, String content) {
        SyncJobToIpgRelation syncJobToIpgRelation = syncJobToIpgRelationRepository.findByIpgJobId(ipgJobId);
        if (syncJobToIpgRelation == null) {
            log.error("[APN: IpgFillerJobService] send mail to AM of job error, parameter error. This job does not exist, ipg job ID: {}", ipgJobId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_SENDMAILTOAMOFJOB_SYNCJOBTOIPGRELATIONNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        Long apnJobId = syncJobToIpgRelation.getApnJobId();
        JobV3 job = jobRepository.findById(apnJobId).orElse(null);
        if (job == null) {
            log.error("[APN: IpgFillerJobService] send mail to AM of job error, parameter error. This job does not exist, job ID: {}", apnJobId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_SENDMAILTOAMOFJOB_SYNCJOBTOIPGRELATIONNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        List<Long> companyAMIds = companyService.getAllAmByCompany(job.getCompanyId()).getBody();
        List<UserBriefDTO> userBriefDTOList = userService.findBriefUsers(companyAMIds).getBody();
        List<String> amEmailList = userBriefDTOList.stream().map(UserBriefDTO::getEmail).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(amEmailList)) {
            sendEmailFromIpgUser(amEmailList, subject, content);
        }
    }

    @Override
    public void syncIpgJob(JobDTOV3 jobDTO, JobDTOV3 result) throws IOException {
        if (ObjectUtil.isEmpty(jobDTO.getIpgJobStatus()) || ObjectUtil.isEmpty(jobDTO.getIpgJobType()) ||
                ObjectUtil.isEmpty(jobDTO.getIpgRequirements()) || ObjectUtil.isEmpty(jobDTO.getIpgResponsibilities())) {
            return;
        }
        JobToIpgDTO jobToIpgDTO = JobToIpgDTO.fromJobDTOV3(jobDTO);
        if (jobToIpgDTO.getStatus() == JobStatus.NO_PUBLISHED) {
            jobToIpgDTO.setStatus(jobDTO.getStatus());
        }
        SyncJobToIpgRelation syncJobToIpgRelation = syncJobToIpgRelationRepository.findByApnJobId(jobDTO.getId());
        HttpResponse response = null;
        try {
            if (syncJobToIpgRelation == null) {
                response = syncJobToIpg(jobToIpgDTO);
            } else {
                if (jobToIpgDTO.getStatus().equals(JobStatus.CLOSED)) {
                    response = deleteSyncJobToIpg(jobToIpgDTO.getId(), JobStatus.CLOSED);
                } else {
                    response = updateSyncJobToIpg(jobToIpgDTO);
                }
            }
        } catch (Exception e) {
            log.error("sync job to ipg failed, params error: {}", e.getMessage());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("message", e.getMessage());
            result.setIpgResponse(jsonObject);
        } finally {
            log.info("sync job to ipg jobId: {}", jobDTO.getId());
            if (response != null && (response.getCode() != 200 && response.getCode() != 201 && response.getCode() != 204)) {
                result.setIpgResponse(JSONUtil.parseObj(response));
                log.info("sync job to ipg fail: jobId: {}, ToIpg id: {}", jobDTO.getId(), jobToIpgDTO.getId());
            }

        }
        syncIpgJobDto(result);
    }

    private void sendEmailFromIpgUser(List<String> amEmailList, String subject, String content) {
        MailVM mailVm = new MailVM(applicationProperties.getSupportSender(), amEmailList, null, null, subject, content, null, true);
        try {
            mailService.sendHtmlMail(mailVm);
        } catch (Exception e) {
            log.error("[MailGenerateService] Send apply candidate Email {} Error:{}", mailVm, e);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_SENDEMAILFROMIPGUSER_SENDMAILERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        log.info("[apn] ipg send email success ");
    }


    /**
     * Check whether there is permission to synchronize job to IPG.
     *
     * @param job
     * @return
     */
    private JobV3 hasSyncToIpgPermission(JobV3 job) {
        if (job == null) {
            return null;
        }
        if (SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.TENANT_ADMIN)) {
            return job;
        }
        log.info("sync job, jobId:{}, jobTitle:{}, jobUserId:{}, jobCreatedBy:{}", job.getId(), job.getTitle(), job.getPermissionUserId(), job.getCreatedBy());
        String creator = String.valueOf(job.getPermissionUserId());
        if (ObjectUtil.isEmpty(creator)) {
            String jobCreatedBy = job.getCreatedBy();
            creator = jobCreatedBy.split(",")[0];
        }
        if (String.valueOf(SecurityUtils.getUserId()).equals(creator)) {
            return job;
        }
        List<Long> companyAMIds = companyService.getAllAmByCompany(job.getCompanyId()).getBody();
        if (companyAMIds != null && companyAMIds.contains(SecurityUtils.getUserId())) {
            return job;
        }
        return null;
    }

    /**
     * Get count of each job search category.
     *
     * @return List of JobStatusCountDTO
     */
    @Override
    public List<SearchCategoryCountDTO> getJobSearchCategoryStatistics() {
        Map<Long, JobType> recruitmentProcessIdJobTypeMap = applicationService.getAllMyRecruitmentProcessIds().getBody();
        List<Long> recruitmentProcessIdsExceptPayroll = recruitmentProcessIdJobTypeMap.entrySet().stream()
                .filter(entry -> entry.getValue() != JobType.PAY_ROLL)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        JobCategoryCountRequestDTO requestDto = new JobCategoryCountRequestDTO(SecurityUtils.getUserId(), "jobs_" + SecurityUtils.getTenantId(), recruitmentProcessIdsExceptPayroll);
        applyStatisticsCountPermission(requestDto);
        List<SearchCategoryCountDTO> categoryCountDTOList = new ArrayList<>();
        Map<String, Long> categoryMap = Collections.emptyMap();
        try {
            categoryMap = esFillerJobService.getJobCategoryCount(requestDto);
        } catch (IOException ex) {
            log.error("[APN: JobESService] fetch job Category count IO exception {}", ex);
        } catch (Exception ex) {
            log.error("[APN: JobESService] fetch job Category count exception {}", ex);
        }

        for (Map.Entry<String, Long> entry : categoryMap.entrySet()) {
            SearchCategory category = SearchCategory.fromESKey(entry.getKey());
            SearchCategoryCountDTO dto = new SearchCategoryCountDTO(category.getDbValue(), entry.getValue(), category);
            categoryCountDTOList.add(dto);
        }

        Collections.sort(categoryCountDTOList, Comparator.comparing(SearchCategoryCountDTO::getCategoryId));
        return categoryCountDTOList;
    }

    /**
     * coutn from ES with data permission
     * if the current user has all permission, skip append filters.
     * if the current user has some teams data permission, add filter: affiliations: ["pteam_53", "pteam_65", "all"], all is for public job
     * if the current user has only user limited permission, add filter: userResponsibility5.userId: [1052], 1052 is user ID.
     *
     * @param jobCategoryCountRequestDTO
     */
    private void applyStatisticsCountPermission(JobCategoryCountRequestDTO jobCategoryCountRequestDTO) {
        TeamDataPermissionRespDTO teamDataPermission = cachePermission.getTeamDataPermissionFromCacheOnly(SecurityUtils.getUserId());
        log.info("DataPermission (user: {}) = {}", SecurityUtils.getUserId(), teamDataPermission);
        if (Objects.isNull(teamDataPermission)) {
            teamDataPermission = initiationService.initiateDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        }
        JobCategoryCountRequestDTO.DataPermission dataPermission = new JobCategoryCountRequestDTO.DataPermission();
        dataPermission.setPrivateJobPermission(teamDataPermission.isPrivateJobPermission());
        if (Objects.nonNull(teamDataPermission.getTeamIdForPrivateJob())){
            dataPermission.setAffiliationForPrivateJob("pteam_" + teamDataPermission.getTeamIdForPrivateJob());
        }

        if (CollectionUtils.isNotEmpty(teamDataPermission.getReadableTeamIds())) {
            List<String> readableTeamIds = teamDataPermission.getReadableTeamIds().stream().map(tId -> "pteam_" + tId).collect(Collectors.toList());
            dataPermission.setAffiliations(readableTeamIds);
            //readableTeamIds.add("all"); // for public job

        } else if (BooleanUtils.isTrue(teamDataPermission.getSelf())) {
            dataPermission.setCreatedByOwn(true);
        }
        jobCategoryCountRequestDTO.setDataPermission(dataPermission);
    }

    /****
     * save value of general text from conditionDTO as history after successful search
     * @param condition
     */
    private void saveSearchHistory(SearchConditionDTO condition) {
        try {
            Long userId = SecurityUtils.getUserId();
            List<ConditionParam> searchParams = condition.getSearch() == null ? new ArrayList<ConditionParam>() :
                    condition.getSearch().stream()
                            .flatMap(search -> search.getCondition().stream())
                            .filter(param -> param.getKey().equals(GENERALTEXT))
                            .collect(Collectors.toList());
            List<ConditionParam> filterParams = condition.getFilter() == null ? new ArrayList<ConditionParam>() :
                    condition.getFilter().stream()
                            .flatMap(search -> search.getCondition().stream())
                            .filter(param -> param.getKey().equals(GENERALTEXT))
                            .collect(Collectors.toList());

            List<ConditionParam> params = Stream.concat(searchParams.stream(), filterParams.stream()).collect(Collectors.toList());
            if (params.size() > 0) {

                List<String> searchStrs = new ArrayList<>();
                ObjectMapper mapper = new ObjectMapper();
                for (ConditionParam conditionParam : params) {
                    Object valueObject = conditionParam.getValue();
                    try {
                        Object value = conditionParam.getValue();
                        JSONObject jsonObject = JSONUtil.parseObj(value);
                        Object data = jsonObject.get("data");
                        if (data instanceof String) {
                            String dataStr = (String) data;
                            searchStrs.add(dataStr);
                        } else if (data instanceof List) {
                            List<?> list = (List<?>) data;
                            if (!list.isEmpty() && list.get(0) instanceof String) {
                                searchStrs.addAll((List<String>) list);
                            }
                        }
                    } catch (IllegalArgumentException e) {
                        // Handle error: valueObject cannot be converted into a JsonNode
                        e.printStackTrace();
                        log.error("[APN] SaveSearchHisotry: valueObject cannot be converted into a JsonNode ");
                    }

                }
                CompletableFuture.supplyAsync(() -> {
                    commonRedisService.lPushStringListWithMaxSizeAndExpire(String.format(DATA_KEY_JOB_SEARCH_HISTORY, userId), searchStrs, applicationProperties.getSearchHistoryMaxSize(), applicationProperties.getSearchHistoryExpireTime());
                    return 0;
                });

            }
        } catch (Exception ex) {
            log.error("[APN] Fail to save keyword history by user {}: condition: {}, error: {}", SecurityUtils.getUserId(), condition.toString(), ex);
        }

    }

    @Override
    public List<String> querySearchHistory() {
        List<String> searchHistoryList = commonRedisService.getListData(String.format(DATA_KEY_JOB_SEARCH_HISTORY, SecurityUtils.getUserId()), 0, applicationProperties.getSearchHistoryMaxSize());
        return searchHistoryList;
    }

    @Override
    public void insertSearchHistoryDummy(SearchConditionDTO searchDTO) {
        saveSearchHistory(searchDTO);
    }

    @Override
    public Page<JobActivityDTO> getJobActivities(Long jobId, Pageable pageable) throws Throwable {
        return jobActivityService.getJobActivities(jobId, pageable);
    }

    @Override
    public List<ICompanyTeamUser.CompanyTeamUserDTO> getCompanyTeamUserByJobId(Long jobId) {
        JobCompanyIdAndSalesLeadIdVM companyIdAndSalesLeadIdVM = jobRepository.findCompanyIdByJobIdAndTenantId(jobId, SecurityUtils.getTenantId());
        if (Objects.isNull(companyIdAndSalesLeadIdVM) || Objects.isNull(companyIdAndSalesLeadIdVM.getCompanyId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_GETCOMPANYTEAMUSERBYJOBID_JOBINVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(jobId),jobApiPromptProperties.getJobService()));
        }
        return companyService.getCompanyTeamUsersByCompanyId(companyIdAndSalesLeadIdVM.getCompanyId(), companyIdAndSalesLeadIdVM.getSalesLeadId()).getBody();
    }


    @Override
    public String getJobTitleByJobId(Long jobId) {
        return jobRepository.findJobTitleById(jobId);
    }


    @Override
    public List<RelateJobFolderInfo> searchRelateJobFolders(SearchConditionDTO condition, Pageable pageable, HttpHeaders headers) throws Throwable {
        SearchGroup searchGroup = new SearchGroup();
        ServiceUtils.myCopyProperties(condition, searchGroup);
        //check search area
        SearchConditionDTO.checkPageable(pageable);
        //fix pageInfo
        pageable = PageRequest.of(pageable.getPageNumber() - 1, pageable.getPageSize(), pageable.getSort());
        //format filter
        SearchFilterDTO searchFilterDTO = new SearchFilterDTO();
        List<String> sourceList = ObjectUtil.cloneByStream(ElasticSearchConstants.RELATE_JOB_FOLDER_SEARCH_SOURCE);
        searchFilterDTO.setSource(sourceList);
        searchFilterDTO.setQueryFilter(condition.getFilter());
        JobFolderSearchFilter jobFolderSearchFilter = getJobFolderSearchFilter(condition.getSearch());
        searchFilterDTO.setJobFolderSearchFilter(jobFolderSearchFilter);
        searchGroup.setIndex(Constants.INDEX_JOBS + SecurityUtils.getTenantId());
        searchGroup.setModule(ModuleType.JOB_FOLDER.getName());
        searchGroup.setFilter(searchFilterDTO);
        searchGroup.setTimeZone(condition.getTimezone());
        searchGroup.setLanguage(condition.getLanguage().toDbValue());
        HttpResponse response;
        try {
            response = esFillerJobService.searchRelateJobFolderFromCommonService(searchGroup, pageable);
        } catch (ExternalServiceInterfaceException es) {
            throw es;
        } catch (Exception e) {
            if (Objects.isNull(e.getCause())) {
                throw e;
            }
            throw e.getCause();
        }
        if (ObjectUtil.isEmpty(response) || ObjectUtil.isEmpty(response.getBody())) {
            headers.set("Pagination-Count", String.valueOf(0));
            return new ArrayList<>();
        } else {
            headers.set("Pagination-Count", ElasticSearchUtil.getObjectCountFromResponseHeader(response.getHeaders()) + "");
            return getRelatejobFolderInfoList(response.getBody());
        }
    }

    @Override
    public JobDTOV3 findOneBrief(Long jobId) {
        JobV3 job;
        try {
            job = hasVisiblePermission(jobRepository.findById(jobId).orElse(null));
        } catch (EntityNotFoundException e) {
            throw new NoPermissionException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_GETJOBWITHOUTENTITY_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        if (job == null) {
            return null;
        }
        JobDTOV3 result = JobDTOV3.fromJob(job);
        result.setIsPrivateJob(jobProjectRepository.existsById(job.getPermissionTeamId()));
        RecruitmentProcessVO recruitmentProcessVO = applicationService.getRecruitmentProcessBriefById(job.getRecruitmentProcessId()).getBody();
        if (recruitmentProcessVO != null && ObjectUtil.isNotEmpty(recruitmentProcessVO.getJobType())) {
            result.getRecruitmentProcess().setJobType(recruitmentProcessVO.getJobType());
            result.getRecruitmentProcess().setName(recruitmentProcessVO.getJobType().getName());
            result.getRecruitmentProcess().setStatus(recruitmentProcessVO.getStatus());
        }

        var jobCompanyContactRelationList = jobCompanyContactRelationRepository.findAllByJobId(job.getId());
        if (ObjectUtil.isNotEmpty(jobCompanyContactRelationList)) {
            int last = jobCompanyContactRelationList.size() - 1;
            JobCompanyContactRelation jobCompanyContactRelation = jobCompanyContactRelationList.get(last);
            SalesLeadClientContactDTO salesLeadClientContactDTO = new SalesLeadClientContactDTO();
            salesLeadClientContactDTO.setId(jobCompanyContactRelation.getClientContactId());
            ClientContactBriefInfoDTO contactBriefInfoDTO = companyService.getCompanyClientContactBriefInfo(job.getCompanyId(), salesLeadClientContactDTO.getId()).getBody();
            if (ObjectUtil.isNotEmpty(contactBriefInfoDTO)) {
                salesLeadClientContactDTO.setName(CommonUtils.formatFullName(contactBriefInfoDTO.getFirstName(), contactBriefInfoDTO.getLastName()));
            }
            result.setClientContactCategory(jobCompanyContactRelation.getContactCategory());
            result.setClientContact(salesLeadClientContactDTO);
        }

        return result;
    }

    private JobFolderSearchFilter getJobFolderSearchFilter(List<SearchParam> searchParamList) {
        JobFolderSearchFilter searchFilter = new JobFolderSearchFilter();
        searchFilter.setUserId(String.valueOf(SecurityUtils.getUserId()));
        List<SearchParam> jobFolderSearchParam = new ArrayList<>();
        for (SearchParam searchParam : searchParamList) {
            List<ConditionParam> conditions = searchParam.getCondition();
            List<ConditionParam> jobFolderConditionParam = new ArrayList<>();
            Iterator<ConditionParam> iterator = conditions.iterator();
            while (iterator.hasNext()) {
                ConditionParam conditionParam = iterator.next();
                if ("createdDate".equals(conditionParam.getKey())) {
                    iterator.remove();
                    jobFolderConditionParam.add(conditionParam);
                } else if (ResponsibilityConstants.OWNER.equals(conditionParam.getKey())) {
                    iterator.remove();
                    conditionParam.setKey("responsibility1.id");
                    jobFolderConditionParam.add(conditionParam);
                } else if (ResponsibilityConstants.SHARED_BY.equals(conditionParam.getKey())) {
                    iterator.remove();
                    conditionParam.setKey("responsibility2.id");
                    jobFolderConditionParam.add(conditionParam);
                }
            }
            if (!jobFolderConditionParam.isEmpty()) {
                SearchParam specialSearchParam = new SearchParam();
                specialSearchParam.setRelation(searchParam.getRelation());
                specialSearchParam.setCondition(jobFolderConditionParam);
                jobFolderSearchParam.add(specialSearchParam);
            }
        }
        searchFilter.setSearch(jobFolderSearchParam);
        return searchFilter;
    }

    @Resource
    private EnumCommonService enumCommonService;

    private List<SearchParam> getRelateJobFolderFilter(List<SearchParam> filter) {
        //TODO 先用jobId过滤
        if (filter == null) {
            filter = new ArrayList<>();
        }
        SearchParam belongFolder = new SearchParam();
        belongFolder.setRelation(Relation.OR);
        List<ConditionParam> conditionParams = new ArrayList<>();
        List<TalentAssociationJobFolder> talentAssociationJobFolders = talentRelateJobFolderRepository.getTalentRelateJobFoldersByUserIdIs(SecurityUtils.getUserId());
        List<String> jobIdList = talentAssociationJobFolders.stream().map(TalentAssociationJobFolder::getJobId).collect(Collectors.toSet()).stream().map(String::valueOf).collect(Collectors.toList());
        ConditionParam conditionParam = new ConditionParam();
        conditionParam.setKey("jobId");
        JSONObject value = new JSONObject();
        value.put("data", jobIdList);
        value.put("relation", Relation.OR.name());
        conditionParam.setValue(value);
        conditionParams.add(conditionParam);
        belongFolder.setCondition(conditionParams);
        filter.add(belongFolder);
        return filter;
    }


    private List<RelateJobFolderInfo> getRelatejobFolderInfoList(String body) {
        List<RelateJobFolderInfo> ret = new ArrayList<>();
        JSONArray resultArray = JSONUtil.parseArray(body);
        if (!resultArray.isEmpty()) {
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject searchData = JSONUtil.parseObj(resultArray.get(i));
                String id = searchData.getStr("_id");
                JSONObject source = searchData.getJSONObject("_source");
                JSONArray foldersOfPreSubmitTalents = source.getJSONArray("foldersOfPreSubmitTalents");
                if (foldersOfPreSubmitTalents == null) {
                    continue;
                }
                boolean permission = judgeJobPermission(Long.valueOf(id));
                splitJob2RelateJobFolder(ret, id, source, foldersOfPreSubmitTalents, permission);
            }
        }
        return ret;
    }


    private boolean judgeJobPermission(Long jobId) {
        Optional<JobV3> job = jobRepository.findRegularJobById(jobId, SecurityUtils.getTenantId());
        if (job.isEmpty()) {
            job = jobRepository.findPrivateJobById(jobId, SecurityUtils.getUserId());
        }
        return job.isPresent();

    }

    private void splitJob2RelateJobFolder(List<RelateJobFolderInfo> ret, String id, JSONObject source, JSONArray foldersOfPreSubmitTalents, boolean permission) {
        for (int i = 0; i < foldersOfPreSubmitTalents.size(); i++) {
            JSONObject folder = foldersOfPreSubmitTalents.getJSONObject(i);
            RelateJobFolderInfo relateJobFolderInfo = new RelateJobFolderInfo();
            relateJobFolderInfo.setId(folder.getStr("folderId"));
            Long jobId = Long.valueOf(id);
            relateJobFolderInfo.setJobId(jobId);
            relateJobFolderInfo.setTitle(source.getStr("title"));
            if (permission) {
                relateJobFolderInfo.setNumberOfNotes(source.getInt("numberOfNotes"));
                relateJobFolderInfo.setCompanyName(source.getStr("companyName"));
                relateJobFolderInfo.setStatus(source.getStr("status"));
                relateJobFolderInfo.setPriority(source.getInt("priority"));
            }
            relateJobFolderInfo.setCreatedDate(folder.getStr("createdDate"));
            setOwnerAndShare(relateJobFolderInfo, folder, jobId, permission);
            if (checkOwnerAndShareHaveSelf(relateJobFolderInfo)) {
                ret.add(relateJobFolderInfo);
            }
        }
    }

    private boolean checkOwnerAndShareHaveSelf(RelateJobFolderInfo relateJobFolderInfo) {
        List<SimpleUserInfoDTO> owner = relateJobFolderInfo.getOwner();
        boolean exist = false;
        if (owner != null) {
            exist |= owner.stream().anyMatch(p -> p.getId().equals(String.valueOf(SecurityUtils.getUserId())));
        }
        List<SimpleUserInfoDTO> share = relateJobFolderInfo.getShare();
        if (share != null) {
            exist |= share.stream().anyMatch(p -> p.getId().equals(String.valueOf(SecurityUtils.getUserId())));
        }
        return exist;
    }

    @Resource
    private TalentRelateJobFolderRepository talentRelateJobFolderRepository;

    private void setOwnerAndShare(RelateJobFolderInfo relateJobFolderInfo, JSONObject folder, Long jobId, boolean permission) {
        List<EnumUserResponsibility> enumUserResponsibilityList = enumCommonService.findFoldersOfPreSubmitTalentsEnumUserResponsibility();
        EnumUserResponsibility owner = enumUserResponsibilityList.stream().filter(f -> ResponsibilityConstants.OWNER.equals(f.getLabel())).findFirst().orElse(null);
        EnumUserResponsibility share = enumUserResponsibilityList.stream().filter(f -> ResponsibilityConstants.SHARED_BY.equals(f.getLabel())).findFirst().orElse(null);
        EnumUserResponsibility pending = enumUserResponsibilityList.stream().filter(f -> ResponsibilityConstants.PENDING.equals(f.getLabel())).findFirst().orElse(null);
        if (owner == null || share == null || pending == null) {
            return;
        }
        String userId = String.valueOf(SecurityUtils.getUserId());
        boolean isOwner = false;
        boolean isPending = false;
        JSONArray ownerArray = folder.getJSONArray(getResponsibilityEsKey(owner.getFoldersOfPreSubmitTalentsEsKey()));
        if (ownerArray != null) {
            List<SimpleUserInfoDTO> ownerList = new ArrayList<>();
            for (int i = 0; i < ownerArray.size(); i++) {
                JSONObject record = ownerArray.getJSONObject(i);
                SimpleUserInfoDTO userInfoDTO = new SimpleUserInfoDTO();
                String id = record.getStr("id");
                userInfoDTO.setId(id);
                userInfoDTO.setFullName(record.getStr("namePinYin"));
                if (userId.equals(id)) {
                    isOwner = true;
                }
                ownerList.add(userInfoDTO);
            }
            ownerList.sort(Comparator.comparing(SimpleUserInfoDTO::getFullName));
            relateJobFolderInfo.setOwner(ownerList);
        }

        JSONArray shareArray = folder.getJSONArray(getResponsibilityEsKey(share.getFoldersOfPreSubmitTalentsEsKey()));
        if (shareArray != null) {
            List<SimpleUserInfoDTO> shareList = new ArrayList<>();
            for (int i = 0; i < shareArray.size(); i++) {
                JSONObject record = shareArray.getJSONObject(i);
                SimpleUserInfoDTO userInfoDTO = new SimpleUserInfoDTO();
                userInfoDTO.setId(record.getStr("id"));
                userInfoDTO.setFullName(record.getStr("namePinYin"));
                shareList.add(userInfoDTO);
            }
            shareList.sort(Comparator.comparing(SimpleUserInfoDTO::getFullName));
            relateJobFolderInfo.setShare(shareList);
        }
        JSONArray pendingArray = folder.getJSONArray(getResponsibilityEsKey(pending.getFoldersOfPreSubmitTalentsEsKey()));
        if (pendingArray != null) {
            for (int i = 0; i < pendingArray.size(); i++) {
                JSONObject record = pendingArray.getJSONObject(i);
                if (userId.equals(record.getStr("id"))) {
                    isPending = true;
                }
            }
        }
        TalentAssociationJobFolder exist = talentRelateJobFolderRepository.getTalentRelateJobFolderByJobIdIsAndUserIdIsAndStatusIs(jobId, SecurityUtils.getUserId(), RelateJobFolderStatus.CONFIRM);
        if (!permission) {
            relateJobFolderInfo.setType(RelateJobFolderType.NO_AUTHORITY);
        } else if (exist != null && isPending) {
            relateJobFolderInfo.setType(RelateJobFolderType.PENDING_MERGE);
        } else if (exist == null && isPending) {
            relateJobFolderInfo.setType(RelateJobFolderType.PENDING_RECEIVE);
        } else if (isOwner) {
            relateJobFolderInfo.setType(RelateJobFolderType.FOLDER_OWNER);
        } else {
            relateJobFolderInfo.setType(RelateJobFolderType.NORMAL);
        }
    }

    private String getResponsibilityEsKey(String esKey) {
        if (esKey.contains(".")) {
            return esKey.substring(esKey.lastIndexOf(".") + 1, esKey.length());
        }
        return esKey;
    }

    @Override
    public Integer searchJobsCountByPTeamId(Long teamId) {
        return jobRepository.countByTeamId(teamId);
    }

    @Override
    public List<SalesLeadDTO> searchJobSalesLead(Long companyId, String subcategory) {

        StringBuilder dataSql = new StringBuilder("""
                select t.id,t.business_progress as businessProgress,bp.`name` as businessProgressName,t.name as salesLeadName from account_business t
                left join account_business_service_type_relation tr on  tr.account_business_id = t.id
                left join enum_company_service_type cst on cst.id = tr.service_type_id
                left join enum_business_progress bp on bp.id = t.business_progress
                where  tr.service_type_id=:type and t.company_id =:companyId
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("companyId", companyId);
        JobType jobType = JobType.valueOf(subcategory);
        if (jobType.equals(JobType.CONTRACT)) {
            dataQuery.setParameter("type", 7);
        } else if (jobType.equals(JobType.FULL_TIME) || jobType.equals(JobType.OTHERS)) {
            dataQuery.setParameter("type", 2);
        } else if (jobType.equals(JobType.PAY_ROLL) ) {
            dataQuery.setParameter("type", 20);
        } else if (jobType.equals(JobType.MSP) ) {
            dataQuery.setParameter("type", 11);
        }
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(SalesLeadDTO.class));
        return dataQuery.getResultList();
    }

    @Override
    public List<JobHistoryRecommendDTO> searchHistoryJobRecommend(Long talentId) {

        StringBuilder dataSql = new StringBuilder("""
                         select distinct trp.job_id as jobId,b.title,cast(rp.job_type as char) as jobType,c.full_business_name as companyName from talent_recruitment_process_kpi_user t
                                        left join (select z.* from talent_recruitment_process z,
                        				(select max(id) as m ,job_id from talent_recruitment_process group by job_id) x where z.id = x.m and z.job_id = x.job_id)  trp on trp.id = t.talent_recruitment_process_id
                                        left join job b on b.id  = trp.job_id
                                        left join recruitment_process rp on rp.id = trp.recruitment_process_id
                                        LEFT JOIN company c on b.company_id = c.id
                                        where t.user_role != 4 and t.user_id = :userId and b.STATUS in(0,100) and trp.talent_id !=:talentId
                                                order by trp.id desc limit 200
                        """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("userId", SecurityUtils.getUserId());
        dataQuery.setParameter("talentId", talentId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(JobHistoryRecommendDTO.class));
        return dataQuery.getResultList();
    }

    @Override
    public String getJobLocationsByJobId(Long jobId) {
        return jobLocationRepository.getJobLocationsByJobId(jobId);
    }

    @Override
    public List<JobBriefDTO> getBriefJobsByIds(List<Long> ids) {
        return jobRepository.findBriefJobsByIdIn(ids);
    }


    @Override
    public Set<Long> findPrivateJobIds(List<Long> ids) {
        return jobProjectRepository.findPrivateJobIds(SecurityUtils.getTenantId(), ids);
    }

    @Override
    public List<JobCrossSellDTOV3> getJobInfoByBusinessId(List<Long> businessIdList) {
        List<JobV3> jobList = jobRepository.findAllBySalesLeadIdIn(businessIdList);
        if (null == jobList && jobList.isEmpty()) {
            return new ArrayList<>();
        }
        List<JobCrossSellDTOV3> crossSellDTOV3List = new ArrayList<>();
        jobList.forEach(x -> {
            JobCrossSellDTOV3 bean = new JobCrossSellDTOV3();
            bean.setBusinessId(x.getSalesLeadId());
            bean.setId(x.getId());
            bean.setJobs(x.getOpenings());
            bean.setJobStatus(x.getStatus().name());
            crossSellDTOV3List.add(bean);
        });
        return crossSellDTOV3List;
    }

    @Override
    public List<JobPermissionDTO> checkJobPermissionById(List<Long> jobIds) {
        if (null == jobIds) {
            return new ArrayList<>();
        }
        List<Map<String,Object>> privateJobs = jobRepository.findJobByIds(jobIds);
        List<JobPermissionDTO> result = new ArrayList<>();
        privateJobs.forEach(x -> {
            JobPermissionDTO bean = new JobPermissionDTO();

            String privateJob = x.get("privateJob")+"";
            String companyId = x.get("companyId")+"";
            String id = x.get("id")+"";
            String tenantId = x.get("tenantId")+"";
            String createdBy = x.get("createdBy")+"";

            JobV3 job = new JobV3();
            job.setCompanyId(Long.valueOf(companyId));
            job.setId(Long.valueOf(id));
            job.setTenantId(Long.valueOf(tenantId));
            job.setCreatedBy(createdBy);

            if(StringUtils.isNotBlank(privateJob) && !privateJob.equals("null")){
                if (checkJobPermissionByStart(job, true)) {
                    bean.setHavePermission(true);
                } else {
                    bean.setHavePermission(false);
                }
                bean.setPrivateJob(true);
            }else{
                bean.setPrivateJob(false);
                JobV3 v3 = hasVisiblePermission(job);
                if (v3 != null) {
                    bean.setHavePermission(true);
                } else {
                    bean.setHavePermission(false);
                }
            }
            bean.setId(Long.valueOf(id));
            result.add(bean);
        });
        return result;
    }

    @Override
    public JobPermissionDTO checkJobPermissionByChinaInvoicing(Long jobId) {
        JobPermissionDTO dto = new JobPermissionDTO();
        if (CollectionUtils.isNotEmpty(jobRepository.findPrivateJobId(jobId))) {
            dto.setPrivateJob(true);
            Optional<JobV3> job = jobRepository.findPrivateJobById(jobId, SecurityUtils.getUserId());
            if (job.isPresent()) {
                if (checkJobPermissionByStart(job.get(), true)) {
                    dto.setHavePermission(true);
                } else {
                    dto.setHavePermission(false);
                }
            } else {
                dto.setHavePermission(false);
            }
        } else {
            dto.setPrivateJob(false);
            JobV3 job = jobRepository.findRegularJobById(jobId, SecurityUtils.getTenantId()).orElse(null);
            if (job != null) {
                if (checkJobPermissionByStart(job, false)) {
                    dto.setHavePermission(true);
                } else {
                    dto.setHavePermission(false);
                }
            } else {
                dto.setHavePermission(false);
            }
        }
        return dto;
    }

    /**
     * 中国区开票job 权限验证
     * 普通职位编辑权限：租户admin，公司AM，职位创建者和岗位负责人可编辑；
     * 私有职位编辑权限：职位创建者和岗位负责人可编辑；
     * @param job
     * @param privateJob
     * @return
     */
    private boolean checkJobPermissionByStart(JobV3 job,Boolean privateJob){
        if (!ObjectUtils.equals(job.getTenantId(), SecurityUtils.getTenantId())) {
            return false;
        }
        boolean permitted = false;
        if (!privateJob) {
            if (SecurityUtils.isAdmin()) {
                permitted = true;
            }
            List<Long> companyAMIds = companyService.getAllAmByCompany(job.getCompanyId()).getBody();
            if (companyAMIds.contains(SecurityUtils.getUserId())) {
                permitted = true;
            }
        }
        if (SecurityUtils.getUserId().equals(SecurityUtils.getUserIdFromCreatedBy(job.getCreatedBy()))) {
            permitted = true;
        }
        //assignedUser is allowed update the job
        Boolean isAssignedUser = jobRelationService.isAssignedUsers(job.getId(), SecurityUtils.getUserId());
        if (isAssignedUser) {
            permitted = true;
        }

        return permitted;
    }

    public JobDTOV3 translate2JobDTOV3(String request) {
        JobDTOV3 jobDTOV3 = new JobDTOV3();
        ObjectMapper mapper = ObjectMapperUtils.getInstance();
        try {
            JsonNode jsonObject = mapper.readTree(request);
            ObjectNode extendedInfo = (ObjectNode) jsonObject.deepCopy();

            // 处理基本字段
//            if (isValidField(jsonObject, "id")) { //path variable
//                jobDTOV3.setId(jsonObject.get("id").asLong());
//                extendedInfo.remove("id");
//            }
//            if (isValidField(jsonObject, "tenantId")) { //no
//                jobDTOV3.setTenantId(jsonObject.get("tenantId").asLong());
//                extendedInfo.remove("tenantId");
//            }
            if (isValidField(jsonObject, "company")) {
                CompanyBriefDTO companyBriefDTO = mapper.convertValue(jsonObject.get("company"), CompanyBriefDTO.class);
                jobDTOV3.setCompany(companyBriefDTO);
                extendedInfo.remove("company");
            }
            if (isValidField(jsonObject, "title")) {
                jobDTOV3.setTitle(jsonObject.get("title").asText());
                extendedInfo.remove("title");
            }
            if (isValidField(jsonObject, "code")) {
                jobDTOV3.setCode(jsonObject.get("code").asText());
                extendedInfo.remove("code");
            }
            if (isValidField(jsonObject, "clientContact")) {
                SalesLeadClientContactDTO clientContact = mapper.convertValue(jsonObject.get("clientContact"), SalesLeadClientContactDTO.class);
                jobDTOV3.setClientContact(clientContact);
                extendedInfo.remove("clientContact");
            }
            if (isValidField(jsonObject, "clientContactCategory")) {
                jobDTOV3.setClientContactCategory(jsonObject.get("clientContactCategory").asInt());
                extendedInfo.remove("clientContactCategory");
            }
            if (isValidField(jsonObject, "clientContactEmails")) { //TODO: front end no input
                jobDTOV3.setClientContactEmails(jsonObject.get("clientContactEmails").asText());
                extendedInfo.remove("clientContactEmails");
            }
            if (isValidField(jsonObject, "startDate")) {
                jobDTOV3.setStartDate(mapper.convertValue(jsonObject.get("startDate"), Instant.class));
                extendedInfo.remove("startDate");
            }
            if (isValidField(jsonObject, "endDate")) {
                jobDTOV3.setEndDate(mapper.convertValue(jsonObject.get("endDate"), Instant.class));
                extendedInfo.remove("endDate");
            }

            if (isValidField(jsonObject, "postingTime")) { //TODO: front end no input
                jobDTOV3.setPostingTime(mapper.convertValue(jsonObject.get("postingTime"), Instant.class));
                extendedInfo.remove("postingTime");
            }
            if (isValidField(jsonObject, "openTime")) { //TODO: front end no input
                jobDTOV3.setOpenTime(mapper.convertValue(jsonObject.get("openTime"), Instant.class));
                extendedInfo.remove("openTime");
            }
            if (isValidField(jsonObject, "tenantWebsitePostingTime")) { //TODO: front end no input
                jobDTOV3.setTenantWebsitePostingTime(mapper.convertValue(jsonObject.get("tenantWebsitePostingTime"), Instant.class));
                extendedInfo.remove("tenantWebsitePostingTime");
            }

            if (isValidField(jsonObject, "status")) {
                jobDTOV3.setStatus(JobStatus.valueOf(jsonObject.get("status").asText()));
                extendedInfo.remove("status");
            }
            if (isValidField(jsonObject, "locations")) {
                List<LocationDTO> locations = mapper.convertValue(jsonObject.get("locations"),
                        new TypeReference<List<LocationDTO>>() {});
                jobDTOV3.setLocations(locations);
                extendedInfo.remove("locations");
            }
            if (isValidField(jsonObject, "jobFunctions")) { //TODO: double check
                List<EnumRelationDTO> jobFunctions = mapper.convertValue(jsonObject.get("jobFunctions"),
                        new TypeReference<List<EnumRelationDTO>>() {});
                jobDTOV3.setJobFunctions(jobFunctions);
                extendedInfo.remove("jobFunctions");
            }
            if (isValidField(jsonObject, "assignedUsers")) {
                List<AssignedUserDTO> assignedUsers = mapper.convertValue(jsonObject.get("assignedUsers"),
                        new TypeReference<List<AssignedUserDTO>>() {});
                jobDTOV3.setAssignedUsers(assignedUsers);
                extendedInfo.remove("assignedUsers");
            }
            if (isValidField(jsonObject, "accountManagers")) { //TODO: front end no input
                List<AssignedUserDTO> accountManagers = mapper.convertValue(jsonObject.get("accountManagers"),
                        new TypeReference<List<AssignedUserDTO>>() {});
                jobDTOV3.setAccountManagers(accountManagers);
                extendedInfo.remove("accountManagers");
            }

            if (isValidField(jsonObject, "requiredLanguages")) { //TODO: double check
                List<EnumRelationDTO> requiredLanguages = mapper.convertValue(jsonObject.get("requiredLanguages"),
                        new TypeReference<List<EnumRelationDTO>>() {});
                jobDTOV3.setRequiredLanguages(requiredLanguages);
                extendedInfo.remove("requiredLanguages");
            }
            if (isValidField(jsonObject, "preferredLanguages")) { //TODO: double check
                List<EnumRelationDTO> preferredLanguages = mapper.convertValue(jsonObject.get("preferredLanguages"),
                        new TypeReference<List<EnumRelationDTO>>() {});
                jobDTOV3.setPreferredLanguages(preferredLanguages);
                extendedInfo.remove("preferredLanguages");
            }

            if (isValidField(jsonObject, "isPrivateJob")) { //TODO: front end no input
                jobDTOV3.setIsPrivateJob(jsonObject.get("isPrivateJob").asBoolean());
                extendedInfo.remove("isPrivateJob");
            }
            if (isValidField(jsonObject, "favorite")) { //TODO: front end no input
                jobDTOV3.setFavorite(jsonObject.get("favorite").asBoolean());
                extendedInfo.remove("favorite");
            }

            if (isValidField(jsonObject, "currency")) { //TODO: double check
                jobDTOV3.setCurrency(mapper.convertValue(jsonObject.get("currency"), EnumRelationDTO.class));
                extendedInfo.remove("currency");
            }
            if (isValidField(jsonObject, "openings")) {
                jobDTOV3.setOpenings(jsonObject.get("openings").asInt());
                extendedInfo.remove("openings");
            }
            if (isValidField(jsonObject, "maxSubmissions")) {
                jobDTOV3.setMaxSubmissions(jsonObject.get("maxSubmissions").asInt());
                extendedInfo.remove("maxSubmissions");
            }

            if (isValidField(jsonObject, "boolstr")) { //TODO: double check
                List<JobBoolStringDTO> boolstr = mapper.convertValue(jsonObject.get("preferredLanguages"),
                        new TypeReference<List<JobBoolStringDTO>>() {});
                jobDTOV3.setBoolstr(boolstr);
                extendedInfo.remove("boolstr");
            }

            if (isValidField(jsonObject, "preferredDegrees")) { //TODO: front end no input
                List<EnumRelationDTO> preferredDegrees = mapper.convertValue(jsonObject.get("preferredDegrees"),
                        new TypeReference<List<EnumRelationDTO>>() {});
                jobDTOV3.setPreferredDegrees(preferredDegrees);
                extendedInfo.remove("preferredDegrees");
            }

            if (isValidField(jsonObject, "minimumDegreeLevel")) { //TODO: double check
                jobDTOV3.setMinimumDegreeLevel(mapper.convertValue(jsonObject.get("minimumDegreeLevel"), EnumRelationDTO.class));
                extendedInfo.remove("minimumDegreeLevel");
            }

            if (isValidField(jsonObject, "notes")) { // TODO: front end no input
                List<JobNoteDTO> notes = mapper.convertValue(jsonObject.get("notes"),
                        new TypeReference<List<JobNoteDTO>>() {});
                jobDTOV3.setNotes(notes);
                extendedInfo.remove("notes");
            }
            if (isValidField(jsonObject, "ipgJobStatus")) { // TODO: front end no input
                jobDTOV3.setIpgJobStatus(mapper.convertValue(jsonObject.get("ipgJobStatus"), JobStatus.class));
                extendedInfo.remove("ipgJobStatus");
            } else {
                jobDTOV3.setIpgJobStatus(JobStatus.NO_PUBLISHED);
            }
            if (isValidField(jsonObject, "ipgJobType")) { // TODO: front end no input
                jobDTOV3.setIpgJobType(mapper.convertValue(jsonObject.get("ipgJobType"), JobType.class));
                extendedInfo.remove("ipgJobType");
            }
            if (isValidField(jsonObject, "ipgJobDescription")) { // TODO: front end no input
                jobDTOV3.setIpgJobDescription(jsonObject.get("ipgJobDescription").asText());
                extendedInfo.remove("ipgJobDescription");
            }
            if (isValidField(jsonObject, "operationType")) { // TODO: front end no input
                jobDTOV3.setOperationType(mapper.convertValue(jsonObject.get("operationType"), OperationType.class));
                extendedInfo.remove("operationType");
            }

            if (isValidField(jsonObject, "recruitmentProcess")) {
                RecruitmentProcessBriefDTO recruitmentProcessDTO = mapper.convertValue(jsonObject.get("recruitmentProcess"), RecruitmentProcessBriefDTO.class);
                jobDTOV3.setRecruitmentProcess(recruitmentProcessDTO);
                extendedInfo.remove("recruitmentProcess");
            }

            if (isValidField(jsonObject, "priority")) { //TODO: double check
                jobDTOV3.setPriority(mapper.convertValue(jsonObject.get("priority"), EnumRelationDTO.class));
                extendedInfo.remove("priority");
            }

            if (isValidField(jsonObject, "rawJdFile")) { //TODO: double check
                jobDTOV3.setRawJdFile(mapper.convertValue(jsonObject.get("rawJdFile"), RawJdFileDTO.class));
                extendedInfo.remove("rawJdFile");
            }

            if (isValidField(jsonObject, "flexibleLocation")) {
                jobDTOV3.setFlexibleLocation(jsonObject.get("flexibleLocation").asBoolean());
                extendedInfo.remove("flexibleLocation");
            }

            if (isValidField(jsonObject, "ipgResponse")) { // TODO: front end no input
                JsonNode ipgNode = jsonObject.get("ipgResponse");
                JSONObject ipgResponse = new JSONObject(ipgNode.toString());
                jobDTOV3.setIpgResponse(ipgResponse);
                extendedInfo.remove("ipgResponse");
            }
            if (isValidField(jsonObject, "ipgRequirements")) {
                jobDTOV3.setIpgRequirements(jsonObject.get("ipgRequirements").asText());
                extendedInfo.remove("ipgRequirements");
            }
            if (isValidField(jsonObject, "ipgResponsibilities")) {
                jobDTOV3.setIpgResponsibilities(jsonObject.get("ipgResponsibilities").asText());
                extendedInfo.remove("ipgResponsibilities");
            }
            if (isValidField(jsonObject, "ipgSummary")) {
                jobDTOV3.setIpgSummary(jsonObject.get("ipgSummary").asText());
                extendedInfo.remove("ipgSummary");
            }
            if (isValidField(jsonObject, "ipgLocations")) { // TODO: front end no input
                List<LocationDTO> ipgLocations = mapper.convertValue(jsonObject.get("ipgLocations"),
                        new TypeReference<List<LocationDTO>>() {});
                jobDTOV3.setIpgLocations(ipgLocations);
                extendedInfo.remove("ipgLocations");
            }
            if (isValidField(jsonObject, "ipgFlexibleLocation")) { // TODO: front end no input
                jobDTOV3.setIpgFlexibleLocation(jsonObject.get("ipgFlexibleLocation").asBoolean());
                extendedInfo.remove("ipgFlexibleLocation");
            }
            if (isValidField(jsonObject, "ipgJobSalaryDTO")) { // TODO: front end no input
                jobDTOV3.setIpgJobSalaryDTO(mapper.convertValue(jsonObject.get("ipgJobSalaryDTO"), IpgJobSalaryDTO.class));
                extendedInfo.remove("ipgJobSalaryDTO");
            }
            if (isValidField(jsonObject, "ipgIncludeDefaultContent")) { // TODO: front end no input
                jobDTOV3.setIpgIncludeDefaultContent(jsonObject.get("ipgIncludeDefaultContent").asBoolean());
                extendedInfo.remove("ipgIncludeDefaultContent");
            }
            if (isValidField(jsonObject, "ipgDefaultContent")) { // TODO: front end no input
                jobDTOV3.setIpgDefaultContent(jsonObject.get("ipgDefaultContent").asText());
                extendedInfo.remove("ipgDefaultContent");
            }
            if (isValidField(jsonObject, "defaultCommission")) { // TODO: front end no input
                JsonNode node = jsonObject.get("defaultCommission");
                if (node != null && node.isNumber()) {
                    jobDTOV3.setDefaultCommission(node.floatValue());
                }
                extendedInfo.remove("defaultCommission");
            }


            if (isValidField(jsonObject, "salesLeadId")) {
                jobDTOV3.setSalesLeadId(jsonObject.get("salesLeadId").asLong());
                extendedInfo.remove("salesLeadId");
            }
            if (isValidField(jsonObject, "salesLeadName")) { // TODO: front end no input
                jobDTOV3.setSalesLeadName(jsonObject.get("salesLeadName").asText());
                extendedInfo.remove("salesLeadName");
            }
            if (isValidField(jsonObject, "cooperationStatus")) {
                jobDTOV3.setCooperationStatus(jsonObject.get("cooperationStatus").asText());
                extendedInfo.remove("cooperationStatus");
            }
            if (isValidField(jsonObject, "contractDuration")) {
                jobDTOV3.setContractDuration(jsonObject.get("contractDuration").asInt());
                extendedInfo.remove("contractDuration");
            }



            //AdditionalInfo begin
            if (isValidField(jsonObject, "requiredSkills")) {
                List<SkillDTO> requiredSkills = mapper.convertValue(jsonObject.get("requiredSkills"),
                        new TypeReference<List<SkillDTO>>() {});
                jobDTOV3.setRequiredSkills(requiredSkills);
            }
            if (isValidField(jsonObject, "preferredSkills")) {
                List<SkillDTO> preferredSkills = mapper.convertValue(jsonObject.get("preferredSkills"),
                        new TypeReference<List<SkillDTO>>() {});
                jobDTOV3.setPreferredSkills(preferredSkills);
            }
            if (isValidField(jsonObject, "experienceYearRange")) {
                jobDTOV3.setExperienceYearRange(mapper.convertValue(jsonObject.get("experienceYearRange"), RangeDTO.class));
            }
            if (isValidField(jsonObject, "billRange")) {
                jobDTOV3.setBillRange(mapper.convertValue(jsonObject.get("billRange"), RangeDTO.class));
            }
            if (isValidField(jsonObject, "salaryRange")) {
                jobDTOV3.setBillRange(mapper.convertValue(jsonObject.get("salaryRange"), RangeDTO.class));
            }
            if (isValidField(jsonObject, "payType")) {
                jobDTOV3.setPayType(mapper.convertValue(jsonObject.get("payType"), RateUnitType.class));
            }
            if (isValidField(jsonObject, "department")) {
                jobDTOV3.setDepartment(jsonObject.get("department").asText());
            }

            if (isValidField(jsonObject, "logo")) { //TODO: front end no input
                jobDTOV3.setLogo(jsonObject.get("logo").asText());
            }

            if (isValidField(jsonObject, "requirements")) {
                jobDTOV3.setRequirements(jsonObject.get("requirements").asText());
            }
            if (isValidField(jsonObject, "summary")) {
                jobDTOV3.setSummary(jsonObject.get("summary").asText());
            }
            if (isValidField(jsonObject, "responsibilities")) {
                jobDTOV3.setResponsibilities(jsonObject.get("responsibilities").asText());
            }


            if (isValidField(jsonObject, "reasonForRecruitment")) {
                jobDTOV3.setReasonForRecruitment(jsonObject.get("reasonForRecruitment").asText());
                extendedInfo.remove("reasonForRecruitment"); //local extended info
            }
            if (isValidField(jsonObject, "teamComposition")) {
                jobDTOV3.setTeamComposition(jsonObject.get("teamComposition").asText());
                extendedInfo.remove("teamComposition"); //local extended info
            }
            if (isValidField(jsonObject, "preferredCompanies")) {
                jobDTOV3.setPreferredCompanies(jsonObject.get("preferredCompanies").asText());
                extendedInfo.remove("preferredCompanies"); //local extended info
            }
            if (isValidField(jsonObject, "suggestionsForProspecting")) {
                jobDTOV3.setSuggestionsForProspecting(jsonObject.get("suggestionsForProspecting").asText());
                extendedInfo.remove("suggestionsForProspecting"); //local extended info
            }
            if (isValidField(jsonObject, "recommendedApproach")) {
                jobDTOV3.setRecommendedApproach(jsonObject.get("recommendedApproach").asText());
                extendedInfo.remove("recommendedApproach"); //local extended info
            }
            if (isValidField(jsonObject, "estimatedJobFee")) {
                jobDTOV3.setEstimatedJobFee(mapper.convertValue(jsonObject.get("estimatedJobFee"), FeeDTO.class));
                extendedInfo.remove("estimatedJobFee"); //local extended info
            }
            if (isValidField(jsonObject, "feeStructure")) {
                jobDTOV3.setFeeStructure(jsonObject.get("feeStructure").asText());
                extendedInfo.remove("feeStructure"); //local extended info
            }
            if (isValidField(jsonObject, "contractSigningParty")) {
                jobDTOV3.setContractSigningParty(jsonObject.get("contractSigningParty").asText());
                extendedInfo.remove("contractSigningParty"); //local extended info
            }
            if (isValidField(jsonObject, "preferredIndustry")) {
                jobDTOV3.setPreferredIndustry(jsonObject.get("preferredIndustry").asLong());
                extendedInfo.remove("preferredIndustry"); //local extended info
            }


            // 移除前端专用字段 possible
            List<String> frontendFields = Arrays.asList(
                    "id", "tenantId",
                    "createdUser", "lastModifiedUser", "createdBy", "createdDate", "lastModifiedBy", "lastModifiedDate",
                    "agencyId"
            );
            frontendFields.forEach(extendedInfo::remove);

            // 设置扩展信息
            if (!extendedInfo.isEmpty()) {
                String jsonString = JSON.toJSONString(JSONUtil.parseObj(extendedInfo.toString()), (PropertyFilter) (o, s, o1) -> ObjectUtil.isNotEmpty(o1));
                jobDTOV3.setExtendedInfo(jsonString);
            }
        } catch (JsonProcessingException e) {
            throw new CustomParameterizedException("Talent input request param error, input:{}", request);
        }

        return jobDTOV3;
    }

    @Value("${job_search_condition_generator_host_type:QDRANT_HOST}")
    private String configProperty;

    private static final String PHONE_RECORDING_CHECK_POST = "/search_condition/v1/%s/%s?refresh=%s&host_type=%s";

    @Override
    public String getSearchCondition(ExtractKeywordsDTO dto) throws IOException {
        // 设置超时时间为5分钟
        int timeoutMillis = 5 * 60 * 1000;

        try {
            String url = jobSearchConditionGeneratorUrl + String.format(
                    PHONE_RECORDING_CHECK_POST,
                    SecurityUtils.getTenantId(),
                    dto.getJobId(),
                    dto.getRefresh(),
                    configProperty
            );

            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(timeoutMillis, TimeUnit.MILLISECONDS)
                    .readTimeout(timeoutMillis, TimeUnit.MILLISECONDS)
                    .writeTimeout(timeoutMillis, TimeUnit.MILLISECONDS)
                    .build();

            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

            try (Response response = client.newCall(request).execute()) {
                return response.body().string();
            }
        } catch (Exception e) {
            System.err.println("请求发生异常: " + e.getMessage());
            throw e;
        }
    }

    @Override
    public List<Long> findPrivateJobByIds(FindPrivateJobByIdsDTO dto) {
        return jobRepository.findPrivateJobIds(dto.getJobIds()).stream().toList();
    }

    /**
     * 检查JsonNode中的字段是否有效（非null且非空字符串）
     *
     * @param node JsonNode对象
     * @param fieldName 字段名
     * @return 字段是否有效
     */
    private static boolean isValidField(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull()
                && !(fieldNode.isTextual() && fieldNode.asText().trim().isEmpty());
    }
}
